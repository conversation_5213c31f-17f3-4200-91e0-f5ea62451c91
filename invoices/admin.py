from django.contrib import admin

from .models.invoice import Invoice
from .models.invoice_item import InvoiceItem


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1
    fields = ("product", "quantity", "unit_price", "total_price", "notes")
    readonly_fields = ("total_price",)
    autocomplete_fields = ("product",)


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "warehouse",
        "net_amount",
        "payment_method",
        "created",
    )
    list_filter = ("payment_method", "created", "warehouse")
    search_fields = ("notes",)
    readonly_fields = ("created", "modified", "net_amount", "profit")
    inlines = [InvoiceItemInline]
    autocomplete_fields = ("warehouse", "pos_session")
    date_hierarchy = "created"
    # TODO add the same process of checking items product cost not less than product cost
    #  and calculate total amount and total profit
