# Generated by Django 5.2.1 on 2025-06-28 09:49

import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("pos", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Employee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("address", models.TextField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("full_time", "Full Time"),
                            ("part_time", "Part Time"),
                            ("daily", "Daily Worker"),
                        ],
                        default="daily",
                        max_length=10,
                    ),
                ),
                ("hour_rate", models.DecimalField(decimal_places=2, max_digits=10)),
                ("day_rate", models.DecimalField(decimal_places=2, max_digits=10)),
                ("hire_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("terminated", "Terminated"),
                        ],
                        default="active",
                        max_length=10,
                    ),
                ),
                ("identification", models.ImageField(upload_to="identification/")),
                (
                    "pos",
                    models.ForeignKey(
                        blank=True,
                        help_text="The POS terminal this employee works at",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="pos.pos",
                        verbose_name="POS",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Employee",
                "verbose_name_plural": "Employees",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="Attendance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("time_in", models.DateTimeField(verbose_name="Time In")),
                (
                    "time_out",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Time Out"
                    ),
                ),
                (
                    "total_hours",
                    models.FloatField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0.0)],
                        verbose_name="Total Hours",
                    ),
                ),
                (
                    "time_in_image",
                    models.ImageField(
                        blank=True,
                        max_length=500,
                        null=True,
                        upload_to="",
                        verbose_name="Image",
                    ),
                ),
                (
                    "time_out_image",
                    models.ImageField(
                        blank=True,
                        max_length=500,
                        null=True,
                        upload_to="",
                        verbose_name="Image",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendances",
                        to="employees.employee",
                        verbose_name="Employee",
                    ),
                ),
            ],
            options={
                "verbose_name": "Attendance",
                "verbose_name_plural": "Attendances",
                "ordering": ["-time_in"],
            },
        ),
        migrations.CreateModel(
            name="Salary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "salary",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0.0)],
                        verbose_name="Salary",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "attendance",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="salary",
                        to="employees.attendance",
                        verbose_name="Attendance",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="salaries",
                        to="employees.employee",
                        verbose_name="Employee",
                    ),
                ),
            ],
            options={
                "verbose_name": "Salary",
                "verbose_name_plural": "Salaries",
                "ordering": ["-created"],
            },
        ),
    ]
