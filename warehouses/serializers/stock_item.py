from rest_framework import serializers

from ..models import StockItem


class StockItemSerializer(serializers.ModelSerializer):
    """
    Serializer for the StockItem model.
    Handles serialization and deserialization of StockItem instances.
    """

    warehouse_name = serializers.CharField(source="warehouse.name", read_only=True)
    product_name = serializers.CharField(source="product.name", read_only=True)
    unit_type = serializers.CharField(source="product.unit_type", read_only=True)

    class Meta:
        model = StockItem
        fields = [
            "id",
            "warehouse",
            "warehouse_name",
            "product",
            "product_name",
            "quantity",
            "min_stock",
            "unit_type",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
            "warehouse_name",
            "product_name",
            "unit_type",
        ]

    def validate_quantity(self, value):
        """Ensure quantity is not negative."""
        if value < 0:
            raise serializers.ValidationError("Quantity cannot be negative.")
        return value

    def validate_min_stock(self, value):
        """Ensure min_stock is not negative."""
        if value < 0:
            raise serializers.ValidationError("Minimum stock cannot be negative.")
        return value
