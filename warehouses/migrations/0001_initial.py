# Generated by Django 5.2.1 on 2025-06-15 11:32

import django.db.models.deletion
import django_extensions.db.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("products", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Warehouse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the warehouse",
                        max_length=100,
                        verbose_name="name",
                    ),
                ),
                (
                    "location",
                    models.<PERSON>r<PERSON>ield(
                        help_text="Physical location/address of the warehouse",
                        max_length=255,
                        verbose_name="location",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Additional information about the warehouse",
                        null=True,
                        verbose_name="description",
                    ),
                ),
            ],
            options={
                "verbose_name": "warehouse",
                "verbose_name_plural": "warehouses",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="InventoryCount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        help_text="Current status of the inventory count",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the inventory count was started",
                        null=True,
                        verbose_name="started at",
                    ),
                ),
                (
                    "ended_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the inventory count was completed or cancelled",
                        null=True,
                        verbose_name="ended at",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes or comments about the inventory count",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "noted_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created or is responsible for this inventory count",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="inventory_counts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="noted by",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse where the inventory count is being performed",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inventory_counts",
                        to="warehouses.warehouse",
                        verbose_name="warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "inventory count",
                "verbose_name_plural": "inventory counts",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="StockItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        help_text="Current quantity in stock",
                        max_digits=15,
                        verbose_name="quantity",
                    ),
                ),
                (
                    "min_stock",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        help_text="Minimum required quantity before restocking is needed",
                        max_digits=15,
                        verbose_name="minimum stock",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        help_text="The product this stock item refers to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock_items",
                        to="products.product",
                        verbose_name="product",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse where this stock item is located",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock_items",
                        to="warehouses.warehouse",
                        verbose_name="warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "stock item",
                "verbose_name_plural": "stock items",
                "ordering": ["warehouse", "product"],
                "unique_together": {("warehouse", "product")},
            },
        ),
        migrations.CreateModel(
            name="InventoryCountItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "recorded_quantity",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="The quantity physically counted during inventory",
                        max_digits=15,
                        verbose_name="recorded quantity",
                    ),
                ),
                (
                    "system_quantity",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="The quantity in the system before the count",
                        max_digits=15,
                        verbose_name="system quantity",
                    ),
                ),
                (
                    "difference",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="The difference between recorded and system quantity (recorded - system)",
                        max_digits=15,
                        verbose_name="difference",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this count item",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "inventory_count",
                    models.ForeignKey(
                        help_text="The inventory count this item belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="warehouses.inventorycount",
                        verbose_name="inventory count",
                    ),
                ),
                (
                    "stock_item",
                    models.ForeignKey(
                        help_text="The stock item being counted",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inventory_count_items",
                        to="warehouses.stockitem",
                        verbose_name="stock item",
                    ),
                ),
            ],
            options={
                "verbose_name": "inventory count item",
                "verbose_name_plural": "inventory count items",
                "ordering": ["inventory_count", "stock_item"],
                "unique_together": {("inventory_count", "stock_item")},
            },
        ),
    ]
