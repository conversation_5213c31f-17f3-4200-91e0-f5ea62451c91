import React, { useState } from 'react';
import { Container, Row, Col, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useProducts } from '../../hooks/useProducts';
import ProductForm from './ProductForm';

const ProductCreate = () => {
    const navigate = useNavigate();
    const { createProduct } = useProducts();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    const handleSubmit = async (productData) => {
        try {
            setLoading(true);
            setError(null);
            
            await createProduct(productData);
            
            setSuccess(true);
            
            // Redirect to products list after a short delay
            setTimeout(() => {
                navigate('/products');
            }, 1500);
            
        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;
                
                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field]) 
                            ? errorData[field] 
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setError(errorMessages.join(', '));
                } else {
                    setError(errorData.detail || errorData.message || 'Failed to create product');
                }
            } else {
                setError('Failed to create product. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    if (success) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="success" className="text-center">
                            <h4>Product Created Successfully!</h4>
                            <p>Redirecting to products list...</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Product</h2>
                    <p className="text-muted">
                        Add a new product to your inventory. Fill in the required information below.
                    </p>
                </Col>
            </Row>

            <Row>
                <Col>
                    <ProductForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default ProductCreate;
