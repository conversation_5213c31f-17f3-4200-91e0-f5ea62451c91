from decimal import Decimal

from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from products.models import Product
from purchases_return.models.purchase_return import PurchaseReturn


class PurchaseReturnItem(TimeStampedModel):
    """
    Represents an item within a purchase return.
    Each return item is associated with a return and product, and includes
    details such as quantity, unit cost, and total cost.
    """

    purchase_return = models.ForeignKey(
        PurchaseReturn,
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name=_("purchase return"),
        help_text=_("The purchase return this item belongs to"),
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name="purchase_return_items",
        verbose_name=_("product"),
        help_text=_("The product being returned"),
    )

    quantity = models.DecimalField(
        _("quantity"),
        max_digits=10,
        decimal_places=3,
        help_text=_("Quantity of the product being returned"),
        validators=[MinValueValidator(Decimal('0.001'))],
    )

    unit_cost = models.DecimalField(
        _("unit cost"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Cost per unit of the returned product"),
        validators=[MinValueValidator(Decimal("0.01"))],
    )

    total_cost = models.DecimalField(
        _("total cost"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Total cost (quantity × unit_cost)"),
        validators=[MinValueValidator(Decimal("0.01"))],
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        help_text=_("Additional notes about this return item"),
    )

    class Meta:
        verbose_name = _("purchase return item")
        verbose_name_plural = _("purchase return items")
        ordering = ["-created"]

    def __str__(self):
        return f"{self.quantity} x {self.product.name} - ${self.total_cost}"

    def clean(self):
        """Validate that quantity and unit_cost are positive."""
        super().clean()

        if self.quantity is not None and self.quantity <= 0:
            raise ValidationError({
                'quantity': _('Quantity must be greater than 0.')
            })

        if self.unit_cost is not None and self.unit_cost <= 0:
            raise ValidationError({
                'unit_cost': _('Unit cost must be greater than 0.')
            })

    def save(self, *args, **kwargs):
        """Calculate total_cost before saving."""
        # Run clean validation
        self.clean()

        # Calculate total_cost if not explicitly set or if quantity/unit_cost changed
        if self.quantity is not None and self.unit_cost is not None:
            calculated_total = self.quantity * self.unit_cost
            # Round to 2 decimal places to match field precision
            self.total_cost = calculated_total.quantize(Decimal('0.01'))

        super().save(*args, **kwargs)

        # Update parent purchase return total amount
        if hasattr(self, 'purchase_return') and self.purchase_return:
            self.purchase_return.save()
