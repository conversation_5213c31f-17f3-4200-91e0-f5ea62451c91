from rest_framework import filters, viewsets

from purchases.models.supplier import Supplier
from purchases.serializers.supplier import SupplierSerializer
from utils.permissions import IsAdminOnly, IsAdminOrManager


class SupplierViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing suppliers.
    - Admin and managers can create, list, and view details
    - Only admin can delete, update
    - Automatically creates an account for each new supplier
    """

    queryset = Supplier.objects.all().order_by("name")
    serializer_class = SupplierSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "phone_number"]
    ordering_fields = ["name", "created"]
    ordering = ["name"]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["destroy", "update", "partial_update"]:
            permission_classes = [IsAdminOnly]
        else:
            permission_classes = [IsAdminOrManager]
        return [permission() for permission in permission_classes]

    def perform_destroy(self, instance):
        """
        Custom delete method to handle the supplier's account.
        """
        # Delete the associated account if it exists
        if hasattr(instance, "account") and instance.account:
            instance.account.delete()
        instance.delete()
