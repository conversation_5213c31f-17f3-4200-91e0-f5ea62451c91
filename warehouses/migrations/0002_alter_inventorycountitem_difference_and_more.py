# Generated by Django 5.2.1 on 2025-06-22 13:49

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("warehouses", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="inventorycountitem",
            name="difference",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The difference between recorded and system quantity (recorded - system)",
                max_digits=15,
                null=True,
                verbose_name="difference",
            ),
        ),
        migrations.AlterField(
            model_name="inventorycountitem",
            name="recorded_quantity",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The quantity physically counted during inventory",
                max_digits=15,
                null=True,
                verbose_name="recorded quantity",
            ),
        ),
        migrations.AlterField(
            model_name="inventorycountitem",
            name="system_quantity",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The quantity in the system before the count",
                max_digits=15,
                null=True,
                verbose_name="system quantity",
            ),
        ),
    ]
