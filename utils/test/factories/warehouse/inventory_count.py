from datetime import timedelta

from django.utils import timezone
from factory import SubFactory
from factory.django import DjangoModelFactory

from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models.inventory_count import InventoryCount


class InventoryCountFactory(DjangoModelFactory):
    """
    Factory for creating InventoryCount instances for testing.
    """

    class Meta:
        model = InventoryCount
        skip_postgeneration_save = True

    warehouse = SubFactory(WarehouseFactory)
    status = InventoryCount.Status.DRAFT
    notes = ""

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to handle status-specific fields"""
        status = kwargs.get("status", InventoryCount.Status.DRAFT)

        # Set timestamps based on status
        now = timezone.now()
        if status == InventoryCount.Status.IN_PROGRESS:
            if "started_at" not in kwargs:
                kwargs["started_at"] = now - timedelta(hours=1)
        elif status in [
            InventoryCount.Status.COMPLETED,
            InventoryCount.Status.CANCELLED,
        ]:
            if "started_at" not in kwargs:
                kwargs["started_at"] = now - timedelta(hours=2)
            if "ended_at" not in kwargs:
                kwargs["ended_at"] = now - timedelta(hours=1)

            # Set total cost and price for completed counts
            if status == InventoryCount.Status.COMPLETED:
                if "total_cost" not in kwargs:
                    kwargs["total_cost"] = 0
                if "total_price" not in kwargs:
                    kwargs["total_price"] = 0

        return super()._create(model_class, *args, **kwargs)
