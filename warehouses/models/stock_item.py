from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class StockItem(TimeStampedModel):
    """
    StockItem model representing the quantity of a product in a specific warehouse.
    """

    warehouse = models.ForeignKey(
        "warehouses.Warehouse",
        on_delete=models.CASCADE,
        related_name="stock_items",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse where this stock item is located"),
    )

    product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE,
        related_name="stock_items",
        verbose_name=_("product"),
        help_text=_("The product this stock item refers to"),
    )

    quantity = models.DecimalField(
        _("quantity"),
        max_digits=15,
        decimal_places=3,
        default=0,
        help_text=_("Current quantity in stock"),
    )

    min_stock = models.DecimalField(
        _("minimum stock"),
        max_digits=15,
        decimal_places=3,
        default=0,
        help_text=_("Minimum required quantity before restocking is needed"),
        validators=[MinValueValidator(0.0)],
    )

    class Meta:
        verbose_name = _("stock item")
        verbose_name_plural = _("stock items")
        ordering = ["warehouse", "product"]
        unique_together = [["warehouse", "product"]]

    def __str__(self):
        return f"{self.product} - {self.warehouse} ({self.quantity})"

    @property
    def needs_restock(self):
        """Check if the stock level is below the minimum threshold"""
        return self.quantity <= self.min_stock

    @property
    def is_out_of_stock(self):
        """Check if the item is out of stock"""
        return self.quantity <= 0

    def add_stock(self, quantity):
        """Add quantity to the current stock"""
        self.quantity += quantity
        self.save(update_fields=["quantity", "modified"])

    def remove_stock(self, quantity):
        """Remove quantity from the current stock"""
        # if quantity > self.quantity:
        #     raise ValueError("Insufficient stock")
        self.quantity -= quantity
        self.save(update_fields=["quantity", "modified"])
