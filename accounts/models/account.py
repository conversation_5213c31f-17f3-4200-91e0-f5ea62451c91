from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models.aggregates import Sum
from django_extensions.db.models import TimeStampedModel


class Account(TimeStampedModel):
    """
    Account model to track financial accounts for different entities.
    Uses Django's ContentType framework for generic foreign key relationship.
    """

    name = models.CharField(max_length=100, help_text="Name of the account")
    account_type = models.CharField(max_length=100, help_text="Type of the account")
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        related_name="account_owner",
        help_text="The model type of the related entity",
        blank=True,
        null=True,
    )
    object_id = models.PositiveIntegerField(
        help_text="ID of the related entity",
        blank=True,
        null=True,
    )
    content_object = GenericForeignKey("content_type", "object_id")

    class Meta:
        verbose_name = "Account"
        verbose_name_plural = "Accounts"
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
        ]

    def __str__(self):
        return f"Account {self.id} - {self.content_type} {self.object_id} (Balance: {self.balance})"

    @property
    def balance(self):
        return self.balances.aggregate(total=Sum("amount"))["total"] or 0
