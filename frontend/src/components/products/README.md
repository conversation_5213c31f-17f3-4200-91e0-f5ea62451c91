# Products CRUD Implementation

This directory contains the complete implementation of Products CRUD functionality for the ERP frontend application.

## Overview

The products system allows users to create, read, update, and delete products with comprehensive product information including pricing, categories, images, and inventory details. The implementation follows React best practices and uses React Bootstrap for styling.

## Features

- ✅ **List Products**: Paginated table with search and sorting
- ✅ **Create Product**: Form to add new products with validation
- ✅ **View Product**: Detailed view of product information
- ✅ **Edit Product**: Update existing products
- ✅ **Delete Product**: Remove products with confirmation
- ✅ **Category Integration**: Link products to categories
- ✅ **Image Upload**: Support for product images
- ✅ **Pricing Management**: Cost, selling price, and profit margin calculation
- ✅ **Unit Types**: Support for different measurement units
- ✅ **Barcode Support**: Optional barcode field
- ✅ **Search & Filter**: Search by name, barcode, description
- ✅ **Pagination**: Handle large datasets efficiently
- ✅ **Error Handling**: Comprehensive error states and messages
- ✅ **Loading States**: User feedback during API operations
- ✅ **Responsive Design**: Mobile-friendly interface
- ✅ **Role-based Access**: Admin-only operations for create/edit/delete

## File Structure

```
products/
├── README.md                    # This documentation
├── ProductsList.jsx            # Main list view with table, search, pagination
├── ProductForm.jsx             # Reusable form component for create/edit
├── ProductCreate.jsx           # Create product page
├── ProductEdit.jsx             # Edit product page
├── ProductView.jsx             # View product details page
├── DeleteConfirmModal.jsx      # Delete confirmation modal
└── __tests__/                  # Test files
    ├── ProductsList.test.jsx
    ├── ProductForm.test.jsx
    └── ...
```

## Components

### ProductsList
- **Purpose**: Main listing page for products
- **Features**: 
  - Paginated table display
  - Search functionality (name, barcode, description)
  - Column sorting (name, price, created date)
  - Action buttons (View, Edit, Delete)
  - Loading and error states
  - Profit margin display with color coding
- **Props**: None (uses hooks for data management)

### ProductForm
- **Purpose**: Reusable form for creating and editing products
- **Features**:
  - Form validation with real-time feedback
  - Image upload with preview
  - Category selection dropdown
  - Unit type selection
  - Profit margin calculation
  - Price validation (price >= cost)
  - Character counting for description
  - Error display
  - Loading states
- **Props**:
  - `initialData`: Pre-fill data for edit mode
  - `onSubmit`: Form submission handler
  - `loading`: Loading state
  - `error`: Error message
  - `isEdit`: Boolean to determine create vs edit mode

### ProductCreate
- **Purpose**: Create new product page
- **Features**:
  - Uses ProductForm component
  - Success message and redirect
  - Error handling with field-specific messages
- **Props**: None

### ProductEdit
- **Purpose**: Edit existing product page
- **Features**:
  - Fetches existing product data
  - Pre-fills form with current values
  - Uses ProductForm component
  - Success message and redirect
  - Error handling
- **Props**: None (uses URL params for product ID)

### ProductView
- **Purpose**: Display detailed product information
- **Features**:
  - Complete product details display
  - Image display with fallback
  - Profit margin and markup calculations
  - Category information
  - Metadata (created/modified dates)
  - Quick stats sidebar
  - Action buttons for edit (admin only)
- **Props**: None (uses URL params for product ID)

### DeleteConfirmModal
- **Purpose**: Confirmation dialog for product deletion
- **Features**:
  - Clear confirmation message
  - Loading state during deletion
  - Cancel and confirm actions
- **Props**:
  - `show`: Boolean to control modal visibility
  - `onHide`: Function to hide modal
  - `onConfirm`: Function to confirm deletion
  - `productName`: Name of product being deleted
  - `loading`: Loading state

## API Integration

### Service Layer (`productsService.js`)
Handles all API communications:
- `getProducts(params)` - List products with filtering
- `getProduct(id)` - Get single product
- `createProduct(data)` - Create new product (with file upload)
- `updateProduct(id, data)` - Update existing product (with file upload)
- `deleteProduct(id)` - Delete product
- `getUnitTypes()` - Get available unit types

### Custom Hooks (`useProducts.js`)
State management and API integration:
- `useProducts()` - Main hook for products CRUD operations
- `useProduct(id)` - Hook for single product management
- `useUnitTypes()` - Hook for unit type options

## Routing

The following routes are configured in `App.jsx`:

- `/products` - Products list (authenticated users)
- `/products/create` - Create product (admin only)
- `/products/:id` - View product (authenticated users)
- `/products/:id/edit` - Edit product (admin only)

## Permissions

- **List/View**: All authenticated users
- **Create/Edit/Delete**: Admin and Manager users only

## Validation

### Client-side Validation
- Product name: Required, 2-200 characters
- Cost price: Required, positive number
- Selling price: Required, positive number, must be >= cost price
- Description: Optional, max 1000 characters
- Barcode: Optional, max 100 characters
- Unit type: Required, from predefined list
- Category: Optional, from existing categories
- Image: Optional, image files only

### Server-side Validation
- Handled by Django backend
- Unique barcode validation
- Price validation (price >= cost)
- File type validation for images

## Error Handling

- **Network Errors**: User-friendly messages for connection issues
- **Validation Errors**: Field-specific error messages
- **Server Errors**: Graceful handling of 500 errors
- **Not Found**: Proper handling of missing products
- **Permission Errors**: Clear messages for unauthorized actions

## Features

### Image Upload
- Supports common image formats (JPEG, PNG, GIF)
- Image preview before upload
- Fallback display for products without images
- Proper file handling with FormData

### Profit Margin Calculation
- Real-time calculation in forms
- Color-coded display in lists and views
- Profit amount calculation
- Markup percentage display

### Unit Types
- Predefined unit types (piece, kg, g, l, ml)
- Consistent display formatting
- Easy to extend with new units

### Search and Filtering
- Search across name, barcode, and description
- Real-time search as you type
- Sorting by multiple fields
- Pagination with configurable page sizes

## Testing

### Unit Tests
- Component rendering tests
- Form validation tests
- API integration tests
- Error handling tests
- User interaction tests

### Integration Tests
- Full CRUD workflow tests
- Permission-based access tests
- File upload tests
- Search and pagination tests

## Performance Optimizations

- Lazy loading of product images
- Pagination to handle large datasets
- Debounced search to reduce API calls
- Optimistic updates for better UX
- Proper loading states and error boundaries

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Progressive enhancement
- Graceful degradation for older browsers
