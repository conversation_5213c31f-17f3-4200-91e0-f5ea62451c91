import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryForm from '../CategoryForm';
import { useParentCategories } from '../../../hooks/useCategories';

// Mock the useParentCategories hook
jest.mock('../../../hooks/useCategories');

describe('CategoryForm', () => {
    const mockParentCategories = [
        { id: 1, name: 'Electronics' },
        { id: 2, name: 'Clothing' }
    ];

    const defaultProps = {
        onSubmit: jest.fn(),
        loading: false,
        error: null,
        isEdit: false
    };

    beforeEach(() => {
        useParentCategories.mockReturnValue({
            parentCategories: mockParentCategories,
            loading: false
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders create form correctly', () => {
        render(<CategoryForm {...defaultProps} />);
        
        expect(screen.getByText('Create New Category')).toBeInTheDocument();
        expect(screen.getByLabelText(/Category Name/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Parent Category/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
        expect(screen.getByText('Create Category')).toBeInTheDocument();
    });

    test('renders edit form correctly', () => {
        const initialData = {
            id: 1,
            name: 'Test Category',
            description: 'Test description',
            parent_id: 2
        };

        render(
            <CategoryForm 
                {...defaultProps} 
                isEdit={true} 
                initialData={initialData} 
            />
        );
        
        expect(screen.getByText('Edit Category')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test Category')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();
        expect(screen.getByText('Update Category')).toBeInTheDocument();
    });

    test('validates required fields', async () => {
        render(<CategoryForm {...defaultProps} />);
        
        const submitButton = screen.getByText('Create Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(screen.getByText('Category name is required')).toBeInTheDocument();
        });
        
        expect(defaultProps.onSubmit).not.toHaveBeenCalled();
    });

    test('validates name length', async () => {
        render(<CategoryForm {...defaultProps} />);
        
        const nameInput = screen.getByLabelText(/Category Name/);
        fireEvent.change(nameInput, { target: { value: 'A' } });
        
        const submitButton = screen.getByText('Create Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(screen.getByText('Category name must be at least 2 characters long')).toBeInTheDocument();
        });
    });

    test('validates maximum name length', async () => {
        render(<CategoryForm {...defaultProps} />);
        
        const nameInput = screen.getByLabelText(/Category Name/);
        const longName = 'A'.repeat(101);
        fireEvent.change(nameInput, { target: { value: longName } });
        
        const submitButton = screen.getByText('Create Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(screen.getByText('Category name must not exceed 100 characters')).toBeInTheDocument();
        });
    });

    test('validates description length', async () => {
        render(<CategoryForm {...defaultProps} />);
        
        const nameInput = screen.getByLabelText(/Category Name/);
        const descriptionInput = screen.getByLabelText(/Description/);
        
        fireEvent.change(nameInput, { target: { value: 'Valid Name' } });
        fireEvent.change(descriptionInput, { target: { value: 'A'.repeat(501) } });
        
        const submitButton = screen.getByText('Create Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(screen.getByText('Description must not exceed 500 characters')).toBeInTheDocument();
        });
    });

    test('prevents self-reference in edit mode', async () => {
        const initialData = {
            id: 1,
            name: 'Test Category',
            description: 'Test description',
            parent_id: null
        };

        render(
            <CategoryForm 
                {...defaultProps} 
                isEdit={true} 
                initialData={initialData} 
            />
        );
        
        const parentSelect = screen.getByLabelText(/Parent Category/);
        fireEvent.change(parentSelect, { target: { value: '1' } });
        
        const submitButton = screen.getByText('Update Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(screen.getByText('A category cannot be a parent of itself')).toBeInTheDocument();
        });
    });

    test('submits form with correct data', async () => {
        render(<CategoryForm {...defaultProps} />);
        
        const nameInput = screen.getByLabelText(/Category Name/);
        const descriptionInput = screen.getByLabelText(/Description/);
        const parentSelect = screen.getByLabelText(/Parent Category/);
        
        fireEvent.change(nameInput, { target: { value: 'New Category' } });
        fireEvent.change(descriptionInput, { target: { value: 'New description' } });
        fireEvent.change(parentSelect, { target: { value: '1' } });
        
        const submitButton = screen.getByText('Create Category');
        fireEvent.click(submitButton);
        
        await waitFor(() => {
            expect(defaultProps.onSubmit).toHaveBeenCalledWith({
                name: 'New Category',
                description: 'New description',
                parent_id: 1
            });
        });
    });

    test('displays loading state', () => {
        render(<CategoryForm {...defaultProps} loading={true} />);
        
        expect(screen.getByText('Creating...')).toBeInTheDocument();
        expect(screen.getByText('Creating...')).toBeDisabled();
    });

    test('displays error message', () => {
        const error = 'Failed to create category';
        render(<CategoryForm {...defaultProps} error={error} />);
        
        expect(screen.getByText(error)).toBeInTheDocument();
    });

    test('shows character count for name and description', () => {
        render(<CategoryForm {...defaultProps} />);
        
        expect(screen.getByText('0/100 characters')).toBeInTheDocument();
        expect(screen.getByText('0/500 characters')).toBeInTheDocument();
    });

    test('updates character count when typing', () => {
        render(<CategoryForm {...defaultProps} />);
        
        const nameInput = screen.getByLabelText(/Category Name/);
        fireEvent.change(nameInput, { target: { value: 'Test' } });
        
        expect(screen.getByText('4/100 characters')).toBeInTheDocument();
    });

    test('filters out current category from parent options in edit mode', () => {
        const initialData = {
            id: 1,
            name: 'Electronics',
            description: 'Test description',
            parent_id: null
        };

        render(
            <CategoryForm 
                {...defaultProps} 
                isEdit={true} 
                initialData={initialData} 
            />
        );
        
        const parentSelect = screen.getByLabelText(/Parent Category/);
        const options = Array.from(parentSelect.options).map(option => option.text);
        
        expect(options).toContain('Clothing');
        expect(options).not.toContain('Electronics');
    });
});
