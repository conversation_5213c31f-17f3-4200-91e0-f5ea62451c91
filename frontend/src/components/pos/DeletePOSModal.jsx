import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { format } from 'date-fns';

/**
 * DeletePOSModal component
 * Confirmation dialog for deleting POS terminals
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Hide handler
 * @param {Function} props.onConfirm - Confirmation handler
 * @param {Object} props.pos - The POS terminal to be deleted
 * @param {boolean} props.loading - Loading state
 */
const DeletePOSModal = ({
    show,
    onHide,
    onConfirm,
    pos,
    loading = false
}) => {
    const handleConfirm = () => {
        if (onConfirm && pos) {
            onConfirm(pos.id);
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'PPpp');
        } catch (error) {
            return 'Invalid Date';
        }
    };

    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title className="text-danger">
                    Delete POS Terminal
                </Modal.Title>
            </Modal.Header>
            
            <Modal.Body>
                <div className="alert alert-danger">
                    <strong>Warning:</strong> This action cannot be undone. 
                    Deleting this POS terminal will permanently remove all associated data including sessions and transactions.
                </div>

                {pos && (
                    <div className="pos-details">
                        <h6>POS Terminal Details:</h6>
                        <div className="row">
                            <div className="col-sm-4"><strong>ID:</strong></div>
                            <div className="col-sm-8">#{pos.id}</div>
                        </div>
                        <div className="row">
                            <div className="col-sm-4"><strong>Name:</strong></div>
                            <div className="col-sm-8">{pos.name}</div>
                        </div>
                        <div className="row">
                            <div className="col-sm-4"><strong>Warehouse:</strong></div>
                            <div className="col-sm-8">
                                {pos.warehouse ? (
                                    <span>
                                        {pos.warehouse.name}
                                        {pos.warehouse.location && ` - ${pos.warehouse.location}`}
                                    </span>
                                ) : (
                                    'No warehouse assigned'
                                )}
                            </div>
                        </div>
                        {pos.description && (
                            <div className="row">
                                <div className="col-sm-4"><strong>Description:</strong></div>
                                <div className="col-sm-8">{pos.description}</div>
                            </div>
                        )}
                        <div className="row">
                            <div className="col-sm-4"><strong>Created:</strong></div>
                            <div className="col-sm-8">{formatDate(pos.created)}</div>
                        </div>
                        <div className="row">
                            <div className="col-sm-4"><strong>Last Modified:</strong></div>
                            <div className="col-sm-8">{formatDate(pos.modified)}</div>
                        </div>
                    </div>
                )}

                <div className="mt-3">
                    <p className="mb-2">
                        <strong>This will also delete:</strong>
                    </p>
                    <ul className="mb-0">
                        <li>All sessions associated with this POS terminal</li>
                        <li>All transactions within those sessions</li>
                        <li>Any related financial records</li>
                    </ul>
                </div>

                <p className="mt-3 mb-0">
                    Are you sure you want to delete this POS terminal?
                </p>
            </Modal.Body>
            
            <Modal.Footer>
                <Button 
                    variant="secondary" 
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button 
                    variant="danger" 
                    onClick={handleConfirm}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Deleting...
                        </>
                    ) : (
                        'Delete POS Terminal'
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

DeletePOSModal.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    pos: PropTypes.object,
    loading: PropTypes.bool
};

export default DeletePOSModal;
