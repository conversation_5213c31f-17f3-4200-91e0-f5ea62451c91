# Generated by Django 5.2.1 on 2025-06-26 11:33

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("pos", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExpenseCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the expense category",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the expense category",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Expense Category",
                "verbose_name_plural": "Expense Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Expense",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the expense",
                        max_digits=12,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the expense",
                        null=True,
                    ),
                ),
                (
                    "pos_session_transaction",
                    models.OneToOneField(
                        blank=True,
                        help_text="Related POS session transaction if applicable",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="expense",
                        to="pos.possessiontransaction",
                    ),
                ),
                (
                    "expense_category",
                    models.ForeignKey(
                        help_text="Category of the expense",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="expenses",
                        to="expenses.expensecategory",
                    ),
                ),
            ],
            options={
                "verbose_name": "Expense",
                "verbose_name_plural": "Expenses",
                "ordering": ["-created"],
            },
        ),
    ]
