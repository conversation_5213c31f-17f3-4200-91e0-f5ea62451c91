GenerateWarehousesPages:
  description: |
    In the `frontend/` project, generate CRUD pages for the `warehouses` app using the 'warehouses/' APIs.
    You should use "react-bootstrap" to create the UI, similar to the "components/products/" and "components/categories/" pages.
  rules:
    - Create the following components in `frontend/src/components/warehouses/`:
      - `WarehousesList.jsx` - For listing all warehouses with search, sort, and pagination
      - `WarehouseForm.jsx` - Reusable form component for create/edit operations
      - `WarehouseCreate.jsx` - Page for creating a new warehouse
      - `WarehouseEdit.jsx` - Page for editing an existing warehouse
      - `WarehouseView.jsx` - Page for viewing warehouse details
      - `DeleteConfirmModal.jsx` - Modal for confirming warehouse deletion

    - Follow these patterns for the warehouse model:
      - Required fields: name (string), location (string)
      - Optional fields: description (text), is_active (boolean)
      - Include timestamps: created_at, updated_at

    - Implement the following features:
      - List view with search by name and location
      - Sortable columns (name, location, created_at)
      - Pagination with configurable page size
      - Form validation for required fields
      - Loading states for async operations
      - Error handling with user-friendly messages
      - Success notifications after operations
      - Delete confirmation modal

    - Use these API endpoints:
      - GET /warehouses/ - List all warehouses
      - POST /warehouses/ - Create new warehouse
      - GET /warehouses/{id}/ - Get warehouse details
      - PUT /warehouses/{id}/ - Update warehouse
      - DELETE /warehouses/{id}/ - Delete warehouse

    - Follow the same styling and structure as products/categories components:
      - Use Bootstrap's grid system and components
      - Implement responsive design
      - Use consistent spacing and typography
      - Follow the same color scheme and UI patterns

    - Add proper routing in App.jsx:
      - /warehouses - List view
      - /warehouses/create - Create form
      - /warehouses/:id - View details
      - /warehouses/:id/edit - Edit form

    - Add navigation link to the main navigation if user has appropriate permissions

    - Include proper TypeScript types/interfaces for the warehouse model

    - Add unit tests for all components

    - Document the components with JSDoc comments

    - Follow the project's code style and conventions

    - Ensure all CRUD operations work correctly with the backend API

    - Handle loading and error states gracefully

    - Implement proper form validation with clear error messages

    - Add success notifications after successful operations

    - Include proper accessibility attributes (aria-*)

    - Make sure all interactive elements are keyboard navigable

    - Implement proper error boundaries