# Generated by Django 5.2.1 on 2025-07-22 06:47

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("purchases", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="purchase",
            name="paid_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0.0,
                help_text="Amount paid for this purchase",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="paid amount",
            ),
        ),
        migrations.AddField(
            model_name="purchase",
            name="reminder_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0.0,
                help_text="Amount remaining to be paid for this purchase",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="reminder amount",
            ),
        ),
        migrations.AddField(
            model_name="purchaseitem",
            name="unit_price",
            field=models.DecimalField(
                decimal_places=2,
                default=1,
                help_text="Price per unit of the product",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="unit price",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="purchaseitem",
            name="total_cost",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Total cost (quantity × unit_cost)",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="total cost",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseitem",
            name="unit_cost",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Cost per unit of the product",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="unit cost",
            ),
        ),
    ]
