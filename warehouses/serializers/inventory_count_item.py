from rest_framework import serializers

from ..models import InventoryCountItem


class InventoryCountItemSerializer(serializers.ModelSerializer):
    """Serializer for inventory count items."""

    product_name = serializers.CharField(
        source="stock_item.product.name", read_only=True
    )
    product_code = serializers.CharField(
        source="stock_item.product.code", read_only=True
    )
    unit_type = serializers.CharField(
        source="stock_item.product.unit_type", read_only=True
    )
    stock_item_quantity = serializers.DecimalField(
        source="stock_item.quantity",
        read_only=True,
        max_digits=15,
        decimal_places=3,
    )

    class Meta:
        model = InventoryCountItem
        fields = [
            "id",
            "stock_item",
            "product_name",
            "product_code",
            "unit_type",
            "recorded_quantity",
            "system_quantity",
            "difference",
            "notes",
            "stock_item_quantity",
        ]
        read_only_fields = [
            "id",
            "product_name",
            "product_code",
            "unit_type",
            "system_quantity",
            "difference",
        ]
