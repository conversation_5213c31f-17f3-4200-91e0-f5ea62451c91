# Generated by Django 5.2.1 on 2025-07-19 11:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("warehouses", "0004_inventorycount_total_price_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="inventorycountitem",
            name="difference",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The difference between recorded and system quantity (recorded - system)",
                max_digits=15,
                null=True,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="difference",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="inventorycountitem",
            name="recorded_quantity",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The quantity physically counted during inventory",
                max_digits=15,
                null=True,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="recorded quantity",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="inventorycountitem",
            name="system_quantity",
            field=models.DecimalField(
                blank=True,
                decimal_places=3,
                help_text="The quantity in the system before the count",
                max_digits=15,
                null=True,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="system quantity",
            ),
        ),
        migrations.AlterField(
            model_name="stockitem",
            name="min_stock",
            field=models.DecimalField(
                decimal_places=3,
                default=0,
                help_text="Minimum required quantity before restocking is needed",
                max_digits=15,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="minimum stock",
            ),
        ),
    ]
