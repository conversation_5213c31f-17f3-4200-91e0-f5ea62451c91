from rest_framework import serializers

from pos.models import POSSession


class POSSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for POS Session model
    """

    pos_name = serializers.CharField(source="pos.name", read_only=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)

    class Meta:
        model = POSSession
        fields = [
            "id",
            "pos",
            "pos_name",
            "user",
            "user_name",
            "opening_balance",
            "closing_balance",
            "calculated_balance",
            "total_sales",
            "total_expenses",
            "difference",
            "opened_at",
            "closed_at",
            "notes",
            "status",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "user",
            "total_sales",
            "total_expenses",
            "difference",
            "opened_at",
            "closed_at",
            "created",
            "modified",
        ]


class POSStartSessionSerializer(serializers.Serializer):
    opening_balance = serializers.DecimalField(max_digits=12, decimal_places=2)


class POSCloseSessionSerializer(serializers.Serializer):
    closing_balance = serializers.DecimalField(max_digits=12, decimal_places=2)
