import random
from decimal import Decimal

import factory
from factory import <PERSON>zy<PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from pos.models.pos_session_transaction import POSSessionTransaction, TransactionType
from utils.test.factories.pos.pos_session import POSSessionFactory


class POSSessionTransactionFactory(DjangoModelFactory):
    """
    Factory for creating POSSessionTransaction instances for testing.
    """

    class Meta:
        model = POSSessionTransaction
        skip_postgeneration_save = True

    session = SubFactory(POSSessionFactory)

    # Default to expense transaction type
    transaction_type = TransactionType.EXPENSE

    # Generate a random amount between 10 and 200
    amount = LazyAttribute(
        lambda _: Decimal(random.uniform(10, 200)).quantize(Decimal("0.01"))
    )

    description = factory.Faker("paragraph", nb_sentences=1)

    # Generic relation fields are left empty by default
    content_type = None
    object_id = None
