from decimal import Decimal

from rest_framework import serializers

from products.models.category import Category
from products.models.product import Product


class ProductSerializer(serializers.ModelSerializer):
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        source="category",
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "description",
            "cost",
            "price",
            "barcode",
            "image",
            "unit_type",
            "category_id",
            "created",
            "modified",
        ]
        read_only_fields = ["created", "modified"]

    def validate_price(self, value):
        """Ensure price is not lower than cost"""
        cost = Decimal(self.initial_data.get("cost", 0))
        if cost and value < cost:
            raise serializers.ValidationError("Price cannot be less than cost.")
        return value


class ProductReadSerializer(ProductSerializer):
    category_name = serializers.CharField(source="category.name", read_only=True)

    class Meta:
        model = Product
        fields = ProductSerializer.Meta.fields + ["category_name"]
        read_only_fields = ProductSerializer.Meta.read_only_fields + ["category_name"]
