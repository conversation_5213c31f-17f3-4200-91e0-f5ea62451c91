from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import (
    InventoryCount,
    InventoryCountItem,
    StockItem,
    Warehouse,
)


class StockItemInline(admin.TabularInline):
    model = StockItem
    extra = 1
    readonly_fields = ("quantity", "min_stock")
    fields = ("product", "quantity", "min_stock")
    show_change_link = True


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = ("name", "location", "created", "modified")
    search_fields = ("name", "location", "description")
    list_filter = ("created", "modified")
    readonly_fields = ("created", "modified")
    fieldsets = (
        (None, {"fields": ("name", "location", "description")}),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )
    inlines = [StockItemInline]


class InventoryCountItemInline(admin.TabularInline):
    model = InventoryCountItem
    extra = 0
    readonly_fields = (
        "stock_item",
        "system_quantity",
        "difference",
        "has_discrepancy_display",
    )
    fields = (
        "stock_item",
        "system_quantity",
        "recorded_quantity",
        "difference",
        "has_discrepancy_display",
        "notes",
    )

    def has_discrepancy_display(self, obj):
        if obj.difference is None:
            return ""
        if obj.difference < 0:
            return format_html(
                '<span style="color: red;">▼ {}</span>', abs(obj.difference)
            )
        elif obj.difference > 0:
            return format_html(
                '<span style="color: green;">▲ {}</span>', obj.difference
            )
        return "—"

    has_discrepancy_display.short_description = _("Discrepancy")


@admin.register(InventoryCount)
class InventoryCountAdmin(admin.ModelAdmin):
    list_display = (
        "__str__",
        "warehouse",
        "status",
        "started_at",
        "ended_at",
        "noted_by",
        "total_cost",
        "total_price",
    )
    list_filter = ("status", "warehouse", "started_at", "ended_at")
    search_fields = ("warehouse__name", "notes")
    readonly_fields = ("total_cost", "total_price", "created", "modified")
    fieldsets = (
        (None, {"fields": ("warehouse", "status", "notes")}),
        ("Timing", {"fields": ("started_at", "ended_at"), "classes": ("collapse",)}),
        ("Totals", {"fields": ("total_cost", "total_price")}),
        (
            "Metadata",
            {"fields": ("noted_by", "created", "modified"), "classes": ("collapse",)},
        ),
    )
    inlines = [InventoryCountItemInline]

    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only for new objects
            obj.noted_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockItem)
class StockItemAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "warehouse",
        "quantity",
        "min_stock",
        "status",
        "needs_restock",
        "is_out_of_stock",
    )
    list_filter = ("warehouse", "product__category")
    search_fields = ("product__name", "warehouse__name")
    readonly_fields = ("created", "modified", "needs_restock", "is_out_of_stock")
    list_editable = ("min_stock",)
    fieldsets = (
        (None, {"fields": ("product", "warehouse")}),
        (
            "Stock Levels",
            {"fields": ("quantity", "min_stock", "needs_restock", "is_out_of_stock")},
        ),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def status(self, obj):
        if obj.is_out_of_stock:
            return format_html('<span style="color: red;">Out of Stock</span>')
        elif obj.needs_restock:
            return format_html('<span style="color: orange;">Low Stock</span>')
        return format_html('<span style="color: green;">In Stock</span>')

    status.short_description = _("Status")


@admin.register(InventoryCountItem)
class InventoryCountItemAdmin(admin.ModelAdmin):
    list_display = (
        "__str__",
        "inventory_count_link",
        "system_quantity",
        "recorded_quantity",
        "difference_display",
        "has_discrepancy",
    )
    list_filter = ("inventory_count__warehouse", "inventory_count__status")
    search_fields = ("stock_item__product__name", "inventory_count__warehouse__name")
    readonly_fields = (
        "inventory_count_link",
        "stock_item",
        "system_quantity",
        "difference",
        "has_discrepancy",
    )

    def inventory_count_link(self, obj):
        url = reverse(
            "admin:warehouses_inventorycount_change", args=[obj.inventory_count.id]
        )
        return format_html('<a href="{}">{}</a>', url, obj.inventory_count)

    inventory_count_link.short_description = _("Inventory Count")

    def difference_display(self, obj):
        if obj.difference is None:
            return "—"
        if obj.difference < 0:
            return format_html(
                '<span style="color: red;">▼ {}</span>', abs(obj.difference)
            )
        elif obj.difference > 0:
            return format_html(
                '<span style="color: green;">▲ {}</span>', obj.difference
            )
        return "—"

    difference_display.short_description = _("Difference")
    difference_display.admin_order_field = "difference"
