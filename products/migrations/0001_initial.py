# Generated by Django 5.2.1 on 2025-06-15 09:07

import django.db.models.deletion
import django_extensions.db.fields
import products.models
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Enter the category name",
                        max_length=100,
                        verbose_name="name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Enter a description for the category (optional)",
                        null=True,
                        verbose_name="description",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Select a parent category if this is a sub-category",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="products.category",
                        verbose_name="parent category",
                    ),
                ),
            ],
            options={
                "verbose_name": "category",
                "verbose_name_plural": "categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Enter the product name",
                        max_length=200,
                        verbose_name="name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Enter a detailed description of the product (optional)",
                        null=True,
                        verbose_name="description",
                    ),
                ),
                (
                    "cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Cost price of the product",
                        max_digits=10,
                        verbose_name="cost price",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Selling price of the product",
                        max_digits=10,
                        verbose_name="selling price",
                    ),
                ),
                (
                    "barcode",
                    models.CharField(
                        blank=True,
                        help_text="Barcode number (optional)",
                        max_length=100,
                        null=True,
                        unique=True,
                        verbose_name="barcode",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Upload a product image (optional)",
                        null=True,
                        upload_to=products.models.product_image_upload_path,
                        verbose_name="image",
                    ),
                ),
                (
                    "unit_type",
                    models.CharField(
                        choices=[
                            ("piece", "Piece"),
                            ("kg", "Kilogram"),
                            ("g", "Gram"),
                            ("l", "Liter"),
                            ("ml", "Milliliter"),
                        ],
                        default="piece",
                        help_text="Select the unit type for this product",
                        max_length=10,
                        verbose_name="unit type",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        help_text="Select a category for this product",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="products",
                        to="products.category",
                        verbose_name="category",
                    ),
                ),
            ],
            options={
                "verbose_name": "product",
                "verbose_name_plural": "products",
                "ordering": ["name"],
                "indexes": [
                    models.Index(fields=["name"], name="products_pr_name_9ff0a3_idx"),
                    models.Index(
                        fields=["barcode"], name="products_pr_barcode_e44f4f_idx"
                    ),
                    models.Index(
                        fields=["category"], name="products_pr_categor_9edb3d_idx"
                    ),
                ],
            },
        ),
    ]
