from rest_framework import serializers

from ..models import Salary


class SalarySerializer(serializers.ModelSerializer):
    """
    Serializer for Salary model with related employee and attendance information.
    """

    # Employee information
    employee_id = serializers.IntegerField(source="employee.id", read_only=True)
    employee_name = serializers.CharField(source="employee.user.get_full_name", read_only=True)
    employee_first_name = serializers.CharField(source="employee.user.first_name", read_only=True)
    employee_last_name = serializers.CharField(source="employee.user.last_name", read_only=True)
    employee_email = serializers.EmailField(source="employee.user.email", read_only=True)
    employee_phone = serializers.CharField(source="employee.user.phone_number", read_only=True)
    employee_role = serializers.CharField(source="employee.user.role", read_only=True)
    employee_type = serializers.Char<PERSON>ield(source="employee.type", read_only=True)
    employee_status = serializers.Char<PERSON><PERSON>(source="employee.status", read_only=True)
    employee_pos_name = serializers.<PERSON><PERSON><PERSON><PERSON>(source="employee.pos.name", read_only=True)
    employee_hour_rate = serializers.DecimalField(
        source="employee.hour_rate",
        max_digits=10,
        decimal_places=2,
        read_only=True
    )
    employee_day_rate = serializers.DecimalField(
        source="employee.day_rate",
        max_digits=10,
        decimal_places=2,
        read_only=True
    )

    # Attendance information
    attendance_id = serializers.IntegerField(source="attendance.id", read_only=True)
    attendance_time_in = serializers.DateTimeField(source="attendance.time_in", read_only=True)
    attendance_time_out = serializers.DateTimeField(source="attendance.time_out", read_only=True)
    attendance_total_hours = serializers.FloatField(source="attendance.total_hours", read_only=True)
    attendance_notes = serializers.CharField(source="attendance.notes", read_only=True)

    # Calculated fields
    hourly_equivalent = serializers.SerializerMethodField()
    work_date = serializers.SerializerMethodField()

    class Meta:
        model = Salary
        fields = [
            "id",
            "salary",
            "notes",
            "created",
            "modified",
            # Employee fields
            "employee_id",
            "employee_name",
            "employee_first_name",
            "employee_last_name",
            "employee_email",
            "employee_phone",
            "employee_role",
            "employee_type",
            "employee_status",
            "employee_pos_name",
            "employee_hour_rate",
            "employee_day_rate",
            # Attendance fields
            "attendance_id",
            "attendance_time_in",
            "attendance_time_out",
            "attendance_total_hours",
            "attendance_notes",
            # Calculated fields
            "hourly_equivalent",
            "work_date",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
        ]

    def get_hourly_equivalent(self, obj):
        """Calculate hourly equivalent rate based on total hours worked."""
        if obj.attendance and obj.attendance.total_hours and obj.attendance.total_hours > 0:
            return round(float(obj.salary) / obj.attendance.total_hours, 2)
        return None

    def get_work_date(self, obj):
        """Get the work date from attendance time_in."""
        if obj.attendance and obj.attendance.time_in:
            return obj.attendance.time_in.date()
        return None


class SalarySummarySerializer(serializers.ModelSerializer):
    """
    Simplified serializer for salary summaries and lists.
    """

    employee_name = serializers.CharField(source="employee.user.get_full_name", read_only=True)
    employee_pos_name = serializers.CharField(source="employee.pos.name", read_only=True)
    work_date = serializers.SerializerMethodField()
    total_hours = serializers.FloatField(source="attendance.total_hours", read_only=True)

    class Meta:
        model = Salary
        fields = [
            "id",
            "salary",
            "employee_name",
            "employee_pos_name",
            "work_date",
            "total_hours",
            "created",
        ]
        read_only_fields = ["id", "created"]

    def get_work_date(self, obj):
        """Get the work date from attendance time_in."""
        if obj.attendance and obj.attendance.time_in:
            return obj.attendance.time_in.date()
        return None