import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, <PERSON>, Spinner} from 'react-bootstrap';
import {useNavigate, useParams} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {posService} from '../../services/posService';
import {format} from 'date-fns';
import {FaArrowLeft, FaEdit, FaEye, FaTrash} from 'react-icons/fa';
import DeletePOSModal from './DeletePOSModal';

/**
 * POSView component
 * Shows POS terminal details
 */
const POSView = () => {
    const navigate = useNavigate();
    const {posId} = useParams();
    const {currentUser} = useAuth();

    const [pos, setPOS] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [startingSession, setStartingSession] = useState(false);

    // Delete modal state
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteLoading, setDeleteLoading] = useState(false);

    // Fetch POS details
    const fetchPOSDetails = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await posService.getPOSDetails(posId);
            setPOS(response.data);

        } catch (err) {
            console.error('Error fetching POS details:', err);
            setError('Failed to load POS details. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Handle delete POS
    const handleDeletePOS = () => {
        setShowDeleteModal(true);
    };

    const confirmDeletePOS = async (posId) => {
        try {
            setDeleteLoading(true);
            setError('');

            await posService.deletePOS(posId);

            // Navigate back to POS list after successful deletion
            navigate('/pos');

        } catch (err) {
            console.error('Error deleting POS:', err);
            setError('Failed to delete POS terminal. Please try again.');
            setShowDeleteModal(false);
        } finally {
            setDeleteLoading(false);
        }
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'PPpp');
        } catch (error) {
            return 'Invalid Date';
        }
    };

    // Initial load
    useEffect(() => {
        if (posId) {
            fetchPOSDetails();
        }
    }, [posId]);

    if (loading) {
        return (
            <Container>
                <div className="text-center p-4">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            </Container>
        );
    }

    if (!pos) {
        return (
            <Container>
                <Alert variant="danger">
                    POS terminal not found or you don't have permission to view it.
                </Alert>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/pos')}
                                className="me-3"
                            >
                                <FaArrowLeft/>
                            </Button>
                            <h2>POS Terminal: {pos.name}</h2>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate(`/pos/${posId}/edit`)}
                            >
                                <FaEdit className="me-2"/>
                                Edit
                            </Button>
                            <Button
                                variant="primary"
                                onClick={() => navigate(`/pos/sessions?pos=${posId}`)}
                            >
                                <FaEye className="me-2"/>
                                View Sessions
                            </Button>
                            <Button
                                variant="outline-danger"
                                onClick={handleDeletePOS}
                            >
                                <FaTrash className="me-2"/>
                                Delete
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* POS Details */}
            <Row>
                <Col md={8}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Terminal Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="row mb-3">
                                <div className="col-sm-3"><strong>ID:</strong></div>
                                <div className="col-sm-9">#{pos.id}</div>
                            </div>
                            <div className="row mb-3">
                                <div className="col-sm-3"><strong>Name:</strong></div>
                                <div className="col-sm-9">{pos.name}</div>
                            </div>
                            <div className="row mb-3">
                                <div className="col-sm-3"><strong>Warehouse:</strong></div>
                                <div className="col-sm-9">
                                    {pos.warehouse ? (
                                        <span>
                                            {pos.warehouse.name}
                                            {pos.warehouse.location && ` - ${pos.warehouse.location}`}
                                        </span>
                                    ) : (
                                        'No warehouse assigned'
                                    )}
                                </div>
                            </div>
                            {pos.description && (
                                <div className="row mb-3">
                                    <div className="col-sm-3"><strong>Description:</strong></div>
                                    <div className="col-sm-9">{pos.description}</div>
                                </div>
                            )}
                            <div className="row mb-3">
                                <div className="col-sm-3"><strong>Created:</strong></div>
                                <div className="col-sm-9">{formatDate(pos.created)}</div>
                            </div>
                            <div className="row mb-3">
                                <div className="col-sm-3"><strong>Last Modified:</strong></div>
                                <div className="col-sm-9">{formatDate(pos.modified)}</div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Quick Actions</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-grid gap-2">
                                <Button
                                    variant="outline-secondary"
                                    onClick={() => navigate(`/pos/${posId}/edit`)}
                                >
                                    <FaEdit className="me-2"/>
                                    Edit POS Terminal
                                </Button>
                                <Button
                                    variant="outline-primary"
                                    onClick={() => navigate(`/pos/sessions?pos=${posId}`)}
                                >
                                    <FaEye className="me-2"/>
                                    View All Sessions
                                </Button>
                                <Button
                                    variant="outline-danger"
                                    onClick={handleDeletePOS}
                                >
                                    <FaTrash className="me-2"/>
                                    Delete POS Terminal
                                </Button>
                                <Button
                                    variant="outline-secondary"
                                    onClick={() => navigate('/pos')}
                                >
                                    <FaArrowLeft className="me-2"/>
                                    Back to POS List
                                </Button>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Delete Modal */}
            <DeletePOSModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={confirmDeletePOS}
                pos={pos}
                loading={deleteLoading}
            />
        </Container>
    );
};

export default POSView;
