from decimal import Decimal

from django.urls import reverse
from rest_framework import status

from pos.models import POS, POSSession
from utils.test.base_test import BaseTestCase
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class POSViewSetTestCase(BaseTestCase):
    """Test suite for the POSViewSet."""

    def setUp(self):
        super().setUp()
        # Create some test data
        self.warehouse1 = WarehouseFactory.create(name="Warehouse 1")
        self.warehouse2 = WarehouseFactory.create(name="Warehouse 2")

        # Create POS instances
        self.pos1 = POSFactory.create(warehouse=self.warehouse1, name="POS 1")
        self.pos2 = POSFactory.create(warehouse=self.warehouse2, name="POS 2")

        # Update manager's POS assignment
        self.manager_user.employee.pos = self.pos1
        self.manager_user.employee.save()

        # Update cashier's POS assignment
        self.cashier_user.employee.pos = self.pos1
        self.cashier_user.employee.save()

        # URL for the POS list
        self.list_url = reverse("pos:pos-list")

    # Helper methods
    def get_detail_url(self, pos_id):
        return reverse("pos:pos-detail", args=[pos_id])

    def get_start_session_url(self, pos_id):
        return reverse("pos:pos-start-session", args=[pos_id])

    # Test cases for list action
    def test_list_pos_as_admin(self):
        """Admin should see all POS terminals."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

    def test_list_pos_as_manager(self):
        """Manager should only see their assigned POS terminal."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.pos1.id)

    def test_list_pos_as_cashier(self):
        """Cashier should only see their assigned POS terminal."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.pos1.id)

    def test_list_pos_unauthenticated(self):
        """Unauthenticated users should not be able to list POS terminals."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Test cases for retrieve action
    def test_retrieve_pos_as_admin(self):
        """Admin should be able to retrieve any POS terminal."""
        url = self.get_detail_url(self.pos1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.pos1.id)

    def test_retrieve_pos_as_manager_assigned(self):
        """Manager should be able to retrieve their assigned POS terminal."""
        url = self.get_detail_url(self.pos1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_pos_as_manager_unassigned(self):
        """Manager should not be able to retrieve unassigned POS terminal."""
        url = self.get_detail_url(self.pos2.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Test cases for create action
    def test_create_pos_as_admin_for_existing_warehouse_have_pos(self):
        """Admin should be able to create a new POS terminal."""
        data = {
            "name": "New POS",
            "description": "A new POS terminal",
            "warehouse": self.warehouse1.id,
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_pos_as_admin(self):
        """Admin should be able to create a new POS terminal."""
        warehouse = WarehouseFactory.create(name="Warehouse")

        data = {
            "name": "New POS",
            "description": "A new POS terminal",
            "warehouse": warehouse.id,
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(POS.objects.count(), 4)

    def test_create_pos_as_manager(self):
        """Manager should not be able to create a POS terminal."""
        data = {
            "name": "New POS",
            "description": "A new POS terminal",
            "warehouse": self.warehouse1.id,
        }
        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Test cases for update action
    def test_update_pos_as_admin(self):
        """Admin should be able to update any POS terminal."""
        url = self.get_detail_url(self.pos1.id)
        data = {"name": "Updated POS Name"}
        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.pos1.refresh_from_db()
        self.assertEqual(self.pos1.name, "Updated POS Name")

    def test_update_pos_as_manager_assigned(self):
        """Manager should not be able to update their assigned POS terminal."""
        url = self.get_detail_url(self.pos1.id)
        data = {"name": "Should Not Update"}
        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Test cases for delete action
    def test_delete_pos_as_admin(self):
        """Admin should be able to delete a POS terminal."""
        self.assertEqual(POS.objects.count(), 3)
        url = self.get_detail_url(self.pos1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(POS.objects.count(), 2)

    def test_delete_pos_with_sessions(self):
        """Should not be able to delete a POS with active sessions."""
        self.assertEqual(POS.objects.count(), 3)
        # Create a session for pos1
        POSSession.objects.create(
            pos=self.pos1, user=self.admin_user, opening_balance=Decimal("100.00")
        )
        url = self.get_detail_url(self.pos1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(POS.objects.count(), 3)

    # Test cases for start_session action
    def test_start_session_as_cashier(self):
        """Cashier should be able to start a new session."""
        url = self.get_start_session_url(self.pos1.id)
        data = {"opening_balance": "500.00"}
        response = self.cashier_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(POSSession.objects.filter(pos=self.pos1).exists())

    def test_start_session_as_manager(self):
        """Manager should be able to start a new session."""
        url = self.get_start_session_url(self.pos1.id)
        data = {"opening_balance": "500.00"}
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(POSSession.objects.filter(pos=self.pos1).exists())

    def test_start_session_as_admin(self):
        """Admin should not be able to start a session (must be cashier/manager)."""
        url = self.get_start_session_url(self.pos1.id)
        data = {"opening_balance": "500.00"}
        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_start_session_already_open(self):
        """Should not be able to start a new session if one is already open."""
        # Create an open session
        POSSession.objects.create(
            pos=self.pos1, user=self.cashier_user, opening_balance=Decimal("100.00")
        )

        url = self.get_start_session_url(self.pos1.id)
        data = {"opening_balance": "500.00"}
        response = self.cashier_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Test filtering
    def test_filter_pos_by_warehouse(self):
        """Should be able to filter POS by warehouse."""
        url = f"{self.list_url}?warehouse={self.warehouse1.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["warehouse"], self.warehouse1.id)

    def test_search_pos_by_name(self):
        """Should be able to search POS by name."""
        url = f"{self.list_url}?search=POS 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "POS 1")
