from rest_framework import serializers

from invoices.models.invoice_item import InvoiceItem
from products.serialzers.product import ProductReadSerializer


class InvoiceItemSerializer(serializers.ModelSerializer):
    """
    Serializer for InvoiceItem model.
    """

    class Meta:
        model = InvoiceItem
        fields = ["id", "product", "quantity", "unit_price", "total_price", "notes"]

    def validate(self, data):
        """
        Validate that unit_price is not less than product cost.
        """
        unit_price = data.get("unit_price")
        product = data.get("product")

        # If this is an update and unit_price is not being changed, skip validation
        if self.instance and "unit_price" not in data:
            return data

        if unit_price is not None and product is not None:
            if unit_price < product.cost:
                raise serializers.ValidationError(
                    {
                        "unit_price": f"Unit price (${unit_price}) cannot be less than product cost (${product.cost})"
                    }
                )

        return data


class InvoiceItemReadSerializer(serializers.ModelSerializer):
    """
    Serializer for reading InvoiceItem with expanded product details.
    """

    product = ProductReadSerializer(read_only=True)

    class Meta:
        model = InvoiceItem
        fields = [
            "id",
            "product",
            "quantity",
            "unit_price",
            "total_price",
            "notes",
            "created",
            "modified",
        ]
