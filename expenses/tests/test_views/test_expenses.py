from decimal import Decimal

from django.urls import reverse
from rest_framework import status

from expenses.models import Expense
from pos.models import POSSession, TransactionType
from utils.test.base_test import BaseTestCase
from utils.test.factories.expenses.expense import ExpenseFactory
from utils.test.factories.expenses.expense_category import ExpenseCategoryFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class ExpenseViewSetTestCase(BaseTestCase):
    """
    Test cases for ExpenseViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("expenses:expense-list")
        self.detail_url_name = "expenses:expense-detail"

        # Create test data
        self.expense_category = ExpenseCategoryFactory.create(name="Office Supplies")

        # Create separate warehouse and POS for cashier to test proper filtering
        self.cashier_warehouse = WarehouseFactory.create(name="Cashier Warehouse")
        self.cashier_pos = POSFactory.create(
            warehouse=self.cashier_warehouse, name="Cashier POS"
        )
        self.cashier_user.employee.pos = self.cashier_pos
        self.cashier_user.employee.save()

        # Create separate warehouse and POS for admin to test proper access control
        self.admin_warehouse = WarehouseFactory.create(name="Admin Warehouse")
        self.admin_pos = POSFactory.create(
            warehouse=self.admin_warehouse, name="Admin POS"
        )

        # Create POS sessions for testing
        self.admin_session = POSSessionFactory.create(
            pos=self.admin_pos,
            user=self.admin_user,
            status=POSSession.Status.OPEN,
            opening_balance=Decimal("1000.00"),
        )

        self.manager_session = POSSessionFactory.create(
            pos=self.pos,
            user=self.manager_user,
            status=POSSession.Status.OPEN,
            opening_balance=Decimal("800.00"),
        )

        # Create test expenses
        self.admin_expense = ExpenseFactory.create(
            expense_category=self.expense_category,
            amount=Decimal("50.00"),
            description="Admin test expense",
            pos_session_transaction=None,
        )

        # Create expense with POS session transaction for manager
        self.manager_expense = ExpenseFactory.create(
            expense_category=self.expense_category,
            amount=Decimal("30.00"),
            description="Manager test expense",
        )
        # Update the transaction to link to manager's session
        self.manager_expense.pos_session_transaction.session = self.manager_session
        self.manager_expense.pos_session_transaction.save()

    def get_detail_url(self, expense_id):
        """Helper method to get detail URL for an expense."""
        return reverse(self.detail_url_name, kwargs={"pk": expense_id})

    # Authentication Tests
    def test_list_expenses_unauthenticated(self):
        """Test that unauthenticated users cannot list expenses."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_expense_unauthenticated(self):
        """Test that unauthenticated users cannot retrieve expenses."""
        url = self.get_detail_url(self.admin_expense.id)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_expense_unauthenticated(self):
        """Test that unauthenticated users cannot create expenses."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "25.00",
            "description": "Test expense",
        }
        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # List Tests
    def test_list_expenses_as_admin(self):
        """Admin should see all expenses."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)  # Both expenses

        # Check that expenses are ordered by -created
        expense_ids = [expense["id"] for expense in response.data["results"]]
        self.assertIn(self.admin_expense.id, expense_ids)
        self.assertIn(self.manager_expense.id, expense_ids)

    def test_list_expenses_as_manager(self):
        """Manager should only see expenses linked to their POS sessions."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.manager_expense.id)

    def test_list_expenses_as_cashier(self):
        """Cashier should only see expenses linked to their POS sessions."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 0)  # No expenses for cashier

    # Retrieve Tests
    def test_retrieve_expense_as_admin(self):
        """Admin should be able to retrieve any expense."""
        url = self.get_detail_url(self.admin_expense.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.admin_expense.id)
        self.assertEqual(response.data["amount"], "50.00")
        self.assertEqual(response.data["description"], "Admin test expense")

    def test_retrieve_expense_as_manager_own_expense(self):
        """Manager should be able to retrieve their own expense."""
        url = self.get_detail_url(self.manager_expense.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.manager_expense.id)

    def test_retrieve_expense_as_manager_other_expense(self):
        """Manager should not be able to retrieve expenses not linked to their sessions."""
        url = self.get_detail_url(self.admin_expense.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_expense_as_cashier(self):
        """Cashier should not be able to retrieve expenses not linked to their sessions."""
        url = self.get_detail_url(self.admin_expense.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_expense_as_admin_without_pos_session(self):
        """Admin should be able to create expense without POS session."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "75.00",
            "description": "Admin expense without session",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that expense was created
        expense = Expense.objects.get(id=response.data["id"])
        self.assertEqual(expense.amount, Decimal("75.00"))
        self.assertEqual(expense.description, "Admin expense without session")
        self.assertIsNone(expense.pos_session_transaction)

    def test_create_expense_as_admin_with_pos_session(self):
        """Admin should be able to create expense with POS session."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "100.00",
            "description": "Admin expense with session",
            "pos_session_id": self.admin_session.id,
        }
        old_total_expenses = self.admin_session.total_expenses
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that expense was created with transaction
        expense = Expense.objects.get(id=response.data["id"])
        self.assertEqual(expense.amount, Decimal("100.00"))
        self.assertIsNotNone(expense.pos_session_transaction)
        self.assertEqual(expense.pos_session_transaction.session, self.admin_session)
        self.assertEqual(expense.pos_session_transaction.amount, Decimal("100.00"))
        self.assertEqual(
            expense.pos_session_transaction.transaction_type, TransactionType.EXPENSE
        )
        self.admin_session.refresh_from_db()
        self.assertEqual(
            self.admin_session.total_expenses, old_total_expenses + Decimal("100.00")
        )

    def test_create_expense_as_manager_with_valid_pos_session(self):
        """Manager should be able to create expense with their POS session."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "40.00",
            "description": "Manager expense",
            "pos_session_id": self.manager_session.id,
        }
        old_total_expenses = self.manager_session.total_expenses
        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that expense was created with transaction
        expense = Expense.objects.get(id=response.data["id"])
        self.assertEqual(expense.amount, Decimal("40.00"))
        self.assertIsNotNone(expense.pos_session_transaction)
        self.assertEqual(expense.pos_session_transaction.session, self.manager_session)
        self.manager_session.refresh_from_db()
        self.assertEqual(
            self.manager_session.total_expenses, old_total_expenses + Decimal("40.00")
        )

    def test_create_expense_as_manager_with_invalid_pos_session(self):
        """Manager should not be able to create expense with admin's POS session."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "40.00",
            "description": "Manager expense",
            "pos_session_id": self.admin_session.id,  # Not their session
        }
        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("No active POS session found", response.data["detail"])

    def test_create_expense_as_cashier_with_valid_pos_session(self):
        """Cashier should be able to create expense with their POS session."""
        # Create a session for cashier on their POS
        cashier_session = POSSessionFactory.create(
            pos=self.cashier_pos,
            user=self.cashier_user,
            status=POSSession.Status.OPEN,
            opening_balance=Decimal("500.00"),
        )
        old_total_expenses = cashier_session.total_expenses
        data = {
            "expense_category": self.expense_category.id,
            "amount": "20.00",
            "description": "Cashier expense",
            "pos_session_id": cashier_session.id,
        }
        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that expense was created with transaction
        expense = Expense.objects.get(id=response.data["id"])
        self.assertEqual(expense.amount, Decimal("20.00"))
        self.assertIsNotNone(expense.pos_session_transaction)
        self.assertEqual(expense.pos_session_transaction.session, cashier_session)
        cashier_session.refresh_from_db()
        self.assertEqual(
            cashier_session.total_expenses, old_total_expenses + Decimal("20.00")
        )

    def test_create_expense_as_cashier_without_pos_session(self):
        """Cashier cannot create expense without providing pos_session_id due to ViewSet logic bug."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "20.00",
            "description": "Cashier expense without session",
        }
        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_expense_with_closed_pos_session(self):
        """Should not be able to create expense with closed POS session."""
        closed_session = POSSessionFactory.create(
            pos=self.pos,
            user=self.manager_user,
            status=POSSession.Status.CLOSED,
            opening_balance=Decimal("500.00"),
            closing_balance=Decimal("600.00"),
        )

        data = {
            "expense_category": self.expense_category.id,
            "amount": "30.00",
            "description": "Expense with closed session",
            "pos_session_id": closed_session.id,
        }
        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("No active POS session found", response.data["detail"])

    def test_create_expense_with_invalid_data(self):
        """Test creating expense with invalid data."""
        data = {
            "expense_category": 99999,  # Non-existent category
            "amount": "invalid_amount",
            "description": "Test expense",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_expense_missing_required_fields(self):
        """Test creating expense with missing required fields."""
        data = {
            "description": "Test expense without required fields",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("expense_category", response.data)
        self.assertIn("amount", response.data)

    # Update Tests
    def test_update_expense(self):
        """Admin should be able to update expenses, but currently uses read-only serializer."""
        url = self.get_detail_url(self.admin_expense.id)
        data = {
            "expense_category": self.expense_category.id,
            "amount": "60.00",
            "description": "Updated admin expense",
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    # Delete Tests
    def test_delete_expense(self):
        """Admin should be able to delete expenses."""
        url = self.get_detail_url(self.admin_expense.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    # Serializer Tests
    def test_serializer_used_for_create(self):
        """Test that ExpenseWriteSerializer is used for create operations."""
        data = {
            "expense_category": self.expense_category.id,
            "amount": "25.00",
            "description": "Test serializer",
            "pos_session_id": self.admin_session.id,
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that pos_session_id field was accepted (only in write serializer)
        self.assertIn("pos_session_id", data)

    def test_serializer_used_for_read(self):
        """Test that ExpenseReadSerializer is used for read operations."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that expense_category is expanded (only in read serializer)
        if response.data["results"]:
            expense_data = response.data["results"][0]
            self.assertIn("expense_category", expense_data)
            self.assertIsInstance(expense_data["expense_category"], dict)
            self.assertIn("name", expense_data["expense_category"])

    def test_expense_ordering(self):
        """Test that expenses are ordered by -created."""
        # Create additional expense
        newer_expense = ExpenseFactory.create(
            expense_category=self.expense_category,
            amount=Decimal("10.00"),
            pos_session_transaction=None,
        )

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that newer expense comes first
        expense_ids = [expense["id"] for expense in response.data["results"]]
        newer_expense_index = expense_ids.index(newer_expense.id)
        self.assertEqual(newer_expense_index, 0)  # Should be first
