from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import POS, POSSession, POSSessionTransaction


@admin.register(POS)
class POSAdmin(admin.ModelAdmin):
    list_display = ("name", "warehouse_link", "created", "modified")
    search_fields = ("name", "description", "warehouse__name")
    list_filter = ("created", "modified")
    readonly_fields = ("created", "modified")
    fieldsets = (
        (None, {"fields": ("name", "warehouse", "description")}),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def warehouse_link(self, obj):
        url = reverse("admin:warehouses_warehouse_change", args=[obj.warehouse.id])
        return format_html('<a href="{}">{}</a>', url, obj.warehouse)

    warehouse_link.short_description = _("Warehouse")


class POSSessionTransactionInline(admin.TabularInline):
    model = POSSessionTransaction
    extra = 0
    readonly_fields = ("transaction_type", "amount", "description", "created")
    fields = ("transaction_type", "amount", "description", "created")
    can_delete = False
    max_num = 10

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(POSSession)
class POSSessionAdmin(admin.ModelAdmin):
    list_display = (
        "pos",
        "status_display",
        "user",
        "opened_at",
        "closed_at",
        "opening_balance",
        "closing_balance",
        "total_sales",
        "total_expenses",
        "difference",
    )
    list_filter = ("status", "pos", "opened_at", "closed_at")
    search_fields = ("pos__name", "user__email", "notes")
    readonly_fields = ("created", "modified", "calculated_balance_display", "opened_at_display", "closed_at_display")
    fieldsets = (
        ("Session Information", {"fields": ("pos", "user", "status", "notes")}),
        (
            "Balances",
            {
                "fields": (
                    "opening_balance",
                    "closing_balance",
                    "total_sales",
                    "total_expenses",
                    "difference",
                    "calculated_balance_display",
                )
            },
        ),
        ("Timing", {"fields": ("opened_at_display", "closed_at_display"), "classes": ("collapse",)}),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    inlines = [POSSessionTransactionInline]
    actions = [
        "close_selected_sessions",
        "suspend_selected_sessions",
        "resume_selected_sessions",
    ]

    def status_display(self, obj):
        status_colors = {"open": "green", "closed": "gray", "suspended": "orange"}
        return format_html(
            '<span style="color: {};">{}</span>',
            status_colors.get(obj.status, "black"),
            obj.get_status_display(),
        )

    status_display.short_description = _("Status")
    status_display.admin_order_field = "status"

    def opened_at_display(self, obj):
        return obj.opened_at.strftime('%Y-%m-%d %H:%M') if obj.opened_at else '—'
    opened_at_display.short_description = 'Opened At'

    def closed_at_display(self, obj):
        return obj.closed_at.strftime('%Y-%m-%d %H:%M') if obj.closed_at else '—'
    closed_at_display.short_description = 'Closed At'

    def calculated_balance_display(self, obj):
        return f"{obj.calculated_balance:.2f}"

    calculated_balance_display.short_description = _("Calculated Balance")

    @admin.action(description=_("Close selected sessions"))
    def close_selected_sessions(self, request, queryset):
        for session in queryset.filter(status="open"):
            try:
                session.close_session(session.calculated_balance)
            except Exception as e:
                self.message_user(
                    request, f"Error closing session {session}: {str(e)}", level="ERROR"
                )

    @admin.action(description=_("Suspend selected sessions"))
    def suspend_selected_sessions(self, request, queryset):
        for session in queryset.filter(status="open"):
            try:
                session.suspend_session()
            except Exception as e:
                self.message_user(
                    request,
                    f"Error suspending session {session}: {str(e)}",
                    level="ERROR",
                )

    @admin.action(description=_("Resume selected sessions"))
    def resume_selected_sessions(self, request, queryset):
        for session in queryset.filter(status="suspended"):
            try:
                session.resume_session()
            except Exception as e:
                self.message_user(
                    request,
                    f"Error resuming session {session}: {str(e)}",
                    level="ERROR",
                )


@admin.register(POSSessionTransaction)
class POSSessionTransactionAdmin(admin.ModelAdmin):
    list_display = (
        "transaction_type_display",
        "amount_display",
        "session_link",
        "created",
    )
    list_filter = ("transaction_type", "created")
    search_fields = ("description", "session__pos__name")
    readonly_fields = ("created", "modified", "session_link", "related_object_link")
    fieldsets = (
        (
            "Transaction Details",
            {"fields": ("session_link", "transaction_type", "amount", "description")},
        ),
        (
            "Related Object",
            {"fields": ("related_object_link",), "classes": ("collapse",)},
        ),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def transaction_type_display(self, obj):
        type_colors = {
            "sale": "green",
            "refund": "red",
            "purchase": "blue",
            "refund_purchase": "orange",
            "cash_in": "darkgreen",
            "cash_out": "darkred",
            "expense": "purple",
            "salary": "brown",
            "other": "gray",
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            type_colors.get(obj.transaction_type, "black"),
            obj.get_transaction_type_display(),
        )

    transaction_type_display.short_description = _("Type")
    transaction_type_display.admin_order_field = "transaction_type"

    def amount_display(self, obj):
        color = (
            "red"
            if obj.transaction_type
            in ["refund", "purchase", "cash_out", "expense", "salary"]
            else "green"
        )
        return format_html('<span style="color: {};">{}</span>', color, obj.amount)

    amount_display.short_description = _("Amount")
    amount_display.admin_order_field = "amount"

    def session_link(self, obj):
        url = reverse("admin:pos_possession_change", args=[obj.session.id])
        return format_html('<a href="{}">{}</a>', url, obj.session)

    session_link.short_description = _("Session")

    def related_object_link(self, obj):
        if obj.content_type and obj.object_id:
            try:
                model = obj.content_type.model_class()
                if model:
                    related_obj = model.objects.get(pk=obj.object_id)
                    url = reverse(
                        f"admin:{obj.content_type.app_label}_{obj.content_type.model}_change",
                        args=[obj.object_id],
                    )
                    return format_html('<a href="{}">{}</a>', url, str(related_obj))
            except:
                pass
        return "—"

    related_object_link.short_description = _("Related Object")
