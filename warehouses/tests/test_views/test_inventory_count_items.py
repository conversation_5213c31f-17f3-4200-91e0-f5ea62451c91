from decimal import Decimal

from django.urls import reverse
from django.urls.exceptions import NoReverseMatch
from rest_framework import status

from utils.test.base_test import BaseTestCase
from utils.test.factories.warehouse.inventory_count import InventoryCountFactory
from utils.test.factories.warehouse.inventory_count_item import (
    InventoryCountItemFactory,
    NoDiscrepancyInventoryCountItemFactory,
    ShortageInventoryCountItemFactory,
    SurplusInventoryCountItemFactory,
)
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models import InventoryCount, InventoryCountItem


class InventoryCountItemViewSetTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        # Create test data

        self.warehouse2 = WarehouseFactory()

        self.inventory_count = InventoryCountFactory(
            warehouse=self.warehouse, status=InventoryCount.Status.IN_PROGRESS
        )
        self.inventory_count2 = InventoryCountFactory(
            warehouse=self.warehouse2, status=InventoryCount.Status.IN_PROGRESS
        )
        # Create items with different discrepancy statuses
        self.no_discrepancy_item = NoDiscrepancyInventoryCountItemFactory(
            inventory_count=self.inventory_count
        )
        self.shortage_item = ShortageInventoryCountItemFactory(
            inventory_count=self.inventory_count
        )
        self.surplus_item = SurplusInventoryCountItemFactory(
            inventory_count=self.inventory_count
        )

        self.no_discrepancy_item2 = NoDiscrepancyInventoryCountItemFactory(
            inventory_count=self.inventory_count2
        )
        self.shortage_item2 = ShortageInventoryCountItemFactory(
            inventory_count=self.inventory_count2
        )
        self.surplus_item2 = SurplusInventoryCountItemFactory(
            inventory_count=self.inventory_count2
        )

        self.url = reverse(
            "warehouses:inventory-count-items-list",
            kwargs={"inventory_count_pk": self.inventory_count.id},
        )

        self.url2 = reverse(
            "warehouses:inventory-count-items-list",
            kwargs={"inventory_count_pk": self.inventory_count2.id},
        )

    def test_list_inventory_count_items_admin(self):
        """Admin should be able to see all items."""
        response = self.admin_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

        response = self.admin_client.get(self.url2)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

    def test_list_inventory_count_items_manager(self):
        """Manager should only see items with unrecorded quantities."""
        # Create an item with recorded quantity
        recorded_item = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )

        response = self.manager_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should only see items with recorded_quantity__isnull=True
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], recorded_item.id)

        # Record quantity for all items
        InventoryCountItem.objects.filter(inventory_count=self.inventory_count).update(
            recorded_quantity=10
        )

        response = self.manager_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data["results"]), 0
        )  # No items with null recorded_quantity

    def test_list_inventory_count_items_cashier(self):
        """Cashier should not be able to see items."""
        response = self.cashier_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_detail_inventory_count_item(self):
        """Test retrieving a single inventory count item."""
        with self.assertRaises(NoReverseMatch):
            reverse(
                "warehouses:inventory-count-items-detail",
                kwargs={
                    "inventory_count_pk": self.inventory_count.id,
                    "pk": self.no_discrepancy_item.id,
                },
            )

    def test_create_inventory_count_item(self):
        """Test retrieving a single inventory count item."""
        response = self.admin_client.post(self.url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_bulk_update_items_with_items_already_recorder(self):
        """Admin should be able to bulk update items."""
        url = f"{self.url}bulk_update/"
        quantity1 = self.no_discrepancy_item.recorded_quantity
        quantity2 = self.shortage_item.recorded_quantity
        data = [
            {
                "id": self.no_discrepancy_item.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": self.shortage_item.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["error"],
            "One or more items not found in this inventory count or recorded quantity set before",
        )
        # Verify no updates
        self.no_discrepancy_item.refresh_from_db()
        self.shortage_item.refresh_from_db()
        self.assertEqual(self.no_discrepancy_item.recorded_quantity, quantity1)
        self.assertEqual(self.shortage_item.recorded_quantity, quantity2)

    def test_bulk_update_items_draft_inventory_count_admin(self):
        """Admin should be able to bulk update items."""
        self.inventory_count.status = InventoryCount.Status.DRAFT
        self.inventory_count.save(update_fields=["status"])
        url = f"{self.url}bulk_update/"
        recorded_item1 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        recorded_item2 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        # if this record updated this mean that inventory count will be completed
        recorded_item3 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        stock_quantity1 = recorded_item1.stock_item.quantity
        stock_quantity2 = recorded_item2.stock_item.quantity
        data = [
            {
                "id": recorded_item1.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": recorded_item2.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.inventory_count.refresh_from_db()
        self.assertEqual(self.inventory_count.status, InventoryCount.Status.IN_PROGRESS)
        # Verify  updates
        recorded_item1.refresh_from_db()
        recorded_item2.refresh_from_db()
        self.assertEqual(recorded_item1.recorded_quantity, Decimal("15"))
        self.assertEqual(recorded_item2.recorded_quantity, Decimal("20"))
        self.assertEqual(recorded_item1.system_quantity, stock_quantity1)
        self.assertEqual(recorded_item2.system_quantity, stock_quantity2)
        self.assertEqual(
            recorded_item1.stock_item.quantity, recorded_item1.recorded_quantity
        )
        self.assertEqual(
            recorded_item2.stock_item.quantity, recorded_item2.recorded_quantity
        )

    def test_bulk_update_items_draft_inventory_count_manager(self):
        """Admin should be able to bulk update items."""
        self.inventory_count.status = InventoryCount.Status.DRAFT
        self.inventory_count.save(update_fields=["status"])
        url = f"{self.url}bulk_update/"
        recorded_item1 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        recorded_item2 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        # if this record updated this mean that inventory count will be completed
        recorded_item3 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        stock_quantity1 = recorded_item1.stock_item.quantity
        stock_quantity2 = recorded_item2.stock_item.quantity
        data = [
            {
                "id": recorded_item1.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": recorded_item2.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.inventory_count.refresh_from_db()
        self.assertEqual(self.inventory_count.status, InventoryCount.Status.IN_PROGRESS)
        # Verify  updates
        recorded_item1.refresh_from_db()
        recorded_item2.refresh_from_db()
        self.assertEqual(recorded_item1.recorded_quantity, Decimal("15"))
        self.assertEqual(recorded_item2.recorded_quantity, Decimal("20"))
        self.assertEqual(recorded_item1.system_quantity, stock_quantity1)
        self.assertEqual(recorded_item2.system_quantity, stock_quantity2)
        self.assertEqual(
            recorded_item1.stock_item.quantity, recorded_item1.recorded_quantity
        )
        self.assertEqual(
            recorded_item2.stock_item.quantity, recorded_item2.recorded_quantity
        )

    def test_bulk_update_items_cashier(self):
        """Admin should be able to bulk update items."""
        self.inventory_count.status = InventoryCount.Status.DRAFT
        self.inventory_count.save(update_fields=["status"])
        url = f"{self.url}bulk_update/"
        recorded_item1 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        recorded_item2 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )

        data = [
            {
                "id": recorded_item1.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": recorded_item2.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.cashier_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_bulk_update_items_from_in_progress_to_completed_inventory_count_admin(
        self,
    ):
        """Admin should be able to bulk update items."""
        self.inventory_count.status = InventoryCount.Status.IN_PROGRESS
        self.inventory_count.save(update_fields=["status"])
        url = f"{self.url}bulk_update/"
        recorded_item1 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        recorded_item2 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        stock_quantity1 = recorded_item1.stock_item.quantity
        stock_quantity2 = recorded_item2.stock_item.quantity
        data = [
            {
                "id": recorded_item1.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": recorded_item2.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.inventory_count.refresh_from_db()
        self.assertEqual(self.inventory_count.status, InventoryCount.Status.COMPLETED)
        self.assertFalse(
            self.inventory_count.items.filter(recorded_quantity__isnull=True).exists()
        )
        # Verify  updates
        recorded_item1.refresh_from_db()
        recorded_item2.refresh_from_db()
        self.assertEqual(recorded_item1.recorded_quantity, Decimal("15"))
        self.assertEqual(recorded_item2.recorded_quantity, Decimal("20"))
        self.assertEqual(recorded_item1.system_quantity, stock_quantity1)
        self.assertEqual(recorded_item2.system_quantity, stock_quantity2)
        self.assertEqual(
            recorded_item1.stock_item.quantity, recorded_item1.recorded_quantity
        )
        self.assertEqual(
            recorded_item2.stock_item.quantity, recorded_item2.recorded_quantity
        )
        cost = 0
        price = 0
        for item in self.inventory_count.items.all():
            cost += item.stock_item.product.cost * item.difference
            price += item.stock_item.product.price * item.difference
        self.assertEqual(
            self.inventory_count.total_cost, cost.quantize(Decimal("0.01"))
        )
        self.assertEqual(
            self.inventory_count.total_price, price.quantize(Decimal("0.01"))
        )

    def test_bulk_update_items_with_items_for_draft_inventory_count_manager(self):
        """Admin should be able to bulk update items."""
        self.inventory_count.status = InventoryCount.Status.DRAFT
        self.inventory_count.save(update_fields=["status"])
        url = f"{self.url}bulk_update/"
        recorded_item1 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        recorded_item2 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        # if this record updated this mean that inventory count will be completed
        recorded_item3 = InventoryCountItemFactory(
            inventory_count=self.inventory_count,
            recorded_quantity=None,
            system_quantity=None,
            difference=None,
        )
        stock_quantity1 = recorded_item1.stock_item.quantity
        stock_quantity2 = recorded_item2.stock_item.quantity
        data = [
            {
                "id": recorded_item1.id,
                "recorded_quantity": 15,
                "notes": "Updated quantity",
            },
            {
                "id": recorded_item2.id,
                "recorded_quantity": 20,
                "notes": "Updated shortage",
            },
        ]
        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.inventory_count.refresh_from_db()
        self.assertEqual(self.inventory_count.status, InventoryCount.Status.IN_PROGRESS)
        # Verify  updates
        recorded_item1.refresh_from_db()
        recorded_item2.refresh_from_db()
        self.assertEqual(recorded_item1.recorded_quantity, Decimal("15"))
        self.assertEqual(recorded_item2.recorded_quantity, Decimal("20"))
        self.assertEqual(recorded_item1.system_quantity, stock_quantity1)
        self.assertEqual(recorded_item2.system_quantity, stock_quantity2)
        self.assertEqual(
            recorded_item1.stock_item.quantity, recorded_item1.recorded_quantity
        )
        self.assertEqual(
            recorded_item2.stock_item.quantity, recorded_item2.recorded_quantity
        )

    def test_unauthorized_access(self):
        """Unauthenticated users should not have access."""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
