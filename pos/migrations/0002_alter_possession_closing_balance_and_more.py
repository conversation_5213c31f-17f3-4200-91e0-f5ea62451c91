# Generated by Django 5.2.1 on 2025-07-19 11:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("pos", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="possession",
            name="closing_balance",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Amount of money at the end of the session",
                max_digits=12,
                null=True,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="closing balance",
            ),
        ),
        migrations.AlterField(
            model_name="possession",
            name="difference",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total difference between calculated balance and closing balance",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="difference",
            ),
        ),
        migrations.AlterField(
            model_name="possession",
            name="opening_balance",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Amount of money at the beginning of the session",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="opening balance",
            ),
        ),
        migrations.AlterField(
            model_name="possession",
            name="total_expenses",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total expenses during the session",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="total expenses",
            ),
        ),
        migrations.AlterField(
            model_name="possession",
            name="total_sales",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total sales amount during the session",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="total sales",
            ),
        ),
        migrations.AlterField(
            model_name="possessiontransaction",
            name="amount",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Transaction amount",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="amount",
            ),
        ),
    ]
