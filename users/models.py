import re

from django.contrib.auth.models import Abstract<PERSON><PERSON>, BaseUserManager
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _


def validate_egyptian_phone_number(value):
    """
    Validate that the phone number is a valid Egyptian number.
    Valid formats:
    - 01XXXXXXXXX (11 digits starting with 01)
    - +201XXXXXXXX (12 digits starting with +20)
    - 00201XXXXXXXX (13 digits starting with 002)
    """
    pattern = r"^(\+20|0020|0)?1[0-9]{9}$"
    if not re.match(pattern, value):
        raise ValidationError(
            _(
                "%(value)s is not a valid Egyptian phone number. It should be 11 digits starting with 01"
            ),
            params={"value": value},
        )


class UserManager(BaseUserManager):
    """Custom user model manager where email is the unique identifier"""

    def _create_user(self, email, phone_number, password, **extra_fields):
        """Create and save a user with the given email and password."""
        email = self.normalize_email(email) if email else None
        user = self.model(email=email, phone_number=phone_number, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email=None, phone_number=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(email, phone_number, password, **extra_fields)

    def create_superuser(self, email, phone_number, password=None, **extra_fields):
        """Create and save a SuperUser with the given email and password."""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        extra_fields.setdefault("role", "admin")

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(email, phone_number, password, **extra_fields)


class User(AbstractUser):
    """Custom user model that supports using email or phone number as the username field."""

    objects = UserManager()

    class Role(models.TextChoices):
        ADMIN = "admin", _("Admin")
        MANAGER = "manager", _("Manager")
        CASHIER = "cashier", _("Cashier")

    username = None
    email = models.EmailField(
        _("email address"),
        unique=True,
        blank=False,
        null=False,
        help_text=_("Required. Enter a valid email address."),
    )
    phone_number = models.CharField(
        _("phone number"),
        max_length=20,
        unique=True,
        validators=[validate_egyptian_phone_number],
        help_text=_("Enter a valid Egyptian phone number (e.g., 01234567890)"),
    )
    role = models.CharField(
        _("role"),
        max_length=10,
        choices=Role.choices,
        default=Role.CASHIER,
        help_text=_("User role in the system"),
    )

    REQUIRED_FIELDS = ["email"]
    USERNAME_FIELD = "phone_number"

    def __str__(self):
        return self.email

    @property
    def is_admin(self):
        return self.role == self.Role.ADMIN or self.is_superuser

    @property
    def is_manager(self):
        return self.role == self.Role.MANAGER

    @property
    def is_cashier(self):
        return self.role == self.Role.CASHIER

    @property
    def name(self):
        return f"{self.first_name} {self.last_name}" if self.first_name else self.email
