import api from './api';

/**
 * Products API service
 * Handles all API calls related to products CRUD operations
 */
export const productsService = {
    /**
     * Get all products with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.category - Filter by category
     * @param {string} params.unit_type - Filter by unit type
     * @returns {Promise} API response
     */
    getProducts: (params = {}) => {
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        
        // Add search parameter
        if (params.search) queryParams.append('search', params.search);
        
        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        // Add filter parameters
        if (params.category) queryParams.append('category', params.category);
        if (params.unit_type) queryParams.append('unit_type', params.unit_type);
        
        const queryString = queryParams.toString();
        const url = `/products/${queryString ? `?${queryString}` : ''}`;
        
        return api.get(url);
    },

    /**
     * Get a single product by ID
     * @param {number} id - Product ID
     * @returns {Promise} API response
     */
    getProduct: (id) => {
        return api.get(`/products/${id}/`);
    },

    /**
     * Create a new product
     * @param {Object} data - Product data
     * @param {string} data.name - Product name
     * @param {string} data.description - Product description
     * @param {number} data.cost - Product cost price
     * @param {number} data.price - Product selling price
     * @param {string} data.barcode - Product barcode
     * @param {string} data.unit_type - Product unit type
     * @param {number} data.category_id - Category ID
     * @param {File} data.image - Product image file
     * @returns {Promise} API response
     */
    createProduct: (data) => {
        // Create FormData for file upload support
        const formData = new FormData();
        
        // Add all fields to FormData
        Object.keys(data).forEach(key => {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                formData.append(key, data[key]);
            }
        });

        return api.post('/products/', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Update an existing product
     * @param {number} id - Product ID
     * @param {Object} data - Updated product data
     * @returns {Promise} API response
     */
    updateProduct: (id, data) => {
        // Create FormData for file upload support
        const formData = new FormData();
        
        // Add all fields to FormData
        Object.keys(data).forEach(key => {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                formData.append(key, data[key]);
            }
        });

        return api.put(`/products/${id}/`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Delete a product
     * @param {number} id - Product ID
     * @returns {Promise} API response
     */
    deleteProduct: (id) => {
        return api.delete(`/products/${id}/`);
    },

    /**
     * Get unit type choices
     * @returns {Array} Array of unit type options
     */
    getUnitTypes: () => {
        return [
            { value: 'piece', label: 'Piece' },
            { value: 'kg', label: 'Kilogram' },
            { value: 'g', label: 'Gram' },
            { value: 'l', label: 'Liter' },
            { value: 'ml', label: 'Milliliter' }
        ];
    }
};

export default productsService;
