from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from warehouses.models import Warehouse


class POS(TimeStampedModel):
    """
    Point of Sale (POS) model representing a physical point of sale location.
    Each POS is associated with a specific warehouse.
    """

    warehouse = models.OneToOneField(
        Warehouse,
        on_delete=models.CASCADE,
        related_name="pos",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse this POS terminal is associated with"),
    )

    name = models.CharField(
        _("name"), max_length=100, help_text=_("Name of the POS terminal"), unique=True
    )

    description = models.TextField(
        _("description"),
        blank=True,
        null=True,
        help_text=_("Additional information about the POS terminal"),
    )

    class Meta:
        verbose_name = _("point of sale")
        verbose_name_plural = _("points of sale")
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} ({self.warehouse.name})"
