from django.test import Client, TestCase
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from employees.models.employee import Employee
from users.models import User
from utils.test.factories.employee.employee import EmployeeFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.user import UserFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class BaseTestCase(TestCase):
    """
    Base test case class that provides common setup for all test cases.
    Creates three types of users: admin, manager, and cashier with their respective permissions.
    """

    @classmethod
    def setUpTestData(cls):
        """Set up test data for all test methods."""
        super().setUpTestData()

        # Create admin user
        cls.admin_user = UserFactory.create(
            email="<EMAIL>",
            is_staff=True,
            is_superuser=True,
            role=User.Role.ADMIN,
        )
        admin_refresh = RefreshToken.for_user(cls.admin_user)
        cls.admin_access_token = str(admin_refresh.access_token)
        cls.warehouse = WarehouseFactory.create(name="Test Warehouse")
        cls.pos = POSFactory.create(warehouse=cls.warehouse)
        # Create manager user
        cls.manager_user = UserFactory.create(
            email="<EMAIL>",
            is_staff=False,
            is_superuser=False,
            role=User.Role.MANAGER,
        )
        cls.manager_employee = EmployeeFactory.create(
            user=cls.manager_user,
            type=Employee.EmployeeType.FULL_TIME,
            status=Employee.Status.ACTIVE,
            pos=cls.pos,
        )
        manager_refresh = RefreshToken.for_user(cls.manager_user)
        cls.manager_access_token = str(manager_refresh.access_token)

        # Create cashier user
        cls.cashier_user = UserFactory.create(
            email="<EMAIL>",
            is_staff=False,
            is_superuser=False,
            role=User.Role.CASHIER,
        )
        cls.cashier_employee = EmployeeFactory.create(
            user=cls.cashier_user,
            type=Employee.EmployeeType.PART_TIME,
            status=Employee.Status.ACTIVE,
            pos=cls.pos,
        )
        cashier_refresh = RefreshToken.for_user(cls.cashier_user)
        cls.cashier_access_token = str(cashier_refresh.access_token)

        # Create API clients for each user
        cls.admin_client = APIClient()
        cls.admin_client.credentials(
            HTTP_AUTHORIZATION=f"Bearer {cls.admin_access_token}"
        )

        cls.manager_client = APIClient()
        cls.manager_client.credentials(
            HTTP_AUTHORIZATION=f"Bearer {cls.manager_access_token}"
        )

        cls.cashier_client = APIClient()
        cls.cashier_client.credentials(
            HTTP_AUTHORIZATION=f"Bearer {cls.cashier_access_token}"
        )

        # Regular unauthenticated client
        cls.client = Client()

        # Add helper methods for getting tokens
        cls.get_admin_token = lambda: str(
            RefreshToken.for_user(cls.admin_user).access_token
        )
        cls.get_manager_token = lambda: str(
            RefreshToken.for_user(cls.manager_user).access_token
        )
        cls.get_cashier_token = lambda: str(
            RefreshToken.for_user(cls.cashier_user).access_token
        )
