from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from purchases.models.purchase import Purchase


class PurchaseItem(TimeStampedModel):
    """
    Represents an item within a purchase.
    Each purchase item is associated with a purchase and product, and includes
    details such as quantity, unit cost, and total cost.
    """

    purchase = models.ForeignKey(
        Purchase,
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name=_("purchase"),
        help_text=_("The purchase this item belongs to"),
    )

    product = models.ForeignKey(
        "products.Product",
        on_delete=models.PROTECT,
        related_name="purchase_items",
        verbose_name=_("product"),
        help_text=_("The product being purchased"),
    )

    quantity = models.DecimalField(
        _("quantity"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Quantity of the product"),
        default=1,
        validators=[MinValueValidator(0.01)],
    )

    unit_cost = models.DecimalField(
        _("unit cost"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Cost per unit of the product"),
        validators=[MinValueValidator(0.00)],
    )
    unit_price = models.DecimalField(
        _("unit price"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Price per unit of the product"),
        validators=[MinValueValidator(0.00)],
    )
    total_cost = models.DecimalField(
        _("total cost"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Total cost (quantity × unit_cost)"),
        validators=[MinValueValidator(0.00)],
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this purchase item"),
    )

    class Meta:
        verbose_name = _("purchase item")
        verbose_name_plural = _("purchase items")
        ordering = ["-created"]

    def __str__(self):
        return f"{self.quantity} x {self.product.name} - {self.total_cost}"

    def save(self, *args, **kwargs):
        # Calculate total_cost before saving if not explicitly set
        if self.pk is None and self.total_cost is None:
            self.total_cost = self.unit_cost * self.quantity
        super().save(*args, **kwargs)
