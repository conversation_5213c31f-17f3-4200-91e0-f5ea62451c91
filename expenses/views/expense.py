from django_filters.rest_framework import <PERSON>jango<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import status
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet, mixins

from expenses.models import Expense
from expenses.serializers import ExpenseRead<PERSON>erializer, ExpenseWriteSerializer
from pos.models import POSSession, POSSessionTransaction, TransactionType
from utils.pagination import PaginationClass


class ExpenseViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
):
    """
    ViewSet for managing expenses.
    - Admin/Manager: Can perform all operations
    - Cashier: Can only create expenses which will be linked to their active POS session
    """

    queryset = Expense.objects.all()
    permission_classes = [IsAuthenticated]
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["expense_category"]
    search_fields = ["description"]
    ordering_fields = ["created", "amount"]
    ordering = ["-created"]

    def get_serializer_class(self):
        if self.action == "create":
            return ExpenseWriteSerializer
        return ExpenseReadSerializer

    def get_queryset(self):
        if self.request.user.is_authenticated and self.request.user.is_admin:
            return self.queryset.order_by("-created")
        return self.queryset.filter(
            pos_session_transaction__session__pos__employee__user=self.request.user
        ).order_by("-created")

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        pos_session_id = request.data.get("pos_session_id")
        if not pos_session_id and not request.user.is_admin:
            return Response(
                {"detail": "POS session is required for non-admin users."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # Get the active POS session for cashiers
        pos_session = None
        if pos_session_id is not None:
            if not request.user.is_admin:
                pos_session = POSSession.objects.filter(
                    id=pos_session_id,
                    status=POSSession.Status.OPEN,
                    pos=request.user.employee.pos,
                ).first()
            else:
                pos_session = POSSession.objects.filter(
                    id=pos_session_id, status=POSSession.Status.OPEN
                ).first()
            # if you are not admin you can't create expense without pos session
            if not pos_session:
                return Response(
                    {"detail": "No active POS session found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        # Create the expense
        expense = serializer.save()
        # if admin creates expense without pos session it will not create transaction
        if not request.user.is_admin or pos_session:
            movement_data = {
                "session": pos_session,
                "amount": expense.amount,
                "transaction_type": TransactionType.EXPENSE,
                "related_object": expense,
                "description": f"Expense: {expense.expense_category.name}",
            }
            transaction = POSSessionTransaction.objects.create(**movement_data)
            # Link the transaction to the expense
            expense.pos_session_transaction = transaction
            expense.save(update_fields=["pos_session_transaction"])
        return Response(
            ExpenseReadSerializer(expense).data,
            status=status.HTTP_201_CREATED,
        )
