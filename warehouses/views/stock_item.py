from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON><PERSON>
from rest_framework import status, viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.response import Response

from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly
from ..models import StockItem
from ..serializers.stock_item import StockItemSerializer


class StockItemViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows stock items to be viewed or edited.
    Supports filtering by warehouse through the URL path: /warehouses/<warehouse_id>/stock-items/
    """

    serializer_class = StockItemSerializer
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["warehouse", "product", "quantity"]
    search_fields = ["product__name", "warehouse__name"]
    ordering_fields = ["product__name", "warehouse__name", "quantity", "min_stock"]
    ordering = ["warehouse__name", "product__name"]
    permission_classes = [IsAdminOnly]

    def get_queryset(self):
        queryset = StockItem.objects.select_related("warehouse", "product")
        # Filter by warehouse_id from URL if provided
        warehouse_id = self.kwargs.get("warehouse_id")
        if warehouse_id is not None:
            queryset = queryset.filter(warehouse_id=warehouse_id)
        return queryset

    def create(self, request, *args, **kwargs):
        form_data = request.data | {"warehouse": kwargs["warehouse_id"]}
        serializer = self.get_serializer(data=form_data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        form_data = request.data | {"warehouse": kwargs["warehouse_id"]}
        serializer = self.get_serializer(instance, data=form_data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)
