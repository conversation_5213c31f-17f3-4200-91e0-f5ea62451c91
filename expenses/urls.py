from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from expenses.views.expense import ExpenseViewSet
from expenses.views.expense_category import ExpenseCategoryViewSet

router = DefaultRouter()
router.register(
    r"expense/categories", ExpenseCategoryViewSet, basename="expense-category"
)
router.register(r"expense", ExpenseViewSet, basename="expense")

urlpatterns = [
    path("", include(router.urls)),
]
