from rest_framework import serializers

from ..models import AccountTransaction


class AccountTransactionSerializer(serializers.ModelSerializer):
    session_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = AccountTransaction
        fields = [
            "id",
            "balance",
            "type",
            "amount",
            "description",
            "related_content_type",
            "related_object_id",
            "session_id",
            "created",
        ]
        read_only_fields = ["created"]

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        return value
