import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    <PERSON>,
    Spinner,
    Table,
    Badge,
    Pagination
} from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { posService } from '../../services/posService';
import { format } from 'date-fns';
import { FaArrowLeft, FaPlay, FaPause, FaStop, FaPlus } from 'react-icons/fa';
import SessionStatusBadge from './SessionStatusBadge';
import TransactionTypeBadge from './TransactionTypeBadge';

/**
 * POSSessionDetail component
 * Shows session details and transactions
 */
const POSSessionDetail = () => {
    const navigate = useNavigate();
    const { sessionId } = useParams();
    const { currentUser } = useAuth();
    
    const [session, setSession] = useState(null);
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [transactionsLoading, setTransactionsLoading] = useState(false);
    const [error, setError] = useState('');
    const [actionLoading, setActionLoading] = useState(null);
    
    // Pagination for transactions
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize] = useState(10);

    // Fetch session details
    const fetchSessionDetails = async () => {
        try {
            setLoading(true);
            setError('');
            
            const response = await posService.getSessionDetails(sessionId);
            setSession(response.data);
            
        } catch (err) {
            console.error('Error fetching session details:', err);
            setError('Failed to load session details. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Fetch transactions
    const fetchTransactions = async (page = 1) => {
        try {
            setTransactionsLoading(true);
            
            const params = {
                page,
                page_size: pageSize,
                ordering: '-created'
            };
            
            const response = await posService.getSessionTransactions(sessionId, params);
            
            setTransactions(response.data.results || []);
            setTotalCount(response.data.count || 0);
            setTotalPages(Math.ceil((response.data.count || 0) / pageSize));
            
        } catch (err) {
            console.error('Error fetching transactions:', err);
            setError('Failed to load transactions. Please try again.');
        } finally {
            setTransactionsLoading(false);
        }
    };

    // Handle session actions
    const handleSessionAction = async (action) => {
        try {
            setActionLoading(action);
            setError('');
            
            let response;
            switch (action) {
                case 'suspend':
                    response = await posService.suspendSession(sessionId);
                    break;
                case 'resume':
                    response = await posService.resumeSession(sessionId);
                    break;
                case 'close':
                    // Navigate to close session form
                    navigate(`/pos/sessions/${sessionId}/close`);
                    return;
                default:
                    throw new Error('Unknown action');
            }
            
            // Refresh session details
            fetchSessionDetails();
            
        } catch (err) {
            console.error(`Error ${action} session:`, err);
            setError(`Failed to ${action} session. Please try again.`);
        } finally {
            setActionLoading(null);
        }
    };

    // Handle page change for transactions
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchTransactions(page);
    };

    // Format currency
    const formatCurrency = (amount) => {
        if (amount === null || amount === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'PPpp');
        } catch (error) {
            return 'Invalid Date';
        }
    };

    // Initial load
    useEffect(() => {
        if (sessionId) {
            fetchSessionDetails();
            fetchTransactions();
        }
    }, [sessionId]);

    // Render pagination
    const renderPagination = () => {
        if (totalPages <= 1) return null;

        const items = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return <Pagination className="justify-content-center">{items}</Pagination>;
    };

    if (loading) {
        return (
            <Container>
                <div className="text-center p-4">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            </Container>
        );
    }

    if (!session) {
        return (
            <Container>
                <Alert variant="danger">
                    Session not found or you don't have permission to view it.
                </Alert>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/pos/sessions')}
                                className="me-3"
                            >
                                <FaArrowLeft />
                            </Button>
                            <h2>Session #{session.id}</h2>
                        </div>
                        <div className="d-flex gap-2">
                            {session.status === 'open' && (
                                <>
                                    <Button
                                        variant="warning"
                                        onClick={() => handleSessionAction('suspend')}
                                        disabled={actionLoading}
                                    >
                                        {actionLoading === 'suspend' ? (
                                            <Spinner as="span" animation="border" size="sm" className="me-2" />
                                        ) : (
                                            <FaPause className="me-2" />
                                        )}
                                        Suspend
                                    </Button>
                                    <Button
                                        variant="danger"
                                        onClick={() => handleSessionAction('close')}
                                        disabled={actionLoading}
                                    >
                                        <FaStop className="me-2" />
                                        Close Session
                                    </Button>
                                </>
                            )}
                            {session.status === 'suspended' && (
                                <Button
                                    variant="success"
                                    onClick={() => handleSessionAction('resume')}
                                    disabled={actionLoading}
                                >
                                    {actionLoading === 'resume' ? (
                                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                                    ) : (
                                        <FaPlay className="me-2" />
                                    )}
                                    Resume
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Session Details */}
            <Row className="mb-4">
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Session Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="row mb-2">
                                <div className="col-sm-4"><strong>POS Terminal:</strong></div>
                                <div className="col-sm-8">{session.pos_name}</div>
                            </div>
                            <div className="row mb-2">
                                <div className="col-sm-4"><strong>User:</strong></div>
                                <div className="col-sm-8">{session.user_name}</div>
                            </div>
                            <div className="row mb-2">
                                <div className="col-sm-4"><strong>Status:</strong></div>
                                <div className="col-sm-8">
                                    <SessionStatusBadge status={session.status} />
                                </div>
                            </div>
                            <div className="row mb-2">
                                <div className="col-sm-4"><strong>Opened:</strong></div>
                                <div className="col-sm-8">{formatDate(session.opened_at)}</div>
                            </div>
                            {session.closed_at && (
                                <div className="row mb-2">
                                    <div className="col-sm-4"><strong>Closed:</strong></div>
                                    <div className="col-sm-8">{formatDate(session.closed_at)}</div>
                                </div>
                            )}
                            {session.notes && (
                                <div className="row mb-2">
                                    <div className="col-sm-4"><strong>Notes:</strong></div>
                                    <div className="col-sm-8">{session.notes}</div>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Financial Summary</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="row mb-2">
                                <div className="col-sm-6"><strong>Opening Balance:</strong></div>
                                <div className="col-sm-6">{formatCurrency(session.opening_balance)}</div>
                            </div>
                            {session.closing_balance !== null && (
                                <div className="row mb-2">
                                    <div className="col-sm-6"><strong>Closing Balance:</strong></div>
                                    <div className="col-sm-6">{formatCurrency(session.closing_balance)}</div>
                                </div>
                            )}
                            <div className="row mb-2">
                                <div className="col-sm-6"><strong>Total Sales:</strong></div>
                                <div className="col-sm-6 text-success">{formatCurrency(session.total_sales)}</div>
                            </div>
                            <div className="row mb-2">
                                <div className="col-sm-6"><strong>Total Expenses:</strong></div>
                                <div className="col-sm-6 text-danger">{formatCurrency(session.total_expenses)}</div>
                            </div>
                            <div className="row mb-2">
                                <div className="col-sm-6"><strong>Calculated Balance:</strong></div>
                                <div className="col-sm-6">
                                    <strong>{formatCurrency(session.calculated_balance)}</strong>
                                </div>
                            </div>
                            {session.difference !== null && session.difference !== 0 && (
                                <div className="row mb-2">
                                    <div className="col-sm-6"><strong>Difference:</strong></div>
                                    <div className={`col-sm-6 ${session.difference > 0 ? 'text-success' : 'text-danger'}`}>
                                        <strong>{formatCurrency(session.difference)}</strong>
                                    </div>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Transactions */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <div className="d-flex justify-content-between align-items-center">
                                <h5 className="mb-0">Transactions ({totalCount})</h5>
                                {session.status === 'open' && (
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={() => navigate(`/pos/sessions/${sessionId}/transactions/create`)}
                                    >
                                        <FaPlus className="me-2" />
                                        Add Transaction
                                    </Button>
                                )}
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {transactionsLoading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading transactions...</span>
                                    </Spinner>
                                </div>
                            ) : transactions.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted mb-0">No transactions found for this session.</p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                        <tr>
                                            <th>ID</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {transactions.map((transaction) => (
                                            <tr key={transaction.id}>
                                                <td>#{transaction.id}</td>
                                                <td>
                                                    <TransactionTypeBadge type={transaction.transaction_type} />
                                                </td>
                                                <td>
                                                    <span className={
                                                        ['sale', 'cash_in', 'refund_purchase'].includes(transaction.transaction_type)
                                                            ? 'text-success' : 'text-danger'
                                                    }>
                                                        {formatCurrency(transaction.amount)}
                                                    </span>
                                                </td>
                                                <td>{transaction.description || 'No description'}</td>
                                                <td>{formatDate(transaction.created)}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Pagination */}
            {renderPagination()}
        </Container>
    );
};

export default POSSessionDetail;
