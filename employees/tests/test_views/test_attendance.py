import os
import shutil
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from employees.models import Attendance, Employee, Salary
from users.models import User
from utils.test.base_test import BaseTestCase
from utils.test.factories.employee.employee import EmployeeFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class AttendanceViewSetTestCase(BaseTestCase):
    """
    Test cases for AttendanceViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("employees:attendance-list")
        self.detail_url_name = "employees:attendance-detail"
        self.time_in_url = reverse("employees:attendance-time-in")
        self.time_out_url = reverse("employees:attendance-time-out")

        # Create additional test data
        self.test_warehouse = WarehouseFactory.create(name="Test Warehouse 2")
        self.test_pos = POSFactory.create(
            warehouse=self.test_warehouse, name="Test POS 2"
        )

        # Create test employees for attendance
        self.test_employee1 = EmployeeFactory.create(
            user__first_name="John",
            user__last_name="Doe",
            user__email="<EMAIL>",
            user__phone_number="01234567890",
            user__role=User.Role.CASHIER,
            hour_rate=Decimal("25.00"),
            day_rate=Decimal("200.00"),
            status=Employee.Status.ACTIVE,
            pos=self.pos,
        )

        self.test_employee2 = EmployeeFactory.create(
            user__first_name="Jane",
            user__last_name="Smith",
            user__email="<EMAIL>",
            user__phone_number="01234567891",
            user__role=User.Role.MANAGER,
            hour_rate=Decimal("30.00"),
            day_rate=Decimal("240.00"),
            status=Employee.Status.ACTIVE,
            pos=self.test_pos,
        )

        # Create test attendance records
        self.attendance1 = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=8),
            time_out=timezone.now() - timedelta(hours=1),
            notes="Test attendance 1",
        )

        self.attendance2 = Attendance.objects.create(
            employee=self.test_employee2,
            time_in=timezone.now() - timedelta(hours=6),
            notes="Test attendance 2 - still active",
        )

    def tearDown(self):
        """Clean up uploaded files after each test."""
        super().tearDown()

        # Clean up media files created during tests
        media_root = settings.MEDIA_ROOT
        attendance_dir = os.path.join(media_root, "attendance")

        if os.path.exists(attendance_dir):
            shutil.rmtree(attendance_dir)

    def get_detail_url(self, attendance_id):
        """Helper method to get detail URL for an attendance record."""
        return reverse(self.detail_url_name, kwargs={"pk": attendance_id})

    def create_test_image(self, name="test_attendance.jpg"):
        """Helper method to create a test image file."""
        # Use the real test image file
        test_image_path = os.path.join(
            os.path.dirname(__file__),
            "..",
            "..",
            "..",
            "utils",
            "test",
            "factories",
            "employee",
            "test_image.png",
        )

        if os.path.exists(test_image_path):
            with open(test_image_path, "rb") as f:
                return SimpleUploadedFile(
                    name=name, content=f.read(), content_type="image/png"
                )
        else:
            # Fallback to simple content if test image doesn't exist
            return SimpleUploadedFile(
                name=name, content=b"fake_image_content", content_type="image/jpeg"
            )

    # Authentication and Permission Tests
    def test_list_attendance_unauthenticated(self):
        """Test that unauthenticated users cannot list attendance records."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_attendance_as_manager(self):
        """Test that manager users cannot list attendance records (admin only)."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_attendance_as_cashier(self):
        """Test that cashier users cannot list attendance records (admin only)."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_attendance_as_admin(self):
        """Admin should see all attendance records."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test attendance records
        self.assertGreaterEqual(len(response.data["results"]), 2)

        # Check that attendance records are ordered by time_in (default ordering)
        time_ins = [att["time_in"] for att in response.data["results"]]
        self.assertEqual(time_ins, sorted(time_ins))

    def test_retrieve_attendance_as_admin(self):
        """Admin should be able to retrieve any attendance record."""
        url = self.get_detail_url(self.attendance1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.attendance1.id)
        self.assertEqual(response.data["employee"], self.attendance1.employee.id)
        self.assertIsNotNone(response.data["time_in"])
        self.assertIsNotNone(response.data["time_out"])
        self.assertEqual(response.data["notes"], "Test attendance 1")

    def test_retrieve_attendance_as_manager(self):
        """Manager should not be able to retrieve attendance records (admin only)."""
        url = self.get_detail_url(self.attendance1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_attendance_as_cashier(self):
        """Cashier should not be able to retrieve attendance records (admin only)."""
        url = self.get_detail_url(self.attendance1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_attendance(self):
        """Test retrieving a non-existent attendance record."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Time In Tests
    def test_time_in_unauthenticated_allowed(self):
        """Test that unauthenticated users can access time-in endpoint (AllowAny)."""
        test_image = self.create_test_image("time_in_test.jpg")
        data = {
            "username": "<EMAIL>",
            "password": "wrongpassword",
            "image": test_image,
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_in_success_with_email(self):
        """Test successful time-in using email as username."""
        test_image = self.create_test_image("time_in_success.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",  # Default password from factory
            "image": test_image,
        }

        initial_count = Attendance.objects.count()
        response = self.client.post(self.time_in_url, data, format="multipart")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Attendance.objects.count(), initial_count + 1)

        # Check response structure
        self.assertEqual(response.data["status"], "success")
        self.assertEqual(response.data["message"], "Time-in recorded successfully")
        self.assertIn("data", response.data)
        self.assertIn("attendance_id", response.data["data"])
        self.assertIn("employee", response.data["data"])
        self.assertIn("time_in", response.data["data"])

    def test_time_in_success_with_phone(self):
        """Test successful time-in using phone number as username."""
        test_image = self.create_test_image("time_in_phone.jpg")
        data = {
            "username": self.test_employee1.user.phone_number,
            "password": "testpass123",
            "image": test_image,
        }

        initial_count = Attendance.objects.count()
        response = self.client.post(self.time_in_url, data, format="multipart")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Attendance.objects.count(), initial_count + 1)

    def test_time_in_invalid_credentials(self):
        """Test time-in with invalid credentials."""
        test_image = self.create_test_image("time_in_invalid.jpg")
        data = {
            "username": "<EMAIL>",
            "password": "wrongpassword",
            "image": test_image,
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_in_wrong_password(self):
        """Test time-in with correct username but wrong password."""
        test_image = self.create_test_image("time_in_wrong_pass.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "wrongpassword",
            "image": test_image,
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_in_user_without_employee(self):
        """Test time-in with user that has no associated employee record."""
        # Create a user without employee record
        user_without_employee = User.objects.create_user(
            email="<EMAIL>",
            phone_number="01999999999",
            password="testpass123",
            first_name="No",
            last_name="Employee",
            role=User.Role.CASHIER,
        )

        test_image = self.create_test_image("time_in_no_employee.jpg")
        data = {
            "username": user_without_employee.email,
            "password": "testpass123",
            "image": test_image,
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_in_already_checked_in(self):
        """Test time-in when employee already has an open attendance record."""
        # First time-in
        test_image1 = self.create_test_image("time_in_first.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image1,
        }

        response1 = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response1.status_code, status.HTTP_201_CREATED)

        # Second time-in (should fail)
        test_image2 = self.create_test_image("time_in_second.jpg")
        data["image"] = test_image2

        response2 = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response2.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_in_missing_image(self):
        """Test time-in without providing image."""
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Time Out Tests
    def test_time_out_success(self):
        """Test successful time-out."""
        # First, create an open attendance record
        attendance = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=8),
        )

        test_image = self.create_test_image("time_out_success.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image,
        }

        initial_salary_count = Salary.objects.count()
        response = self.client.post(self.time_out_url, data, format="multipart")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response structure
        self.assertEqual(response.data["status"], "success")
        self.assertEqual(response.data["message"], "Time-out recorded successfully")
        self.assertIn("data", response.data)
        self.assertIn("attendance_id", response.data["data"])
        self.assertIn("employee", response.data["data"])
        self.assertIn("time_in", response.data["data"])
        self.assertIn("time_out", response.data["data"])
        self.assertIn("salary", response.data["data"])

        # Check that attendance was updated
        attendance.refresh_from_db()
        self.assertIsNotNone(attendance.time_out)
        self.assertIsNotNone(attendance.total_hours)

        # Check that salary record was created
        self.assertEqual(Salary.objects.count(), initial_salary_count + 1)
        salary = Salary.objects.filter(attendance=attendance).first()
        self.assertIsNotNone(salary)
        self.assertEqual(salary.employee, self.test_employee1)

    def test_time_out_no_open_attendance(self):
        """Test time-out when employee has no open attendance record."""
        test_image = self.create_test_image("time_out_no_open.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_out_invalid_credentials(self):
        """Test time-out with invalid credentials."""
        test_image = self.create_test_image("time_out_invalid.jpg")
        data = {
            "username": "<EMAIL>",
            "password": "wrongpassword",
            "image": test_image,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_out_missing_image(self):
        """Test time-out without providing image."""
        # Create an open attendance record
        Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=8),
        )

        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Salary Calculation Tests
    def test_salary_calculation_full_day(self):
        """Test salary calculation for full day work (11-12 hours)."""
        # Create attendance with 11.5 hours
        attendance = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=11, minutes=30),
        )

        test_image = self.create_test_image("salary_full_day.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should use day_rate for full day work
        expected_salary = self.test_employee1.day_rate
        self.assertEqual(Decimal(str(response.data["data"]["salary"])), expected_salary)

    def test_salary_calculation_hourly(self):
        """Test salary calculation for hourly work (less than 11 hours)."""
        # Create attendance with 6 hours
        attendance = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=6),
        )

        test_image = self.create_test_image("salary_hourly.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should use hour_rate * hours for partial day work
        attendance.refresh_from_db()
        expected_salary = self.test_employee1.hour_rate * Decimal(
            str(attendance.total_hours)
        )
        self.assertEqual(Decimal(str(response.data["data"]["salary"])), expected_salary)

    def test_salary_calculation_over_12_hours(self):
        """Test salary calculation for work over 12 hours."""
        # Create attendance with 14 hours
        attendance = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=14),
        )

        test_image = self.create_test_image("salary_overtime.jpg")
        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": test_image,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should use hour_rate * hours for over 12 hours
        attendance.refresh_from_db()
        expected_salary = self.test_employee1.hour_rate * Decimal(
            str(attendance.total_hours)
        )
        self.assertEqual(Decimal(str(response.data["data"]["salary"])), expected_salary)

    # Filtering Tests
    def test_filter_attendance_by_employee(self):
        """Test filtering attendance records by employee."""
        url = f"{self.list_url}?employee={self.test_employee1.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only attendance records for test_employee1
        for attendance in response.data["results"]:
            self.assertEqual(attendance["employee"], self.test_employee1.id)

    def test_filter_attendance_nonexistent_employee(self):
        """Test filtering by non-existent employee."""
        url = f"{self.list_url}?employee=99999"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("employee", str(response.data).lower())

    # Search Tests
    def test_search_attendance_by_employee_first_name(self):
        """Test searching attendance by employee first name."""
        url = f"{self.list_url}?search=John"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(
            len(response.data["results"][0]["employee"]), self.test_employee1
        )

    def test_search_attendance_by_employee_last_name(self):
        """Test searching attendance by employee last name."""
        url = f"{self.list_url}?search=Smith"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(
            len(response.data["results"][0]["employee"]), self.test_employee2
        )

    def test_search_attendance_case_insensitive(self):
        """Test that search is case insensitive."""
        url = f"{self.list_url}?search=john"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(
            len(response.data["results"][0]["employee"]), self.test_employee1
        )

    def test_search_attendance_no_results(self):
        """Test searching with term that has no results."""
        url = f"{self.list_url}?search=NonexistentName"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return no results
        self.assertEqual(len(response.data["results"]), 0)

    # Ordering Tests
    def test_default_ordering_by_time_in(self):
        """Test that attendance records are ordered by time_in by default."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that attendance records are ordered by time_in
        time_ins = [att["time_in"] for att in response.data["results"]]
        self.assertEqual(time_ins, sorted(time_ins))

    def test_ordering_by_time_in_descending(self):
        """Test ordering attendance records by time_in in descending order."""
        url = f"{self.list_url}?ordering=-time_in"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that attendance records are ordered by time_in (descending)
        time_ins = [att["time_in"] for att in response.data["results"]]
        self.assertEqual(time_ins, sorted(time_ins, reverse=True))

    def test_invalid_ordering_field(self):
        """Test that invalid ordering fields are ignored."""
        url = f"{self.list_url}?ordering=invalid_field"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should fall back to default ordering (time_in)
        time_ins = [att["time_in"] for att in response.data["results"]]
        self.assertEqual(time_ins, sorted(time_ins))

    # Combined Tests
    def test_filter_search_and_ordering_combined(self):
        """Test combining filtering, searching, and ordering."""
        url = f"{self.list_url}?employee={self.test_employee1.id}&search=John&ordering=-time_in"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return filtered and searched results in descending order
        for attendance in response.data["results"]:
            self.assertEqual(attendance["employee"], self.test_employee1.id)

    # Pagination Tests
    def test_attendance_pagination(self):
        """Test that pagination works correctly."""
        # Create more attendance records to test pagination
        for i in range(25):
            Attendance.objects.create(
                employee=self.test_employee1,
                time_in=timezone.now() - timedelta(hours=i + 1),
                time_out=timezone.now() - timedelta(hours=i),
                notes=f"Test attendance {i}",
            )

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

        # Should have 20 results per page (default page size)
        self.assertEqual(len(response.data["results"]), 20)

    # Edge Cases and Error Handling
    def test_time_in_with_invalid_image_format(self):
        """Test time-in with invalid image format."""
        invalid_file = SimpleUploadedFile(
            name="test.txt", content=b"not an image", content_type="text/plain"
        )

        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": invalid_file,
        }

        response = self.client.post(self.time_in_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_time_out_with_invalid_image_format(self):
        """Test time-out with invalid image format."""
        # Create an open attendance record
        Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=8),
        )

        invalid_file = SimpleUploadedFile(
            name="test.txt", content=b"not an image", content_type="text/plain"
        )

        data = {
            "username": self.test_employee1.user.email,
            "password": "testpass123",
            "image": invalid_file,
        }

        response = self.client.post(self.time_out_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_attendance_model_string_representation(self):
        """Test the string representation of Attendance model."""
        expected_str = f"{self.attendance1.employee}- {self.attendance1.time_in} to {self.attendance1.time_out}"
        self.assertEqual(str(self.attendance1), expected_str)

        # Test attendance without time_out
        expected_str_no_out = (
            f"{self.attendance2.employee}- {self.attendance2.time_in} to ..."
        )
        self.assertEqual(str(self.attendance2), expected_str_no_out)

    def test_attendance_total_hours_calculation(self):
        """Test that total_hours is calculated correctly when saving."""
        time_in = timezone.now() - timedelta(hours=8)
        time_out = timezone.now()

        attendance = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=time_in,
            time_out=time_out,
        )

        # Check that total_hours was calculated
        self.assertIsNotNone(attendance.total_hours)
        expected_hours = round((time_out - time_in).total_seconds() / 3600, 2)
        self.assertEqual(attendance.total_hours, expected_hours)

    def test_salary_model_string_representation(self):
        """Test the string representation of Salary model."""
        # Create a salary record
        salary = Salary.objects.create(
            employee=self.test_employee1,
            attendance=self.attendance1,
            salary=Decimal("200.00"),
            notes="Test salary",
        )

        expected_str = f"{salary.employee} - {salary.salary} - {salary.created.date()}"
        self.assertEqual(str(salary), expected_str)

    def test_readonly_viewset_restrictions(self):
        """Test that AttendanceViewSet is read-only (no create/update/delete via standard endpoints)."""
        # Test that POST to list endpoint is not allowed
        data = {
            "employee": self.test_employee1.id,
            "time_in": timezone.now().isoformat(),
            "notes": "Test note",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

        # Test that PUT to detail endpoint is not allowed
        url = self.get_detail_url(self.attendance1.id)
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

        # Test that DELETE to detail endpoint is not allowed
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_empty_search_query(self):
        """Test that empty search query returns all attendance records."""
        url = f"{self.list_url}?search="
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return all attendance records (same as no search)
        all_response = self.admin_client.get(self.list_url)
        self.assertEqual(
            len(response.data["results"]), len(all_response.data["results"])
        )
