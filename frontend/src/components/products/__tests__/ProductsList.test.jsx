import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProductsList from '../ProductsList';
import { useProducts } from '../../../hooks/useProducts';
import { useAuth } from '../../../contexts/AuthContext';

// Mock the hooks
jest.mock('../../../hooks/useProducts');
jest.mock('../../../contexts/AuthContext');

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockNavigate,
}));

const mockProducts = [
    {
        id: 1,
        name: 'Test Product 1',
        description: 'Test description 1',
        cost: 10.00,
        price: 15.00,
        barcode: '123456789',
        unit_type: 'piece',
        category: { name: 'Test Category' },
        created: '2023-01-01T00:00:00Z',
        modified: '2023-01-01T00:00:00Z'
    },
    {
        id: 2,
        name: 'Test Product 2',
        description: 'Test description 2',
        cost: 20.00,
        price: 30.00,
        barcode: '987654321',
        unit_type: 'kg',
        category: null,
        created: '2023-01-02T00:00:00Z',
        modified: '2023-01-02T00:00:00Z'
    }
];

const mockPagination = {
    count: 2,
    next: null,
    previous: null,
    page: 1,
    page_size: 10
};

const renderWithRouter = (component) => {
    return render(
        <BrowserRouter>
            {component}
        </BrowserRouter>
    );
};

describe('ProductsList', () => {
    beforeEach(() => {
        useProducts.mockReturnValue({
            products: mockProducts,
            loading: false,
            error: null,
            pagination: mockPagination,
            fetchProducts: jest.fn(),
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        useAuth.mockReturnValue({
            currentUser: { role: 'admin' }
        });

        mockNavigate.mockClear();
    });

    test('renders products list correctly', () => {
        renderWithRouter(<ProductsList />);
        
        expect(screen.getByText('Products')).toBeInTheDocument();
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
        expect(screen.getByText('Test Product 2')).toBeInTheDocument();
    });

    test('displays product information correctly', () => {
        renderWithRouter(<ProductsList />);
        
        // Check first product
        expect(screen.getByText('Test Product 1')).toBeInTheDocument();
        expect(screen.getByText('Test Category')).toBeInTheDocument();
        expect(screen.getByText('$10.00')).toBeInTheDocument();
        expect(screen.getByText('$15.00')).toBeInTheDocument();
        expect(screen.getByText('123456789')).toBeInTheDocument();
        
        // Check second product
        expect(screen.getByText('Test Product 2')).toBeInTheDocument();
        expect(screen.getByText('No category')).toBeInTheDocument();
        expect(screen.getByText('987654321')).toBeInTheDocument();
    });

    test('shows Add New Product button for admin users', () => {
        renderWithRouter(<ProductsList />);
        
        expect(screen.getByText('Add New Product')).toBeInTheDocument();
    });

    test('hides admin actions for non-admin users', () => {
        useAuth.mockReturnValue({
            currentUser: { role: 'cashier' }
        });

        renderWithRouter(<ProductsList />);
        
        expect(screen.queryByText('Add New Product')).not.toBeInTheDocument();
        expect(screen.queryByText('Edit')).not.toBeInTheDocument();
        expect(screen.queryByText('Delete')).not.toBeInTheDocument();
    });

    test('handles search functionality', async () => {
        const mockFetchProducts = jest.fn();
        useProducts.mockReturnValue({
            products: mockProducts,
            loading: false,
            error: null,
            pagination: mockPagination,
            fetchProducts: mockFetchProducts,
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        renderWithRouter(<ProductsList />);
        
        const searchInput = screen.getByPlaceholderText(/search products/i);
        const searchButton = screen.getByText('Search');
        
        fireEvent.change(searchInput, { target: { value: 'test search' } });
        fireEvent.click(searchButton);
        
        expect(mockFetchProducts).toHaveBeenCalledWith({
            page: 1,
            page_size: 10,
            search: 'test search',
            ordering: 'name'
        });
    });

    test('handles sorting functionality', async () => {
        const mockFetchProducts = jest.fn();
        useProducts.mockReturnValue({
            products: mockProducts,
            loading: false,
            error: null,
            pagination: mockPagination,
            fetchProducts: mockFetchProducts,
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        renderWithRouter(<ProductsList />);
        
        const sortSelect = screen.getByDisplayValue('Sort by Name (A-Z)');
        
        fireEvent.change(sortSelect, { target: { value: 'price' } });
        
        expect(mockFetchProducts).toHaveBeenCalledWith({
            page: 1,
            page_size: 10,
            search: '',
            ordering: 'price'
        });
    });

    test('navigates to product view when View button is clicked', () => {
        renderWithRouter(<ProductsList />);
        
        const viewButtons = screen.getAllByText('View');
        fireEvent.click(viewButtons[0]);
        
        expect(mockNavigate).toHaveBeenCalledWith('/products/1');
    });

    test('navigates to product edit when Edit button is clicked', () => {
        renderWithRouter(<ProductsList />);
        
        const editButtons = screen.getAllByText('Edit');
        fireEvent.click(editButtons[0]);
        
        expect(mockNavigate).toHaveBeenCalledWith('/products/1/edit');
    });

    test('shows delete confirmation modal when Delete button is clicked', () => {
        renderWithRouter(<ProductsList />);
        
        const deleteButtons = screen.getAllByText('Delete');
        fireEvent.click(deleteButtons[0]);
        
        expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
        expect(screen.getByText(/Test Product 1/)).toBeInTheDocument();
    });

    test('displays loading state', () => {
        useProducts.mockReturnValue({
            products: [],
            loading: true,
            error: null,
            pagination: mockPagination,
            fetchProducts: jest.fn(),
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        renderWithRouter(<ProductsList />);
        
        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    test('displays error state', () => {
        const errorMessage = 'Failed to fetch products';
        useProducts.mockReturnValue({
            products: [],
            loading: false,
            error: errorMessage,
            pagination: mockPagination,
            fetchProducts: jest.fn(),
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        renderWithRouter(<ProductsList />);
        
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    test('displays empty state when no products', () => {
        useProducts.mockReturnValue({
            products: [],
            loading: false,
            error: null,
            pagination: { ...mockPagination, count: 0 },
            fetchProducts: jest.fn(),
            deleteProduct: jest.fn(),
            setError: jest.fn()
        });

        renderWithRouter(<ProductsList />);
        
        expect(screen.getByText('No products found.')).toBeInTheDocument();
        expect(screen.getByText('Create First Product')).toBeInTheDocument();
    });

    test('calculates and displays profit margin correctly', () => {
        renderWithRouter(<ProductsList />);
        
        // First product: (15-10)/10 * 100 = 50%
        expect(screen.getByText('50.00%')).toBeInTheDocument();
        
        // Second product: (30-20)/20 * 100 = 50%
        expect(screen.getAllByText('50.00%')).toHaveLength(2);
    });
});
