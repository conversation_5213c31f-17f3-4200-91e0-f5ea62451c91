from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from pos.views.pos import POSViewSet
from pos.views.pos_session import POSSessionViewSet
from pos.views.pos_session_transaction import POSSessionTransactionViewSet

router = DefaultRouter()
router.register(r"pos", POSViewSet, basename="pos")
router.register(r"sessions", POSSessionViewSet, basename="pos-session")
router.register(
    r"session/(?P<session_pk>\d+)/transactions",
    POSSessionTransactionViewSet,
    basename="pos-session-transaction",
)

urlpatterns = [
    path("", include(router.urls)),
]
