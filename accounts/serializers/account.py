from rest_framework import serializers

from ..models import Account


class AccountSerializer(serializers.ModelSerializer):
    owner_type = serializers.CharField(source="content_type.model", read_only=True)
    owner_id = serializers.IntegerField(source="object_id", read_only=True)
    owner_name = serializers.SerializerMethodField()

    class Meta:
        model = Account
        fields = [
            "id",
            "name",
            "account_type",
            "balance",
            "created",
            "modified",
            "content_type",
            "object_id",
            "owner_type",
            "owner_id",
            "owner_name",
        ]
        read_only_fields = ["created", "modified"]

    def get_owner_name(self, obj):
        if hasattr(obj, "content_object") and obj.content_object:
            return str(obj.content_object)
        return None
