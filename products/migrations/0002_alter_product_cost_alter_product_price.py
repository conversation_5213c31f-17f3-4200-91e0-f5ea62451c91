# Generated by Django 5.2.1 on 2025-07-19 11:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="cost",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Cost price of the product",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="cost price",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Selling price of the product",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="selling price",
            ),
        ),
    ]
