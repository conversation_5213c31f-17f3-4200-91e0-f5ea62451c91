import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Container, Form, InputGroup, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {useWarehouses} from '../../hooks/useWarehouses';
import {useAuth} from '../../contexts/AuthContext';
import DeleteConfirmModal from './DeleteConfirmModal';

/**
 * Component for displaying a list of warehouses with search, sort, and pagination
 * @returns {JSX.Element} The warehouses list component
 */
const WarehousesList = () => {
    const navigate = useNavigate();
    const { currentUser } = useAuth();
    const { 
        warehouses, 
        loading, 
        error, 
        pagination, 
        fetchWarehouses, 
        deleteWarehouse,
        setError 
    } = useWarehouses();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [ordering, setOrdering] = useState('name');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [warehouseToDelete, setWarehouseToDelete] = useState(null);

    // Load warehouses on component mount
    useEffect(() => {
        fetchWarehouses({
            page: 1,
            page_size: 10,
            ordering: 'name'
        });
    }, [fetchWarehouses]);

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchWarehouses({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchWarehouses({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchWarehouses({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle delete click
    const handleDeleteClick = (warehouse) => {
        setWarehouseToDelete(warehouse);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteWarehouse(warehouseToDelete.id);
            setShowDeleteModal(false);
            setWarehouseToDelete(null);

            // Refresh the current page
            fetchWarehouses({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            });
        } catch (err) {
            // Error is handled by the hook
        }
    };

    // Check if user can perform admin actions
    const canPerformAdminActions = currentUser && 
        (currentUser.role === 'admin' || currentUser.role === 'manager');

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // Previous button
        items.push(
            <Pagination.Prev 
                key="prev"
                disabled={!pagination.previous}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = 1; page <= totalPages; page++) {
            if (
                page === 1 || 
                page === totalPages || 
                (page >= currentPage - 2 && page <= currentPage + 2)
            ) {
                items.push(
                    <Pagination.Item
                        key={page}
                        active={page === currentPage}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </Pagination.Item>
                );
            } else if (
                page === currentPage - 3 || 
                page === currentPage + 3
            ) {
                items.push(<Pagination.Ellipsis key={`ellipsis-${page}`} />);
            }
        }

        // Next button
        items.push(
            <Pagination.Next 
                key="next"
                disabled={!pagination.next}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return items;
    };

    const getSortIcon = (field) => {
        if (ordering === field) return ' ↑';
        if (ordering === `-${field}`) return ' ↓';
        return '';
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Warehouses</h2>
                        {canPerformAdminActions && (
                            <Button 
                                variant="primary" 
                                onClick={() => navigate('/warehouses/create')}
                            >
                                Add New Warehouse
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filters */}
            <Row className="mb-3">
                <Col md={8}>
                    <Form onSubmit={handleSearch}>
                        <InputGroup>
                            <Form.Control
                                type="text"
                                placeholder="Search warehouses by name or location..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                aria-label="Search warehouses"
                            />
                            <Button variant="outline-secondary" type="submit">
                                Search
                            </Button>
                        </InputGroup>
                    </Form>
                </Col>
                <Col md={4}>
                    <Form.Select
                        value={ordering}
                        onChange={(e) => handleSort(e.target.value)}
                        aria-label="Sort warehouses"
                    >
                        <option value="name">Sort by Name (A-Z)</option>
                        <option value="-name">Sort by Name (Z-A)</option>
                        <option value="location">Sort by Location (A-Z)</option>
                        <option value="-location">Sort by Location (Z-A)</option>
                        <option value="created">Sort by Date (Oldest)</option>
                        <option value="-created">Sort by Date (Newest)</option>
                    </Form.Select>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Warehouses Table */}
            <Card>
                <Card.Body>
                    {loading ? (
                        <div className="text-center py-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : warehouses.length === 0 ? (
                        <div className="text-center py-4">
                            <p className="text-muted">No warehouses found.</p>
                            {canPerformAdminActions && (
                                <Button 
                                    variant="primary" 
                                    onClick={() => navigate('/warehouses/create')}
                                >
                                    Create First Warehouse
                                </Button>
                            )}
                        </div>
                    ) : (
                        <>
                            <Table responsive striped hover>
                                <thead className="listing-table-header">
                                    <tr>
                                        <th onClick={() => handleSort('name')}>
                                            Name{getSortIcon('name')}
                                        </th>
                                        <th onClick={() => handleSort('location')}>
                                            Location{getSortIcon('location')}
                                        </th>
                                        <th onClick={() => handleSort('created')}>
                                            Created{getSortIcon('created')}
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {warehouses.map((warehouse) => (
                                        <tr key={warehouse.id}>
                                            <td>
                                                <strong>{warehouse.name}</strong>
                                            </td>
                                            <td>{warehouse.location}</td>
                                            <td>
                                                {new Date(warehouse.created).toLocaleString()}
                                            </td>
                                            <td>
                                                <div className="d-flex gap-1">
                                                    <Button
                                                        variant="outline-info"
                                                        size="sm"
                                                        onClick={() => navigate(`/warehouses/${warehouse.id}`)}
                                                    >
                                                        View
                                                    </Button>
                                                    <Button
                                                        variant="outline-success"
                                                        size="sm"
                                                        onClick={() => navigate(`/warehouses/${warehouse.id}#stock`)}
                                                        title="View stock items"
                                                    >
                                                        Stock
                                                    </Button>
                                                    {canPerformAdminActions && (
                                                        <>
                                                            <Button
                                                                variant="outline-warning"
                                                                size="sm"
                                                                onClick={() => navigate(`/warehouses/${warehouse.id}/edit`)}
                                                            >
                                                                Edit
                                                            </Button>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => handleDeleteClick(warehouse)}
                                                            >
                                                                Delete
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {pagination.count > pagination.page_size && (
                                <div className="d-flex justify-content-between align-items-center mt-3">
                                    <div className="text-muted">
                                        Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                        {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                        {pagination.count} warehouses
                                    </div>
                                    <Pagination className="mb-0">
                                        {generatePaginationItems()}
                                    </Pagination>
                                </div>
                            )}
                        </>
                    )}
                </Card.Body>
            </Card>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                warehouseName={warehouseToDelete?.name}
                loading={loading}
            />
        </Container>
    );
};

export default WarehousesList;
