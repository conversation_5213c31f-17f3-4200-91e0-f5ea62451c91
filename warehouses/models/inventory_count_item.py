from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class InventoryCountItem(TimeStampedModel):
    """
    InventoryCountItem model represents a single item counted during an inventory count.
    It records the difference between the system quantity and the physically counted quantity.
    """

    inventory_count = models.ForeignKey(
        "warehouses.InventoryCount",
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name=_("inventory count"),
        help_text=_("The inventory count this item belongs to"),
    )

    stock_item = models.ForeignKey(
        "warehouses.StockItem",
        on_delete=models.CASCADE,
        related_name="inventory_count_items",
        verbose_name=_("stock item"),
        help_text=_("The stock item being counted"),
    )

    recorded_quantity = models.DecimalField(
        _("recorded quantity"),
        max_digits=15,
        decimal_places=3,
        help_text=_("The quantity physically counted during inventory"),
        null=True,
        blank=True,
        validators=[MinValueValidator(0.0)],
    )

    system_quantity = models.DecimalField(
        _("system quantity"),
        max_digits=15,
        decimal_places=3,
        help_text=_("The quantity in the system before the count"),
        null=True,
        blank=True,
        validators=[MinValueValidator(0.0)],
    )

    difference = models.DecimalField(
        _("difference"),
        max_digits=15,
        decimal_places=3,
        help_text=_(
            "The difference between recorded and system quantity (recorded - system)"
        ),
        null=True,
        blank=True,
        validators=[MinValueValidator(0.0)],
    )
    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this count item"),
    )

    class Meta:
        verbose_name = _("inventory count item")
        verbose_name_plural = _("inventory count items")
        ordering = ["inventory_count", "stock_item"]
        unique_together = [["inventory_count", "stock_item"]]

    def __str__(self):
        return f"{self.stock_item} - {self.recorded_quantity} (System: {self.system_quantity})"

    def save(self, *args, **kwargs):
        """Calculate the difference before saving"""
        if self.recorded_quantity is not None and self.system_quantity is not None:
            self.difference = self.recorded_quantity - self.system_quantity
        super().save(*args, **kwargs)

    @property
    def has_discrepancy(self):
        """Check if there's a difference between recorded and system quantity"""
        return self.difference != 0

    @property
    def is_shortage(self):
        """Check if there's a shortage (recorded < system)"""
        return self.difference < 0

    @property
    def is_surplus(self):
        """Check if there's a surplus (recorded > system)"""
        return self.difference > 0
