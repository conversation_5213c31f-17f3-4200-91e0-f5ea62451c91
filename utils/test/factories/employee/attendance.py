import random

import factory
from factory import <PERSON>zy<PERSON>tt<PERSON>bute, SubFactory
from factory.django import DjangoModelFactory

from employees.models.attendance import Attendance
from utils.test.factories.employee.employee import EmployeeFactory


class AttendanceFactory(DjangoModelFactory):
    """
    Factory for creating Attendance instances for testing.
    """

    class Meta:
        model = Attendance
        skip_postgeneration_save = True

    employee = SubFactory(EmployeeFactory)

    # Generate a time_in within the last week
    time_in = LazyAttribute(
        lambda _: datetime.now()
        - timedelta(days=random.randint(0, 6), hours=random.randint(0, 12))
    )

    # Generate a time_out a few hours after time_in
    time_out = LazyAttribute(
        lambda o: o.time_in + timedelta(hours=random.randint(4, 10))
    )

    # total_hours will be calculated automatically by the model's save method
    total_hours = None

    # Use placeholders for images
    time_in_image = None
    time_out_image = None

    notes = factory.Faker("paragraph", nb_sentences=1)
