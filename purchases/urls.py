from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from purchases.views.purchase import PurchaseViewSet
from purchases.views.supplier import SupplierViewSet

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r'suppliers', SupplierViewSet, basename='supplier')
router.register(r'purchases', PurchaseViewSet, basename='purchase')

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path('', include(router.urls)),
]
