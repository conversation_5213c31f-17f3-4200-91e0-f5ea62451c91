from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from accounts.models import Account, AccountTransaction, Balance, TransactionType
from pos.models import POSSession
from pos.models import TransactionType as POSTransactionType
from utils.test.base_test import BaseTestCase
from utils.test.factories.accounts.account import AccountFactory
from utils.test.factories.accounts.account_transaction import AccountTransactionFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class AccountViewSetTestCase(BaseTestCase):
    """
    Test cases for AccountViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("accounts:account-list")
        self.detail_url_name = "accounts:account-detail"

        # Create test objects for account ownership
        self.test_warehouse1 = WarehouseFactory.create(name="Test Warehouse 1")
        self.test_warehouse2 = WarehouseFactory.create(name="Test Warehouse 2")

        # Use existing POS from BaseTestCase and create another one
        self.test_pos1 = self.pos  # Use existing POS from BaseTestCase
        self.test_pos2 = POSFactory.create(
            name="Test POS 2", warehouse=self.test_warehouse1
        )

        # Create test accounts
        self.account1 = AccountFactory.create_for_object(self.test_warehouse1)

        self.account2 = AccountFactory.create_for_object(self.test_warehouse2)

        self.account3 = AccountFactory.create_for_object(self.test_pos1)

        # Create test balances (many-to-many relationship between accounts and POS)
        self.balance1 = Balance.objects.create(
            account=self.account1,
            pos=self.test_pos1,
            amount=Decimal("1000.00"),
            notes="Initial balance for account1-pos1",
        )

        self.balance2 = Balance.objects.create(
            account=self.account1,
            pos=self.test_pos2,
            amount=Decimal("500.00"),
            notes="Initial balance for account1-pos2",
        )

        self.balance3 = Balance.objects.create(
            account=self.account2,
            pos=self.test_pos1,
            amount=Decimal("300.00"),
            notes="Initial balance for account2-pos1",
        )

        # Create test transactions (now linked to balances)
        self.transaction1 = AccountTransactionFactory.create(
            balance=self.balance1,
            type=TransactionType.CREDIT,
            amount=Decimal("100.00"),
            description="Test credit transaction",
        )

        self.transaction2 = AccountTransactionFactory.create(
            balance=self.balance1,
            type=TransactionType.DEBIT,
            amount=Decimal("50.00"),
            description="Test debit transaction",
        )

    def get_detail_url(self, account_id):
        """Helper method to get detail URL for an account."""
        return reverse(self.detail_url_name, kwargs={"pk": account_id})

    def get_balances_url(self, account_id):
        """Helper method to get balances URL for an account."""
        return reverse("accounts:account-balances", kwargs={"pk": account_id})

    def get_balance_transactions_url(self, account_id, balance_id):
        """Helper method to get balance transactions URL."""
        return reverse(
            "accounts:account-balance-transactions",
            kwargs={"pk": account_id, "balance_pk": balance_id},
        )

    def get_balance_create_transaction_url(self, account_id, balance_id):
        """Helper method to get balance create transaction URL."""
        return reverse(
            "accounts:account-balance-create-transaction",
            kwargs={"pk": account_id, "balance_pk": balance_id},
        )

    # Authentication and Permission Tests
    def test_list_accounts_unauthenticated(self):
        """Test that unauthenticated users cannot list accounts."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_accounts_as_admin(self):
        """Admin should see all accounts."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test accounts
        self.assertGreaterEqual(len(response.data["results"]), 3)

    def test_list_accounts_as_manager(self):
        """Manager should see all accounts (list permission)."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test accounts
        self.assertGreaterEqual(len(response.data["results"]), 3)

    def test_list_accounts_as_cashier(self):
        """Cashier should see all accounts (list permission)."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test accounts
        self.assertGreaterEqual(len(response.data["results"]), 3)

    def test_retrieve_account_as_admin(self):
        """Admin should be able to retrieve any account."""
        url = self.get_detail_url(self.account1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.account1.id)
        self.assertEqual(response.data["owner_type"], "warehouse")
        self.assertEqual(response.data["owner_id"], self.test_warehouse1.id)
        self.assertEqual(response.data["owner_name"], str(self.test_warehouse1))

    def test_retrieve_account_as_manager(self):
        """Manager should not be able to retrieve accounts (admin only for CRUD)."""
        url = self.get_detail_url(self.account1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_account_as_cashier(self):
        """Cashier should not be able to retrieve accounts (admin only for CRUD)."""
        url = self.get_detail_url(self.account1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_account(self):
        """Test retrieving a non-existent account."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_account_as_admin(self):
        """Admin should be able to create accounts."""
        data = {
            "name": "test",
            "account_type": "test",
        }

        initial_count = Account.objects.count()
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that account was created
        self.assertEqual(Account.objects.count(), initial_count + 1)

        # Check response data
        account = Account.objects.get(id=response.data["id"])

    def test_create_account_as_manager(self):
        """Manager should not be able to create accounts (admin only)."""
        data = {
            "name": "test",
            "account_type": "test",
            "balance": "1500.00",
        }

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_account_as_cashier(self):
        """Cashier should not be able to create accounts (admin only)."""
        data = {
            "name": "test",
            "account_type": "test",
            "balance": "1500.00",
        }

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Update Tests
    def test_update_account_as_admin(self):
        """Admin should be able to update accounts."""
        url = self.get_detail_url(self.account1.id)
        data = {
            "name": "test",
            "account_type": "test",
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that account was updated
        self.account1.refresh_from_db()
        self.assertEqual(self.account1.name, "test")

    def test_partial_update_account_as_admin(self):
        """Admin should be able to partially update accounts."""
        url = self.get_detail_url(self.account1.id)
        data = {"name": "tests"}

        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only balance was updated
        self.account1.refresh_from_db()
        self.assertEqual(self.account1.name, "tests")

    def test_update_account_as_manager(self):
        """Manager should not be able to update accounts (admin only)."""
        url = self.get_detail_url(self.account1.id)
        data = {"name": "test"}

        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_account_as_cashier(self):
        """Cashier should not be able to update accounts (admin only)."""
        url = self.get_detail_url(self.account1.id)
        data = {"balance": "1300.00"}

        response = self.cashier_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Delete Tests
    def test_delete_account_as_admin(self):
        """Admin should be able to delete accounts."""
        url = self.get_detail_url(self.account3.id)
        initial_count = Account.objects.count()

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that account was deleted
        self.assertEqual(Account.objects.count(), initial_count - 1)
        self.assertFalse(Account.objects.filter(id=self.account3.id).exists())

    def test_delete_account_as_manager(self):
        """Manager should not be able to delete accounts (admin only)."""
        url = self.get_detail_url(self.account1.id)

        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_account_as_cashier(self):
        """Cashier should not be able to delete accounts (admin only)."""
        url = self.get_detail_url(self.account1.id)

        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_nonexistent_account(self):
        """Test deleting a non-existent account."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Custom Action Tests - Balances
    def test_get_account_balances_as_admin(self):
        """Admin should be able to get account balances."""
        url = self.get_balances_url(self.account1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test balances for account1
        self.assertEqual(len(response.data["results"]), 2)

        # Check that balances are ordered by created (descending)
        created_dates = [balance["created"] for balance in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    def test_get_account_balances_as_manager(self):
        """Manager should only see balances for POS they work on."""
        url = self.get_balances_url(self.account1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["pos"], self.test_pos1.id)

    def test_get_account_balances_as_cashier(self):
        """Cashier should only see balances for POS they work on."""
        url = self.get_balances_url(self.account1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["pos"], self.test_pos1.id)

    def test_get_account_balances_unauthenticated(self):
        """Unauthenticated users should not be able to get account balances."""
        url = self.get_balances_url(self.account1.id)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_balances_nonexistent_account(self):
        """Test getting balances for non-existent account."""
        url = self.get_balances_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Balance Transaction Tests
    def test_get_balance_transactions_as_admin(self):
        """Admin should be able to get balance transactions."""
        url = self.get_balance_transactions_url(self.account1.id, self.balance1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test transactions for balance1
        self.assertGreaterEqual(len(response.data["results"]), 2)

        # Check that transactions are ordered by created (descending)
        created_dates = [trans["created"] for trans in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    def test_get_balance_transactions_as_manager(self):
        """Manager should only see transactions for balances on POS they work on."""
        url = self.get_balance_transactions_url(self.account1.id, self.balance1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should only see transactions for balance1
        self.assertGreaterEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["balance"], self.balance1.id)

    def test_get_balance_transactions_as_cashier(self):
        """Cashier should only see transactions for balances on POS they work on."""
        url = self.get_balance_transactions_url(self.account1.id, self.balance1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should only see transactions for balance1
        self.assertGreaterEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["balance"], self.balance1.id)

    def test_get_balance_transactions_as_manager_for_non_related_balance(self):
        url = self.get_balance_transactions_url(self.account1.id, self.balance2.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_balance_transactions_as_cashier_for_non_related_balance(self):
        url = self.get_balance_transactions_url(self.account1.id, self.balance2.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_balance_transactions_nonexistent_balance(self):
        """Test getting transactions for non-existent balance."""
        url = self.get_balance_transactions_url(self.account1.id, 99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Balance Transaction Creation Tests
    def test_create_balance_transaction_credit_as_admin(self):
        """Admin should be able to create credit transactions for balances."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        initial_balance_amount = self.balance1.amount
        data = {
            "type": TransactionType.CREDIT,
            "amount": "200.00",
            "description": "Test credit transaction",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that transaction was created
        transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(transaction.type, TransactionType.CREDIT)
        self.assertEqual(transaction.amount, Decimal("200.00"))
        self.assertEqual(transaction.description, "Test credit transaction")
        self.assertEqual(transaction.balance, self.balance1)

        # Check that balance amount was updated
        self.balance1.refresh_from_db()
        expected_balance = initial_balance_amount + Decimal("200.00")
        self.assertEqual(self.balance1.amount, expected_balance)

    def test_create_balance_transaction_debit_as_admin(self):
        """Admin should be able to create debit transactions for balances."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        initial_balance_amount = self.balance1.amount
        data = {
            "type": TransactionType.DEBIT,
            "amount": "150.00",
            "description": "Test debit transaction",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that transaction was created
        transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(transaction.type, TransactionType.DEBIT)
        self.assertEqual(transaction.amount, Decimal("150.00"))
        self.assertEqual(transaction.balance, self.balance1)

        # Check that balance amount was updated (decreased)
        self.balance1.refresh_from_db()
        expected_balance = initial_balance_amount - Decimal("150.00")
        self.assertEqual(self.balance1.amount, expected_balance)

    def test_create_balance_transaction_with_related_object(self):
        """Test creating balance transaction with related object."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        warehouse_content_type = ContentType.objects.get_for_model(self.test_warehouse1)

        data = {
            "type": TransactionType.CREDIT,
            "amount": "300.00",
            "description": "Transaction related to warehouse",
            "related_content_type": warehouse_content_type.id,
            "related_object_id": self.test_warehouse1.id,
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that transaction has related object
        transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(transaction.related_content_type, warehouse_content_type)
        self.assertEqual(transaction.related_object_id, self.test_warehouse1.id)
        self.assertEqual(transaction.related_object, self.test_warehouse1)
        self.assertEqual(transaction.balance, self.balance1)

    def test_create_balance_transaction_with_pos_session(self):
        """Test creating balance transaction with POS session."""
        # Create a POS session for the balance's POS
        pos_session = POSSessionFactory.create(
            pos=self.balance1.pos, status=POSSession.Status.OPEN
        )

        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.DEBIT,
            "amount": "100.00",
            "description": "POS transaction",
            "session_id": pos_session.id,
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that both account transaction and POS session transaction were created
        account_transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(account_transaction.amount, Decimal("100.00"))
        self.assertEqual(account_transaction.balance, self.balance1)

        # Check that POS session transaction was created
        pos_transactions = pos_session.transactions.all()
        self.assertEqual(pos_transactions.count(), 1)
        self.assertEqual(pos_transactions.first().amount, Decimal("100.00"))
        self.assertEqual(
            pos_transactions.first().transaction_type, POSTransactionType.PURCHASE
        )

    def test_create_balance_credit_transaction_with_pos_session(self):
        """Test creating balance credit transaction with POS session."""
        # Create a POS session for the balance's POS
        pos_session = POSSessionFactory.create(
            pos=self.balance1.pos, status=POSSession.Status.OPEN
        )
        total_sales = pos_session.total_sales

        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "POS credit transaction",
            "session_id": pos_session.id,
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that both account transaction and POS session transaction were created
        account_transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(account_transaction.amount, Decimal("100.00"))
        self.assertEqual(account_transaction.balance, self.balance1)

        # Check that POS session transaction was created
        pos_transactions = pos_session.transactions.all()
        self.assertEqual(pos_transactions.count(), 1)
        self.assertEqual(pos_transactions.first().amount, Decimal("100.00"))
        self.assertEqual(
            pos_transactions.first().transaction_type, POSTransactionType.CASH_IN
        )
        pos_session.refresh_from_db()
        self.assertEqual(pos_session.total_sales, total_sales + Decimal("100.00"))

    def test_create_balance_transaction_as_manager(self):
        """Manager should be able to create balance transactions for POS they work on."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        pos_session = POSSessionFactory.create(
            pos=self.balance1.pos, status=POSSession.Status.OPEN
        )
        total_sales = pos_session.total_sales

        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "Manager transaction",
            "session_id": pos_session.id,
        }
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that both account transaction and POS session transaction were created
        account_transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(account_transaction.amount, Decimal("100.00"))
        self.assertEqual(account_transaction.balance, self.balance1)

        # Check that POS session transaction was created
        pos_transactions = pos_session.transactions.all()
        self.assertEqual(pos_transactions.count(), 1)
        self.assertEqual(pos_transactions.first().amount, Decimal("100.00"))
        self.assertEqual(
            pos_transactions.first().transaction_type, POSTransactionType.CASH_IN
        )
        pos_session.refresh_from_db()
        self.assertEqual(pos_session.total_sales, total_sales + Decimal("100.00"))

    def test_create_balance_transaction_as_cashier(self):
        """Cashier should be able to create balance transactions for POS they work on."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        pos_session = POSSessionFactory.create(
            pos=self.balance1.pos, status=POSSession.Status.OPEN
        )
        total_sales = pos_session.total_sales

        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "Manager transaction",
            "session_id": pos_session.id,
        }
        response = self.cashier_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that both account transaction and POS session transaction were created
        account_transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(account_transaction.amount, Decimal("100.00"))
        self.assertEqual(account_transaction.balance, self.balance1)

        # Check that POS session transaction was created
        pos_transactions = pos_session.transactions.all()
        self.assertEqual(pos_transactions.count(), 1)
        self.assertEqual(pos_transactions.first().amount, Decimal("100.00"))
        self.assertEqual(
            pos_transactions.first().transaction_type, POSTransactionType.CASH_IN
        )
        pos_session.refresh_from_db()
        self.assertEqual(pos_session.total_sales, total_sales + Decimal("100.00"))

    def test_create_balance_transaction_unauthenticated(self):
        """Unauthenticated users should not be able to create balance transactions."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "Unauthorized transaction",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_balance_transaction_invalid_amount(self):
        """Test creating balance transaction with invalid amount."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.CREDIT,
            "amount": "-100.00",  # Negative amount
            "description": "Invalid transaction",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_balance_transaction_zero_amount(self):
        """Test creating balance transaction with zero amount."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.CREDIT,
            "amount": "0.00",  # Zero amount
            "description": "Zero transaction",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_balance_transaction_missing_required_fields(self):
        """Test creating balance transaction with missing required fields."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "description": "Incomplete transaction"
            # Missing type and amount
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_balance_transaction_nonexistent_account(self):
        """Test creating balance transaction for non-existent account."""
        url = self.get_balance_create_transaction_url(99999, self.balance1.id)
        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "Transaction for non-existent account",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_balance_transaction_nonexistent_balance(self):
        """Test creating balance transaction for non-existent balance."""
        url = self.get_balance_create_transaction_url(self.account1.id, 99999)
        data = {
            "type": TransactionType.CREDIT,
            "amount": "100.00",
            "description": "Transaction for non-existent balance",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Generic Foreign Key Tests
    def test_account_for_different_content_types(self):
        """Test that accounts can be created for different content types."""
        # Test with Warehouse
        warehouse_account = AccountFactory.create_for_object(self.test_warehouse1)
        self.assertEqual(warehouse_account.content_object, self.test_warehouse1)

        # Test with POS
        pos_account = AccountFactory.create_for_object(self.test_pos1)
        self.assertEqual(pos_account.content_object, self.test_pos1)

    def test_account_serializer_owner_fields(self):
        """Test that account serializer includes owner information."""
        url = self.get_detail_url(self.account1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check owner fields
        self.assertIn("owner_type", response.data)
        self.assertIn("owner_id", response.data)
        self.assertIn("owner_name", response.data)
        self.assertEqual(response.data["owner_type"], "warehouse")
        self.assertEqual(response.data["owner_id"], self.test_warehouse1.id)

    # Balance Calculation Tests
    def test_balance_amount_updates_with_multiple_transactions(self):
        """Test that balance amount updates correctly with multiple transactions."""
        initial_balance_amount = self.balance1.amount

        # Create multiple transactions for the balance
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )

        # Credit 100
        response1 = self.admin_client.post(
            url,
            {
                "type": TransactionType.CREDIT,
                "amount": "100.00",
                "description": "Credit 1",
            },
            format="json",
        )
        self.assertEqual(response1.status_code, status.HTTP_201_CREATED)

        # Debit 50
        response2 = self.admin_client.post(
            url,
            {
                "type": TransactionType.DEBIT,
                "amount": "50.00",
                "description": "Debit 1",
            },
            format="json",
        )
        self.assertEqual(response2.status_code, status.HTTP_201_CREATED)

        # Credit 200
        response3 = self.admin_client.post(
            url,
            {
                "type": TransactionType.CREDIT,
                "amount": "200.00",
                "description": "Credit 2",
            },
            format="json",
        )
        self.assertEqual(response3.status_code, status.HTTP_201_CREATED)

        # Check final balance amount: initial + 100 - 50 + 200 = initial + 250
        self.balance1.refresh_from_db()
        expected_balance = initial_balance_amount + Decimal("250.00")
        self.assertEqual(self.balance1.amount, expected_balance)

    def test_negative_balance_amount_allowed(self):
        """Test that balance amounts can be negative."""

        # Create large debit transaction
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        response = self.admin_client.post(
            url,
            {
                "type": TransactionType.DEBIT,
                "amount": -self.balance1.amount - Decimal("100.00"),
                "description": "Large debit",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Check that balance amount is negative
        self.assertEqual(
            response.data,
            {
                "amount": [
                    ErrorDetail(
                        string="Amount must be greater than zero", code="invalid"
                    )
                ]
            },
        )

    # Filtering and Pagination Tests
    def test_account_list_pagination(self):
        """Test that account list supports pagination."""
        # Create more accounts to test pagination
        for i in range(25):
            warehouse = WarehouseFactory.create(name=f"Warehouse {i}")
            AccountFactory.create_for_object(warehouse)

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

    def test_balance_transaction_list_pagination(self):
        """Test that balance transaction list supports pagination."""
        # Create many transactions for a balance
        for i in range(25):
            AccountTransactionFactory.create(balance=self.balance1)

        url = self.get_balance_transactions_url(self.account1.id, self.balance1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

    # Edge Cases
    def test_account_with_deleted_content_object(self):
        """Test account behavior when content object is deleted."""
        # Create account for warehouse
        warehouse = WarehouseFactory.create()
        account = AccountFactory.create_for_object(warehouse)

        # Delete the warehouse
        warehouse_id = warehouse.id
        warehouse.delete()

        # Account should still exist but content_object should be None
        account.refresh_from_db()
        self.assertIsNone(account.content_object)
        self.assertEqual(account.object_id, warehouse_id)

    def test_duplicate_account_for_same_object(self):
        """Test that multiple accounts can exist for the same object."""
        # Create two accounts for the same warehouse
        account1 = AccountFactory.create_for_object(self.test_warehouse1)
        account2 = AccountFactory.create_for_object(self.test_warehouse1)

        # Both should exist and be different
        self.assertNotEqual(account1.id, account2.id)
        self.assertEqual(account1.content_object, account2.content_object)

    def test_balance_transaction_ordering(self):
        """Test that balance transactions are ordered by creation date (descending)."""
        # Create transactions with slight delays to ensure different timestamps
        import time

        trans1 = AccountTransactionFactory.create(
            balance=self.balance1, description="First"
        )
        time.sleep(0.01)
        trans2 = AccountTransactionFactory.create(
            balance=self.balance1, description="Second"
        )
        time.sleep(0.01)
        trans3 = AccountTransactionFactory.create(
            balance=self.balance1, description="Third"
        )

        url = self.get_balance_transactions_url(self.account1.id, self.balance1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that transactions are ordered by created date (descending)
        descriptions = [trans["description"] for trans in response.data["results"]]
        # Most recent should be first
        self.assertEqual(descriptions[0], "Third")

    def test_large_balance_transaction_amounts(self):
        """Test handling of large balance transaction amounts."""
        url = self.get_balance_create_transaction_url(
            self.account1.id, self.balance1.id
        )
        data = {
            "type": TransactionType.CREDIT,
            "amount": "************.99",  # Large amount
            "description": "Large transaction",
        }

        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that large amount is handled correctly
        transaction = AccountTransaction.objects.get(id=response.data["id"])
        self.assertEqual(transaction.amount, Decimal("************.99"))
        self.assertEqual(transaction.balance, self.balance1)
