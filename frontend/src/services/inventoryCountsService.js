import api from './api';

/**
 * Inventory Counts API service
 * Handles all API calls related to inventory counts CRUD operations
 */
export const inventoryCountsService = {
    /**
     * Get all inventory counts with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.status - Filter by status
     * @param {number} params.warehouse - Filter by warehouse ID
     * @returns {Promise} API response
     */
    getInventoryCounts: (params = {}) => {
        const queryParams = new URLSearchParams();

        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);

        // Add search parameter
        if (params.search) queryParams.append('search', params.search);

        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);

        // Add filter parameters
        if (params.status) queryParams.append('status', params.status);
        if (params.warehouse) queryParams.append('warehouse', params.warehouse);

        const queryString = queryParams.toString();
        const url = `/inventory-counts/${queryString ? `?${queryString}` : ''}`;

        return api.get(url);
    },

    /**
     * Get a single inventory count by ID
     * @param {number} id - Inventory count ID
     * @returns {Promise} API response
     */
    getInventoryCount: (id) => {
        return api.get(`/inventory-counts/${id}/`);
    },

    /**
     * Create a new inventory count
     * @param {Object} inventoryCountData - Inventory count data
     * @param {number} inventoryCountData.warehouse - Warehouse ID
     * @param {string} inventoryCountData.notes - Notes (optional)
     * @returns {Promise} API response
     */
    createInventoryCount: (inventoryCountData) => {
        return api.post('/inventory-counts/', inventoryCountData);
    },

    /**
     * Update an existing inventory count
     * @param {number} id - Inventory count ID
     * @param {Object} inventoryCountData - Updated inventory count data
     * @returns {Promise} API response
     */
    updateInventoryCount: (id, inventoryCountData) => {
        return api.put(`/inventory-counts/${id}/`, inventoryCountData);
    },

    /**
     * Delete an inventory count
     * @param {number} id - Inventory count ID
     * @returns {Promise} API response
     */
    deleteInventoryCount: (id) => {
        return api.delete(`/inventory-counts/${id}/`);
    },

    /**
     * Complete an inventory count
     * @param {number} id - Inventory count ID
     * @returns {Promise} API response
     */
    completeInventoryCount: (id) => {
        return api.post(`/inventory-counts/${id}/complete/`);
    },

    /**
     * Cancel an inventory count
     * @param {number} id - Inventory count ID
     * @returns {Promise} API response
     */
    cancelInventoryCount: (id) => {
        return api.post(`/inventory-counts/${id}/cancel/`);
    }
};
