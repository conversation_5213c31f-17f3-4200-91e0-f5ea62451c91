import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Container, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useLocation, useNavigate} from 'react-router-dom';
import {posService} from '../../services/posService';
import {format} from 'date-fns';
import {FaEye, FaSortAmountDown, FaSortAmountUp} from 'react-icons/fa';
import SessionStatusBadge from './SessionStatusBadge';

/**
 * POSSessionList component
 * Lists all POS sessions with filters and actions
 */
const POSSessionList = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [sessions, setSessions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [posFilter, setPosFilter] = useState('');
    const [dateFromFilter, setDateFromFilter] = useState('');
    const [dateToFilter, setDateToFilter] = useState('');
    const [orderBy, setOrderBy] = useState('-opened_at');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize] = useState(10);

    // POS terminals for filter dropdown
    const [posList, setPOSList] = useState([]);
    const [posListLoading, setPosListLoading] = useState(false);

    // Fetch sessions
    const fetchSessions = async (page = 1) => {
        const queryParams = new URLSearchParams(location.search);
        const posId = queryParams.get("pos");
        try {
            setLoading(true);
            setError('');
            const params = {
                page,
                page_size: pageSize,
                ordering: orderBy
            };

            if (searchTerm.trim()) {
                params.search = searchTerm.trim();
            }

            if (statusFilter) {
                params.status = statusFilter;
            }

            if (posId) {
                params.pos = posId;
            }

            if (posFilter) {
                params.pos = posFilter;
            }

            if (dateFromFilter) {
                params.opened_at_after = dateFromFilter;
            }

            if (dateToFilter) {
                params.opened_at_before = dateToFilter;
            }

            const response = await posService.getSessions(params);

            setSessions(response.data.results || []);
            setTotalCount(response.data.count || 0);
            setTotalPages(Math.ceil((response.data.count || 0) / pageSize));
        } catch (err) {
            console.error('Error fetching sessions:', err);
            setError('Failed to load sessions. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Handle ordering change
    const handleOrderChange = (newOrder) => {
        setOrderBy(newOrder);
        setCurrentPage(1);
        // Update the fetchSessions call to use the new ordering
        const updatedFetchSessions = async () => {
            try {
                setLoading(true);
                setError('');

                const params = {
                    page: 1,
                    page_size: pageSize,
                    ordering: newOrder
                };

                if (searchTerm.trim()) {
                    params.search = searchTerm.trim();
                }

                if (statusFilter) {
                    params.status = statusFilter;
                }

                if (posFilter) {
                    params.pos = posFilter;
                }

                if (dateFromFilter) {
                    params.opened_at_after = dateFromFilter;
                }

                if (dateToFilter) {
                    params.opened_at_before = dateToFilter;
                }

                const response = await posService.getSessions(params);

                setSessions(response.data.results || []);
                setTotalCount(response.data.count || 0);
                setTotalPages(Math.ceil((response.data.count || 0) / pageSize));

            } catch (err) {
                console.error('Error fetching sessions:', err);
                setError('Failed to load sessions. Please try again.');
            } finally {
                setLoading(false);
            }
        };
        updatedFetchSessions();
    };

    // Fetch POS terminals for filter dropdown
    const fetchPOSList = async () => {
        try {
            setPosListLoading(true);
            const response = await posService.getPOSList({page_size: 100}); // Get all POS terminals
            setPOSList(response.data.results || []);
        } catch (err) {
            console.error('Error fetching POS list:', err);
            // Don't show error for POS list fetch failure, just log it
        } finally {
            setPosListLoading(false);
        }
    };

    // Handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchSessions(page);
    };

    // Format currency
    const formatCurrency = (amount) => {
        if (amount === null || amount === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
        } catch (error) {
            return 'Invalid Date';
        }
    };

    // Get sort icon for column headers
    const getSortIcon = (field) => {
        if (orderBy === field) {
            return <FaSortAmountUp className="ms-1"/>;
        } else if (orderBy === `-${field}`) {
            return <FaSortAmountDown className="ms-1"/>;
        }
        return null;
    };

    // Handle column header click for sorting
    const handleColumnSort = (field) => {
        let newOrder;
        if (orderBy === field) {
            // Currently ascending, switch to descending
            newOrder = `-${field}`;
        } else if (orderBy === `-${field}`) {
            // Currently descending, switch to ascending
            newOrder = field;
        } else {
            // Not currently sorted by this field, default to descending for most fields
            // except for text fields where ascending makes more sense
            if (['pos_name', 'user_name', 'status'].includes(field)) {
                newOrder = field;
            } else {
                newOrder = `-${field}`;
            }
        }
        handleOrderChange(newOrder);
    };

    // Parse URL query parameters
    const parseQueryParams = () => {
        const queryParams = new URLSearchParams(location.search);
        const posId = queryParams.get('pos');
        if (posId) {
            setPosFilter(posId);
        }
    };

    // Initial load
    useEffect(() => {
        // Parse URL params first
        parseQueryParams();
        fetchPOSList();
        // Initial fetch of sessions - will include posFilter if it was set by parseQueryParams
        fetchSessions();
    }, []);

    // Render pagination
    const renderPagination = () => {
        if (totalPages <= 1) return null;

        const items = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return <Pagination className="justify-content-center">{items}</Pagination>;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Sessions</h2>
                        <Button
                            variant="primary"
                            onClick={() => navigate('/pos')}
                        >
                            Back to POS Terminals
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Filters */}
            <Row className="mb-4">
                <Col>
                    <Row>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    value={statusFilter}
                                    onChange={(e) => {
                                        setStatusFilter(e.target.value);
                                        setCurrentPage(1);
                                        fetchSessions(1);
                                    }}
                                >
                                    <option value="">All Statuses</option>
                                    <option value="open">Open</option>
                                    <option value="closed">Closed</option>
                                    <option value="suspended">Suspended</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>POS Terminal</Form.Label>
                                <Form.Select
                                    value={posFilter}
                                    onChange={(e) => {
                                        setPosFilter(e.target.value);
                                        setCurrentPage(1);
                                        fetchSessions(1);
                                    }}
                                    disabled={posListLoading}
                                >
                                    <option value="">All POS Terminals</option>
                                    {posList.map(pos => (
                                        <option key={pos.id} value={pos.id}>
                                            {pos.name}
                                        </option>
                                    ))}
                                </Form.Select>
                                {posListLoading && (
                                    <Form.Text className="text-muted">
                                        Loading POS terminals...
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Order By</Form.Label>
                                <Form.Select
                                    value={orderBy}
                                    onChange={(e) => handleOrderChange(e.target.value)}
                                >
                                    <option value="-opened_at">Newest First</option>
                                    <option value="opened_at">Oldest First</option>
                                    <option value="-id">ID (High to Low)</option>
                                    <option value="id">ID (Low to High)</option>
                                    <option value="pos_name">POS Terminal (A-Z)</option>
                                    <option value="-pos_name">POS Terminal (Z-A)</option>
                                    <option value="user_name">User (A-Z)</option>
                                    <option value="-user_name">User (Z-A)</option>
                                    <option value="status">Status (A-Z)</option>
                                    <option value="-status">Status (Z-A)</option>
                                    <option value="-opening_balance">Opening Balance (High to Low)</option>
                                    <option value="opening_balance">Opening Balance (Low to High)</option>
                                    <option value="-total_sales">Total Sales (High to Low)</option>
                                    <option value="total_sales">Total Sales (Low to High)</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2} className="d-flex align-items-end">
                            <Button
                                type="button"
                                variant="outline-secondary"
                                onClick={() => {
                                    setSearchTerm('');
                                    setStatusFilter('');
                                    setPosFilter('');
                                    setDateFromFilter('');
                                    setDateToFilter('');
                                    setOrderBy('-opened_at');
                                    setCurrentPage(1);
                                    fetchSessions(1);
                                }}
                            >
                                Clear All Filters
                            </Button>
                        </Col>
                    </Row>
                </Col>
            </Row>

            {/* Search */}
            <Row className="mb-4">
                <Col md={12}>
                    <Form>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Control
                                        type="text"
                                        placeholder="Search sessions..."
                                        value={searchTerm}
                                        onChange={(e) => {
                                            setSearchTerm(e.target.value);
                                            setCurrentPage(1);
                                            // Apply search after a short delay
                                            const timer = setTimeout(() => {
                                                fetchSessions(1);
                                            }, 300);
                                            return () => clearTimeout(timer);
                                        }}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Col>
            </Row>


            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Sessions List */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Sessions ({totalCount})</h5>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : sessions.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted mb-0">No sessions found.</p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                    <tr>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('id')}
                                            title="Click to sort by ID"
                                        >
                                            ID {getSortIcon('id')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('pos_name')}
                                            title="Click to sort by POS Terminal"
                                        >
                                            POS Terminal {getSortIcon('pos_name')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('user_name')}
                                            title="Click to sort by User"
                                        >
                                            User {getSortIcon('user_name')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('status')}
                                            title="Click to sort by Status"
                                        >
                                            Status {getSortIcon('status')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('opened_at')}
                                            title="Click to sort by Opened Date"
                                        >
                                            Opened {getSortIcon('opened_at')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('opening_balance')}
                                            title="Click to sort by Opening Balance"
                                        >
                                            Opening Balance {getSortIcon('opening_balance')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('total_sales')}
                                            title="Click to sort by Total Sales"
                                        >
                                            Total Sales {getSortIcon('total_sales')}
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {sessions.map((session) => (
                                        <tr key={session.id}>
                                            <td>#{session.id}</td>
                                            <td>{session.pos_name}</td>
                                            <td>{session.user_name}</td>
                                            <td>
                                                <SessionStatusBadge status={session.status}/>
                                            </td>
                                            <td>{formatDate(session.opened_at)}</td>
                                            <td>{formatCurrency(session.opening_balance)}</td>
                                            <td>{formatCurrency(session.total_sales)}</td>
                                            <td>
                                                <Button
                                                    variant="secondary"
                                                    onClick={() => navigate(`/pos/sessions/${session.id}`)}
                                                >
                                                    <FaEye className="me-2"/>
                                                    View Details
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Pagination */}
            {renderPagination()}

        </Container>
    );
};

export default POSSessionList;
