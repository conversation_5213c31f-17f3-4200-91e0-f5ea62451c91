from rest_framework import serializers

from products.models import Product
from purchases_return.models.purchase_return_item import PurchaseReturnItem


class PurchaseReturnItemSerializer(serializers.ModelSerializer):
    """
    Serializer for the PurchaseReturnItem model.
    Handles creation and updating of PurchaseReturnItem instances.
    """

    product_id = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(), source="product", write_only=True
    )
    product_name = serializers.StringRelatedField(source="product.name", read_only=True)
    total_cost = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="Calculated automatically as quantity × unit_cost",
    )

    class Meta:
        model = PurchaseReturnItem
        fields = [
            "id",
            "product_id",
            "product_name",
            "quantity",
            "unit_cost",
            "total_cost",
            "notes",
            "created",
            "modified",
        ]
        read_only_fields = ["id", "created", "modified", "total_cost"]

    def validate_quantity(self, value):
        """Validate that quantity is positive."""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0.")
        return value

    def validate_unit_cost(self, value):
        """Validate that unit_cost is positive."""
        if value <= 0:
            raise serializers.ValidationError("Unit cost must be greater than 0.")
        return value

    def validate(self, data):
        """
        Validate the return item data and calculate total_cost.
        """
        quantity = data.get("quantity")
        unit_cost = data.get("unit_cost")

        # If this is an update, get existing values if not provided
        if self.instance:
            quantity = quantity or self.instance.quantity
            unit_cost = unit_cost or self.instance.unit_cost

        # Ensure we have both values
        if quantity is None or unit_cost is None:
            raise serializers.ValidationError(
                "Both quantity and unit_cost are required."
            )

        return data

    def create(self, validated_data):
        """Create a new PurchaseReturnItem."""
        return PurchaseReturnItem.objects.create(**validated_data)

    def update(self, instance, validated_data):
        """Update an existing PurchaseReturnItem."""
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
