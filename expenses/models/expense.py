from django.core.validators import MinValueValidator
from django.db import models
from django_extensions.db.models import TimeStampedModel

from expenses.models.expense_category import ExpenseCategory
from pos.models.pos_session_transaction import POSSessionTransaction


class Expense(TimeStampedModel):
    """
    Model to track expenses in the system.
    """

    expense_category = models.ForeignKey(
        ExpenseCategory,
        on_delete=models.PROTECT,
        related_name="expenses",
        help_text="Category of the expense",
    )
    pos_session_transaction = models.OneToOneField(
        POSSessionTransaction,
        on_delete=models.PROTECT,
        related_name="expense",
        null=True,
        blank=True,
        help_text="Related POS session transaction if applicable",
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Amount of the expense",
        validators=[MinValueValidator(0.01)],
    )
    description = models.TextField(
        blank=True, null=True, help_text="Detailed description of the expense"
    )

    class Meta:
        ordering = ["-created"]
        verbose_name = "Expense"
        verbose_name_plural = "Expenses"

    def __str__(self):
        return f"{self.amount} - {self.expense_category} - {self.created}"
