from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from pos.models.pos import POS
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class POSFactory(DjangoModelFactory):
    """
    Factory for creating POS instances for testing.
    """

    class Meta:
        model = POS
        skip_postgeneration_save = True

    warehouse = SubFactory(WarehouseFactory)
    name = Faker("company")
    description = Faker("paragraph", nb_sentences=1)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unique names."""
        # Generate a unique name by appending a random string
        if "name" not in kwargs:
            base_name = Faker("company").generate({})
            counter = 1
            name = base_name
            while POS.objects.filter(name=name).exists():
                name = f"{base_name} {counter}"
                counter += 1
            kwargs["name"] = name
        return super()._create(model_class, *args, **kwargs)
