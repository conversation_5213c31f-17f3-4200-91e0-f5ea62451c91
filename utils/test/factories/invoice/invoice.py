import factory
import random
from decimal import Decimal
from factory import SubFactory, LazyAttribute
from factory.django import DjangoModelFactory

from invoices.models.invoice import Invoice
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from utils.test.factories.pos.pos_session import POSSessionFactory


class InvoiceFactory(DjangoModelFactory):
    """
    Factory for creating Invoice instances for testing.
    """
    class Meta:
        model = Invoice
        skip_postgeneration_save = True

    warehouse = SubFactory(WarehouseFactory)
    pos_session = SubFactory(POSSessionFactory)
    
    # Generate random amounts
    net_amount = LazyAttribute(lambda _: Decimal(random.uniform(50, 1000)).quantize(Decimal('0.01')))
    profit = LazyAttribute(lambda _: Decimal(random.uniform(10, 200)).quantize(Decimal('0.01')))
    
    # Default values
    payment_method = 'cash'
    notes = factory.Faker('paragraph', nb_sentences=1)
    
    @factory.post_generation
    def items(self, create, extracted, **kwargs):
        """
        Create invoice items if specified.
        Usage:
            invoice = InvoiceFactory.create(items=3)  # Creates an invoice with 3 random items
            invoice = InvoiceFactory.create(items=[item1, item2])  # Creates an invoice with specific items
        """
        from utils.test.factories.invoice.invoice_items import InvoiceItemFactory
        
        if not create:
            return
            
        if extracted:
            if isinstance(extracted, int):
                # Create the specified number of items
                for _ in range(extracted):
                    InvoiceItemFactory.create(invoice=self)
            else:
                # Add the specified items
                for item in extracted:
                    self.items.add(item)