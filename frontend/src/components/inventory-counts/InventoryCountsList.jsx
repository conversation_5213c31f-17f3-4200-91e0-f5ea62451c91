import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {useInventoryCounts} from '../../hooks/useInventoryCounts';
import {useWarehouses} from '../../hooks/useWarehouses';
import DeleteConfirmModal from './DeleteConfirmModal';

/**
 * Component for displaying a list of inventory counts with search, sort, and pagination
 * @returns {JSX.Element} The inventory counts list component
 */
const InventoryCountsList = () => {
    const navigate = useNavigate();
    const {currentUser} = useAuth();
    const {
        inventoryCounts,
        loading,
        error,
        pagination,
        fetchInventoryCounts,
        deleteInventoryCount,
        setError
    } = useInventoryCounts();
    const {warehouses, fetchWarehouses} = useWarehouses();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [warehouseFilter, setWarehouseFilter] = useState('');
    const [ordering, setOrdering] = useState('-created');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [inventoryCountToDelete, setInventoryCountToDelete] = useState(null);

    // Load inventory counts and warehouses on component mount
    useEffect(() => {
        fetchInventoryCounts({
            page: 1,
            page_size: 10,
            ordering: '-created'
        });
        fetchWarehouses();
    }, [fetchInventoryCounts, fetchWarehouses]);

    // Handle search and filter changes
    const handleSearch = () => {
        const params = {
            page: 1,
            page_size: pagination.page_size,
            ordering
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }
        if (statusFilter) {
            params.status = statusFilter;
        }
        if (warehouseFilter) {
            params.warehouse = warehouseFilter;
        }

        fetchInventoryCounts(params);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        const params = {
            page,
            page_size: pagination.page_size,
            ordering
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }
        if (statusFilter) {
            params.status = statusFilter;
        }
        if (warehouseFilter) {
            params.warehouse = warehouseFilter;
        }

        fetchInventoryCounts(params);
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);

        const params = {
            page: 1,
            page_size: pagination.page_size,
            ordering: newOrdering
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }
        if (statusFilter) {
            params.status = statusFilter;
        }
        if (warehouseFilter) {
            params.warehouse = warehouseFilter;
        }

        fetchInventoryCounts(params);
    };

    // Get sort icon
    const getSortIcon = (field) => {
        if (ordering === field) {
            return ' ↑';
        } else if (ordering === `-${field}`) {
            return ' ↓';
        }
        return '';
    };

    // Handle delete confirmation
    const handleDeleteClick = (inventoryCount) => {
        setInventoryCountToDelete(inventoryCount);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            await deleteInventoryCount(inventoryCountToDelete.id);
            setShowDeleteModal(false);
            setInventoryCountToDelete(null);
        } catch (error) {
            console.error('Delete failed:', error);
        }
    };

    // Get status badge variant
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'draft':
                return 'secondary';
            case 'in_progress':
                return 'primary';
            case 'completed':
                return 'success';
            case 'cancelled':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    // Check if user can edit/delete
    const canEdit = (inventoryCount) => {
        return currentUser?.role === 'admin' &&
            ['draft', 'in_progress'].includes(inventoryCount.status);
    };

    const canDelete = (inventoryCount) => {
        return currentUser?.role === 'admin' &&
            inventoryCount.status === 'draft';
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Inventory Counts</h2>
                        {currentUser?.role === 'admin' && (
                            <Button
                                variant="primary"
                                onClick={() => navigate('/inventory-counts/create')}
                            >
                                New Inventory Count
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Section */}
            <Card className="mb-4">
                <Card.Body>
                    <Row>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search inventory counts..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                >
                                    <option value="">All Statuses</option>
                                    <option value="draft">Draft</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Warehouse</Form.Label>
                                <Form.Select
                                    value={warehouseFilter}
                                    onChange={(e) => setWarehouseFilter(e.target.value)}
                                >
                                    <option value="">All Warehouses</option>
                                    {warehouses.map(warehouse => (
                                        <option key={warehouse.id} value={warehouse.id}>
                                            {warehouse.name}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2} className="d-flex align-items-end">
                            <Button variant="outline-primary" onClick={handleSearch}>
                                Search
                            </Button>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Inventory Counts Table */}
            <Card>
                <Card.Body>
                    {loading ? (
                        <div className="text-center py-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : inventoryCounts.length === 0 ? (
                        <div className="text-center py-4">
                            <p className="text-muted">No inventory counts found.</p>
                            {currentUser?.role === 'admin' && (
                                <Button
                                    variant="primary"
                                    onClick={() => navigate('/inventory-counts/create')}
                                >
                                    Create First Inventory Count
                                </Button>
                            )}
                        </div>
                    ) : (
                        <>
                            <Table responsive striped hover>
                                <thead className="listing-table-header">
                                <tr>
                                    <th onClick={() => handleSort('id')}>
                                        ID{getSortIcon('id')}
                                    </th>
                                    <th onClick={() => handleSort('warehouse__name')}>
                                        Warehouse{getSortIcon('warehouse__name')}
                                    </th>
                                    <th onClick={() => handleSort('status')}>
                                        Status{getSortIcon('status')}
                                    </th>
                                    <th onClick={() => handleSort('started_at')}>
                                        Started At{getSortIcon('started_at')}
                                    </th>
                                    <th onClick={() => handleSort('ended_at')}>
                                        Ended At{getSortIcon('ended_at')}
                                    </th>
                                    <th>Item Count</th>
                                    <th>Total Cost</th>
                                    <th>Total Price</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                {inventoryCounts.map((inventoryCount) => (
                                    <tr key={inventoryCount.id}>
                                        <td>
                                            <strong>#{inventoryCount.id}</strong>
                                        </td>
                                        <td>{inventoryCount.warehouse_name}</td>
                                        <td>
                                            <Badge bg={getStatusBadgeVariant(inventoryCount.status)}>
                                                {inventoryCount.status_display}
                                            </Badge>
                                        </td>
                                        <td>
                                            {inventoryCount.started_at ?
                                                new Date(inventoryCount.started_at).toLocaleString() :
                                                '-'
                                            }
                                        </td>
                                        <td>
                                            {inventoryCount.ended_at ?
                                                new Date(inventoryCount.ended_at).toLocaleString() :
                                                '-'
                                            }
                                        </td>
                                        <td>{inventoryCount.item_count || 0}</td>
                                        <td>${inventoryCount.total_cost || '0.00'}</td>
                                        <td>${inventoryCount.total_price || '0.00'}</td>
                                        <td>
                                            <div className="d-flex gap-1">
                                                <Button
                                                    variant="outline-info"
                                                    size="sm"
                                                    onClick={() => navigate(`/inventory-counts/${inventoryCount.id}`)}
                                                >
                                                    View
                                                </Button>
                                                {canEdit(inventoryCount) && (
                                                    <Button
                                                        variant="outline-success"
                                                        size="sm"
                                                        onClick={() => navigate(`/inventory-counts/${inventoryCount.id}/edit`)}
                                                    >
                                                        Edit
                                                    </Button>
                                                )}
                                                {canDelete(inventoryCount) && (
                                                    <Button
                                                        variant="outline-danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteClick(inventoryCount)}
                                                    >
                                                        Delete
                                                    </Button>
                                                )}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {pagination.count > pagination.page_size && (
                                <div className="d-flex justify-content-center mt-3">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={pagination.page === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(pagination.page - 1)}
                                            disabled={pagination.page === 1}
                                        />

                                        {/* Page numbers */}
                                        {Array.from({length: Math.ceil(pagination.count / pagination.page_size)}, (_, i) => i + 1)
                                            .filter(page => Math.abs(page - pagination.page) <= 2)
                                            .map(page => (
                                                <Pagination.Item
                                                    key={page}
                                                    active={page === pagination.page}
                                                    onClick={() => handlePageChange(page)}
                                                >
                                                    {page}
                                                </Pagination.Item>
                                            ))
                                        }

                                        <Pagination.Next
                                            onClick={() => handlePageChange(pagination.page + 1)}
                                            disabled={pagination.page === Math.ceil(pagination.count / pagination.page_size)}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(Math.ceil(pagination.count / pagination.page_size))}
                                            disabled={pagination.page === Math.ceil(pagination.count / pagination.page_size)}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </>
                    )}
                </Card.Body>
            </Card>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                inventoryCount={inventoryCountToDelete}
                loading={loading}
            />
        </Container>
    );
};

export default InventoryCountsList;
