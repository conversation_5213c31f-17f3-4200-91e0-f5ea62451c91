# Generated by Django 5.2.1 on 2025-07-19 11:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("invoices", "0004_alter_invoice_net_amount_alter_invoice_profit"),
    ]

    operations = [
        migrations.AlterField(
            model_name="invoiceitem",
            name="profit",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Profit amount for this item",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.01)],
            ),
        ),
        migrations.AlterField(
            model_name="invoiceitem",
            name="quantity",
            field=models.DecimalField(
                decimal_places=2,
                default=1,
                help_text="Quantity of the product",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(0.01)],
            ),
        ),
        migrations.AlterField(
            model_name="invoiceitem",
            name="total_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Total price (quantity × unit_price)",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.01)],
            ),
        ),
        migrations.AlterField(
            model_name="invoiceitem",
            name="unit_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Price per unit of the product",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.01)],
            ),
        ),
    ]
