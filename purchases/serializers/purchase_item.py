from rest_framework import serializers

from products.models import Product
from purchases.models.purchase_item import PurchaseItem


class PurchaseItemSerializer(serializers.ModelSerializer):
    """
    Serializer for the PurchaseItem model.
    Handles creation and updating of PurchaseItem instances.
    """

    product_id = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(), source="product", write_only=True
    )
    product_name = serializers.StringRelatedField(source="product.name", read_only=True)
    total_cost = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = PurchaseItem
        fields = [
            "id",
            "product_id",
            "product_name",
            "quantity",
            "unit_cost",
            "unit_price",
            "total_cost",
            "notes",
            "created",
            "modified",
        ]
        read_only_fields = ["id", "created", "modified"]

    def validate(self, data):
        """
        Calculate total_cost based on quantity and unit_cost.
        """
        quantity = data.get("quantity")
        unit_cost = data.get("unit_cost")
        if unit_cost >= data.get("unit_price"):
            raise serializers.ValidationError(
                {
                    "unit_cost": f"Unit cost (${unit_cost}) cannot be greater than unit price (${data.get('unit_price')})"
                }
            )
        data["total_cost"] = quantity * unit_cost
        return data
