import random
from decimal import Decimal

from factory import Faker, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from products.models.product import Product
from utils.test.factories.product.category import CategoryFactory


class ProductFactory(DjangoModelFactory):
    """
    Factory for creating Product instances for testing.
    """

    class Meta:
        model = Product
        skip_postgeneration_save = True

    category = SubFactory(CategoryFactory)
    name = Faker("word")
    description = Faker("paragraph", nb_sentences=2)

    # Generate a cost between 5 and 100
    cost = LazyAttribute(
        lambda _: Decimal(random.uniform(5, 100)).quantize(Decimal("0.01"))
    )

    # Generate a price that's higher than the cost
    price = LazyAttribute(
        lambda o: o.cost * Decimal(random.uniform(1.1, 2.0)).quantize(Decimal("0.01"))
    )

    # Generate a unique barcode
    barcode = LazyAttribute(lambda _: f"{random.randint(1000000000, 9999999999)}")

    # Default unit type is piece
    unit_type = "piece"

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unique names and barcodes."""
        # Generate a unique name by appending a random string
        if "name" not in kwargs:
            base_name = Faker("word").generate({})
            counter = 1
            name = base_name
            while Product.objects.filter(name=name).exists():
                name = f"{base_name} {counter}"
                counter += 1
            kwargs["name"] = name

        # Generate a unique barcode if not provided
        if "barcode" not in kwargs:
            barcode = f"{random.randint(1000000000, 9999999999)}"
            while Product.objects.filter(barcode=barcode).exists():
                barcode = f"{random.randint(1000000000, 9999999999)}"
            kwargs["barcode"] = barcode

        return super()._create(model_class, *args, **kwargs)
