import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Form, Row} from 'react-bootstrap';
import {Link, useNavigate} from 'react-router-dom';
import {useAuth} from "../../contexts/AuthContext";

const Login = () => {
    const [formData, setFormData] = useState({
        username: '',
        password: '',
    });
    const [formError, setFormError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const {login, error} = useAuth();
    const navigate = useNavigate();

    const handleChange = (e) => {
        const {name, value} = e.target;
        setFormData({...formData, [name]: value});
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setFormError('');

        // Simple validation
        if (!formData.username || !formData.password) {
            setFormError('Please enter both username and password');
            return;
        }

        try {
            setIsSubmitting(true);
            const response = await login(formData.username, formData.password);

            // if (response.user.role.key === 'admin') {
            //     navigate('/admin/dashboard');
            // } else {
            //     navigate('/client/dashboard');
            // }
            navigate('/');
        } catch (err) {
            console.error('Login failed:', err);
            setFormError(err.response?.data?.detail || 'Login failed. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Container className="mt-5">
            <Row className="justify-content-center">
                <Col md={6}>
                    <Card>
                        <Card.Header as="h4" className="text-center">Login</Card.Header>
                        <Card.Body>
                            {(formError || error) && (
                                <Alert variant="danger">{formError || error}</Alert>
                            )}

                            <Form onSubmit={handleSubmit}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Username</Form.Label>
                                    <Form.Control
                                        type="text"
                                        name="username"
                                        value={formData.username}
                                        onChange={handleChange}
                                        placeholder="Enter username"
                                        required
                                    />
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label>Password</Form.Label>
                                    <Form.Control
                                        type="password"
                                        name="password"
                                        value={formData.password}
                                        onChange={handleChange}
                                        placeholder="Enter password"
                                        required
                                    />
                                </Form.Group>

                                <div className="d-grid gap-2">
                                    <Button
                                        variant="primary"
                                        type="submit"
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? 'Logging in...' : 'Login'}
                                    </Button>
                                </div>
                            </Form>

                            <div className="mt-3 text-center">
                                <p>
                                    Don't have an account? <Link to="/register">Register here</Link>
                                </p>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Login;