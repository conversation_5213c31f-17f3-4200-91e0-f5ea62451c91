import { useState, useEffect, useCallback } from 'react';
import { categoriesService } from '../services/categoriesService';

/**
 * Custom hook for managing categories data and operations
 * Provides state management and API integration for categories CRUD
 */
export const useCategories = () => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch categories with optional parameters
     */
    const fetchCategories = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await categoriesService.getCategories(params);
            
            setCategories(response.data.results || response.data);
            
            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch categories');
            console.error('Error fetching categories:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new category
     */
    const createCategory = async (categoryData) => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await categoriesService.createCategory(categoryData);
            
            // Refresh the categories list
            await fetchCategories({ page: pagination.page, page_size: pagination.page_size });
            
            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail || 
                               err.response?.data?.name?.[0] || 
                               'Failed to create category';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Update an existing category
     */
    const updateCategory = async (id, categoryData) => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await categoriesService.updateCategory(id, categoryData);
            
            // Update the category in the local state
            setCategories(prev => 
                prev.map(cat => cat.id === id ? response.data : cat)
            );
            
            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail || 
                               err.response?.data?.name?.[0] || 
                               'Failed to update category';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Delete a category
     */
    const deleteCategory = async (id) => {
        try {
            setLoading(true);
            setError(null);
            
            await categoriesService.deleteCategory(id);
            
            // Remove the category from local state
            setCategories(prev => prev.filter(cat => cat.id !== id));
            
            // If this was the last item on the page and we're not on page 1, go to previous page
            if (categories.length === 1 && pagination.page > 1) {
                await fetchCategories({ 
                    page: pagination.page - 1, 
                    page_size: pagination.page_size 
                });
            } else {
                // Refresh current page
                await fetchCategories({ 
                    page: pagination.page, 
                    page_size: pagination.page_size 
                });
            }
        } catch (err) {
            const errorMessage = err.response?.data?.detail || 'Failed to delete category';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    };

    return {
        categories,
        loading,
        error,
        pagination,
        fetchCategories,
        createCategory,
        updateCategory,
        deleteCategory,
        setError
    };
};

/**
 * Custom hook for managing a single category
 */
export const useCategory = (id) => {
    const [category, setCategory] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchCategory = useCallback(async () => {
        if (!id) return;
        
        try {
            setLoading(true);
            setError(null);
            
            const response = await categoriesService.getCategory(id);
            setCategory(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch category');
            console.error('Error fetching category:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchCategory();
    }, [fetchCategory]);

    return {
        category,
        loading,
        error,
        refetch: fetchCategory
    };
};

/**
 * Custom hook for managing parent categories
 */
export const useParentCategories = () => {
    const [parentCategories, setParentCategories] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchParentCategories = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await categoriesService.getParentCategories();
            setParentCategories(response.data.results || response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch parent categories');
            console.error('Error fetching parent categories:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchParentCategories();
    }, [fetchParentCategories]);

    return {
        parentCategories,
        loading,
        error,
        refetch: fetchParentCategories
    };
};

export default useCategories;
