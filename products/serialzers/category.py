from rest_framework import serializers

from products.models.category import Category


class CategorySerializer(serializers.ModelSerializer):
    parent_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        source="parent",
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Category
        fields = ["id", "name", "description", "parent_id", "created", "modified"]
        read_only_fields = ["created", "modified"]

    def validate_parent_id(self, value):
        """Prevent circular category references"""
        if self.instance and value and self.instance.id == value.id:
            raise serializers.ValidationError(
                "A category cannot be a parent of itself."
            )
        return value
