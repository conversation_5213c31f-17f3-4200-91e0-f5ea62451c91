import random
from decimal import Decimal

import factory
from factory import <PERSON>zy<PERSON><PERSON><PERSON>bute, SubFactory
from factory.django import DjangoModelFactory

from purchases.models.purchase_item import PurchaseItem
from utils.test.factories.product.product import ProductFactory


class PurchaseItemFactory(DjangoModelFactory):
    """
    Factory for creating PurchaseItem instances for testing.
    """

    class Meta:
        model = PurchaseItem
        skip_postgeneration_save = True

    # Purchase will be set when creating items through PurchaseFactory
    # or can be specified directly when creating standalone items
    purchase = None
    
    product = SubFactory(ProductFactory)
    
    # Generate a random quantity between 1 and 20
    quantity = LazyAttribute(
        lambda _: Decimal(random.uniform(1, 20)).quantize(Decimal('0.01'))
    )
    
    # Use the product's cost as the unit cost (with some variation)
    unit_cost = LazyAttribute(
        lambda o: o.product.cost * Decimal(random.uniform(0.8, 1.2)).quantize(Decimal('0.01'))
    )
    
    # Use the product's price as the unit price (with some variation)
    unit_price = LazyAttribute(
        lambda o: o.product.price * Decimal(random.uniform(0.9, 1.1)).quantize(Decimal('0.01'))
    )
    
    # Calculate total_cost based on quantity and unit_cost
    total_cost = LazyAttribute(
        lambda o: (o.quantity * o.unit_cost).quantize(Decimal('0.01'))
    )
    
    notes = factory.Faker('sentence')

    @classmethod
    def create_with_zero_cost(cls, **kwargs):
        """
        Create a purchase item with zero cost (bonus item).
        
        Usage:
            item = PurchaseItemFactory.create_with_zero_cost(purchase=purchase)
        """
        kwargs.update({
            'unit_cost': Decimal('0.00'),
            'total_cost': Decimal('0.00')
        })
        return cls.create(**kwargs)

    @classmethod
    def create_with_specific_cost(cls, unit_cost, **kwargs):
        """
        Create a purchase item with a specific unit cost.
        
        Usage:
            item = PurchaseItemFactory.create_with_specific_cost(
                unit_cost=Decimal('15.50'),
                purchase=purchase
            )
        """
        unit_cost = Decimal(str(unit_cost))
        kwargs.update({
            'unit_cost': unit_cost,
            'total_cost': kwargs.get('quantity', Decimal('1.00')) * unit_cost
        })
        return cls.create(**kwargs)
