from django.contrib.auth import get_user_model
from django.db import models
from django.db.models import DecimalField, F, Sum, Value
from django.db.models.functions import Coalesce
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from warehouses.models.inventory_count_item import InventoryCountItem

User = get_user_model()


def create_inventory_count_items(inventory_count):
    inventory_count_items = []
    for stock_item in inventory_count.warehouse.stock_items.all():
        inventory_count_items.append(
            InventoryCountItem(inventory_count=inventory_count, stock_item=stock_item)
        )
    inventory_count_items = InventoryCountItem.objects.bulk_create(
        inventory_count_items
    )
    return inventory_count_items


class InventoryCountManager(models.Manager):
    def create(self, **kwargs):
        if self.filter(
            warehouse=kwargs["warehouse"],
            status__in=[InventoryCount.Status.IN_PROGRESS, InventoryCount.Status.DRAFT],
        ).exists():
            raise ValueError(_("Inventory count already exists"))
        inventory_count = super().create(**kwargs)
        create_inventory_count_items(inventory_count)
        return inventory_count


class InventoryCount(TimeStampedModel):
    """
    InventoryCount model represents a physical inventory counting session
    to verify stock levels in a warehouse.
    """

    class Status(models.TextChoices):
        DRAFT = "draft", _("Draft")
        IN_PROGRESS = "in_progress", _("In Progress")
        COMPLETED = "completed", _("Completed")
        CANCELLED = "cancelled", _("Cancelled")

    warehouse = models.ForeignKey(
        "warehouses.Warehouse",
        on_delete=models.CASCADE,
        related_name="inventory_counts",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse where the inventory count is being performed"),
    )

    status = models.CharField(
        _("status"),
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        help_text=_("Current status of the inventory count"),
    )

    started_at = models.DateTimeField(
        _("started at"),
        null=True,
        blank=True,
        help_text=_("When the inventory count was started"),
    )

    ended_at = models.DateTimeField(
        _("ended at"),
        null=True,
        blank=True,
        help_text=_("When the inventory count was completed or cancelled"),
    )

    noted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="inventory_counts",
        verbose_name=_("noted by"),
        help_text=_("User who created or is responsible for this inventory count"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes or comments about the inventory count"),
    )

    total_cost = models.DecimalField(
        _("total cost"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Total cost of inventory based on difference and product costs"),
    )
    total_price = models.DecimalField(
        _("total price"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Total cost of inventory based on difference and product price"),
    )

    class Meta:
        verbose_name = _("inventory count")
        verbose_name_plural = _("inventory counts")
        ordering = ["-created"]

    objects = InventoryCountManager()

    def __str__(self):
        return f"{self.warehouse} - {self.get_status_display()} ({self.created.date()})"

    def start(self, user):
        """Mark the inventory count as in progress"""
        if self.status != self.Status.DRAFT:
            raise ValueError(_("Only draft counts can be started"))

        self.status = self.Status.IN_PROGRESS
        self.started_at = timezone.now()
        self.noted_by = user
        self.save(update_fields=["status", "started_at", "noted_by", "modified"])

    def calculate_total_cost(self):
        """
        Calculate the total cost of inventory based on difference and product costs.
        Uses database aggregation for better performance.
        """

        # Calculate total cost using database aggregation
        cost = self.items.annotate(
            item_cost=Coalesce(
                F("difference") * F("stock_item__product__cost"),
                Value(0, output_field=DecimalField()),
            )
        ).aggregate(total=Sum("item_cost"))

        return cost["total"] or 0

    def calculate_total_price(self):
        """
        Calculate the total cost of inventory based on difference and product prices.
        Uses database aggregation for better performance.
        """

        # Calculate total cost using database aggregation
        price = self.items.annotate(
            item_cost=Coalesce(
                F("difference") * F("stock_item__product__price"),
                Value(0, output_field=DecimalField()),
            )
        ).aggregate(total=Sum("item_cost"))

        return price["total"] or 0

    def complete(self):
        """Mark the inventory count as completed and calculate total cost"""
        if self.status != self.Status.IN_PROGRESS:
            raise ValueError(_("Only counts in progress can be completed"))

        # Calculate and save the total cost before completing
        self.total_cost = self.calculate_total_cost()
        self.total_price = self.calculate_total_price()
        self.status = self.Status.COMPLETED
        self.ended_at = timezone.now()
        self.save(
            update_fields=[
                "total_cost",
                "total_price",
                "status",
                "ended_at",
                "modified",
            ]
        )
        # Create a new inventory count for the next count
        self.__class__.objects.create(warehouse=self.warehouse)

    def cancel(self):
        """Cancel the inventory count"""
        self.status = self.Status.CANCELLED
        self.ended_at = timezone.now()
        self.save(update_fields=["status", "ended_at", "modified"])
        # Create a new inventory count for the next count
        InventoryCount.objects.create(warehouse=self.warehouse)
