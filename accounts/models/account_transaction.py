from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django_extensions.db.models import TimeStampedModel

from .balance import Balance


class TransactionType(models.TextChoices):
    DEBIT = "debit", "Debit"
    CREDIT = "credit", "Credit"


class AccountTransaction(TimeStampedModel):
    """
    Model to track all transactions related to an account.
    Uses generic foreign key for flexible relationship with different models.
    """

    balance = models.ForeignKey(
        Balance,
        on_delete=models.CASCADE,
        related_name="transactions",
        help_text="The balance record of the account this transaction belongs to",
    )

    type = models.CharField(
        max_length=20,
        choices=TransactionType.choices,
        help_text="Type of transaction (debit/credit)",
    )

    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Transaction amount (always positive)",
    )

    # Generic relation to any model
    related_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="The model type this transaction is related to",
    )
    related_object_id = models.PositiveIntegerField(
        null=True, blank=True, help_text="ID of the related object"
    )
    related_object = GenericForeignKey("related_content_type", "related_object_id")

    description = models.TextField(
        blank=True, null=True, help_text="Additional details about the transaction"
    )

    class Meta:
        verbose_name = "Account Transaction"
        verbose_name_plural = "Account Transactions"
        ordering = ["-created"]

    def __str__(self):
        return f"{self.get_type_display()} of {self.amount} on {self.created.strftime('%Y-%m-%d')}"

    def save(self, *args, **kwargs):
        # Calculate the amount to add/subtract from balance
        if self.type == TransactionType.DEBIT:
            amount = -self.amount
        else:
            amount = self.amount

        # Update the account balance
        self.balance.amount += amount
        self.balance.save(update_fields=["amount", "modified"])

        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # Calculate the amount to add/subtract from balance
        if self.type == TransactionType.DEBIT:
            amount = self.amount
        else:
            amount = -self.amount

        # Update the account balance
        self.balance.amount += amount
        self.balance.save(update_fields=["amount", "modified"])

        super().delete(*args, **kwargs)
