import random
from decimal import Decimal

import factory
from factory import Lazy<PERSON><PERSON><PERSON>bute, SubFactory, post_generation
from factory.django import DjangoModelFactory

from purchases.models.purchase import Purchase
from utils.test.factories.purchases.supplier import SupplierFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class PurchaseFactory(DjangoModelFactory):
    """
    Factory for creating Purchase instances for testing.
    """

    class Meta:
        model = Purchase
        skip_postgeneration_save = True

    warehouse = SubFactory(WarehouseFactory)
    supplier = SubFactory(SupplierFactory)
    
    # Generate random amounts
    total_amount = LazyAttribute(
        lambda _: Decimal(random.uniform(100, 2000)).quantize(Decimal('0.01'))
    )
    
    discount = LazyAttribute(
        lambda o: (o.total_amount * Decimal(random.uniform(0, 0.1))).quantize(Decimal('0.01'))
    )
    
    bonus = LazyAttribute(
        lambda _: Decimal(random.uniform(0, 100)).quantize(Decimal('0.01'))
    )
    
    # Calculate net_amount (will be recalculated in serializer)
    net_amount = LazyAttribute(
        lambda o: (o.total_amount - o.discount).quantize(Decimal('0.01'))
    )
    
    # Generate paid amount (can be partial payment)
    paid_amount = LazyAttribute(
        lambda o: (o.net_amount * Decimal(random.uniform(0.5, 1.0))).quantize(Decimal('0.01'))
    )
    
    # Calculate reminder amount
    reminder_amount = LazyAttribute(
        lambda o: (o.net_amount - o.paid_amount).quantize(Decimal('0.01'))
    )
    
    payment_method = 'cash'
    notes = factory.Faker('paragraph', nb_sentences=1)

    @post_generation
    def items(self, create, extracted, **kwargs):
        """
        Create purchase items if specified.
        Usage:
            purchase = PurchaseFactory.create(items=3)  # Creates a purchase with 3 random items
            purchase = PurchaseFactory.create(items=[item1, item2])  # Creates a purchase with specific items
        """
        from utils.test.factories.purchases.purchase_item import PurchaseItemFactory
        
        if not create:
            return
            
        if extracted:
            if isinstance(extracted, int):
                # Create the specified number of items
                for _ in range(extracted):
                    PurchaseItemFactory.create(purchase=self)
            else:
                # Add the specified items
                for item in extracted:
                    if hasattr(item, 'purchase'):
                        item.purchase = self
                        item.save()
                    else:
                        self.items.add(item)

    @classmethod
    def create_with_full_payment(cls, **kwargs):
        """
        Create a purchase with full payment (no reminder amount).
        
        Usage:
            purchase = PurchaseFactory.create_with_full_payment()
        """
        purchase = cls.build(**kwargs)
        purchase.paid_amount = purchase.net_amount
        purchase.reminder_amount = Decimal('0.00')
        return cls._create(cls._meta.model, **purchase.__dict__)

    @classmethod
    def create_with_partial_payment(cls, payment_percentage=0.5, **kwargs):
        """
        Create a purchase with partial payment.
        
        Usage:
            purchase = PurchaseFactory.create_with_partial_payment(payment_percentage=0.3)
        """
        purchase = cls.build(**kwargs)
        purchase.paid_amount = (purchase.net_amount * Decimal(str(payment_percentage))).quantize(Decimal('0.01'))
        purchase.reminder_amount = (purchase.net_amount - purchase.paid_amount).quantize(Decimal('0.01'))
        return cls._create(cls._meta.model, **purchase.__dict__)

    @classmethod
    def create_credit_purchase(cls, **kwargs):
        """
        Create a credit purchase (no payment made).
        
        Usage:
            purchase = PurchaseFactory.create_credit_purchase()
        """
        purchase = cls.build(**kwargs)
        purchase.paid_amount = Decimal('0.00')
        purchase.reminder_amount = purchase.net_amount
        return cls._create(cls._meta.model, **purchase.__dict__)
