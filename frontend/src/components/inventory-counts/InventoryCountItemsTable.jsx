import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import { useInventoryCountItems } from '../../hooks/useInventoryCountItems';

/**
 * Component for displaying and managing inventory count items
 * @param {Object} props - Component props
 * @param {number} props.inventoryCountId - Inventory count ID
 * @param {string} props.inventoryCountStatus - Current status of inventory count
 * @param {Function} props.onItemUpdate - Callback when items are updated
 * @returns {JSX.Element} The inventory count items table component
 */
const InventoryCountItemsTable = ({ 
    inventoryCountId, 
    inventoryCountStatus,
    onItemUpdate 
}) => {
    const {
        inventoryCountItems,
        loading,
        error,
        pagination,
        fetchInventoryCountItems,
        bulkUpdateInventoryCountItems,
        setError
    } = useInventoryCountItems(inventoryCountId);

    // Local state for editing and filters
    const [searchTerm, setSearchTerm] = useState('');
    const [discrepancyFilter, setDiscrepancyFilter] = useState('');
    const [pageSize, setPageSize] = useState(25);
    const [editingItems, setEditingItems] = useState({});
    const [selectedItems, setSelectedItems] = useState(new Set());
    const [bulkQuantity, setBulkQuantity] = useState('');
    const [saving, setSaving] = useState(false);

    // Check if editing is allowed
    const canEdit = ['draft', 'in_progress'].includes(inventoryCountStatus);

    // Handle search and filter
    const handleSearch = () => {
        const params = {
            page: 1,
            page_size: pageSize
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        fetchInventoryCountItems(params);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        const params = {
            page,
            page_size: pageSize
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        fetchInventoryCountItems(params);
    };

    // Handle page size change
    const handlePageSizeChange = (newPageSize) => {
        setPageSize(newPageSize);
        const params = {
            page: 1,
            page_size: newPageSize
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        fetchInventoryCountItems(params);
    };

    // Handle inline editing
    const handleEditChange = (itemId, field, value) => {
        setEditingItems(prev => ({
            ...prev,
            [itemId]: {
                ...prev[itemId],
                [field]: value
            }
        }));
    };

    // Save individual item changes
    const saveItemChanges = async (item) => {
        if (!editingItems[item.id]) return;

        try {
            setSaving(true);
            const updates = editingItems[item.id];
            
            const updateData = [];
            if (updates.recorded_quantity !== undefined) {
                updateData.push({
                    id: item.id,
                    recorded_quantity: parseFloat(updates.recorded_quantity) || 0,
                    notes: updates.notes !== undefined ? updates.notes : item.notes
                });
            } else if (updates.notes !== undefined) {
                updateData.push({
                    id: item.id,
                    recorded_quantity: item.recorded_quantity,
                    notes: updates.notes
                });
            }

            if (updateData.length > 0) {
                await bulkUpdateInventoryCountItems(updateData);
                
                // Clear editing state for this item
                setEditingItems(prev => {
                    const newState = { ...prev };
                    delete newState[item.id];
                    return newState;
                });

                if (onItemUpdate) {
                    onItemUpdate();
                }
            }
        } catch (err) {
            console.error('Error saving item changes:', err);
        } finally {
            setSaving(false);
        }
    };

    // Handle bulk quantity update
    const handleBulkUpdate = async () => {
        if (selectedItems.size === 0 || !bulkQuantity) return;

        try {
            setSaving(true);
            const updateData = Array.from(selectedItems).map(itemId => {
                const item = inventoryCountItems.find(i => i.id === itemId);
                return {
                    id: itemId,
                    recorded_quantity: parseFloat(bulkQuantity),
                    notes: item?.notes || ''
                };
            });

            await bulkUpdateInventoryCountItems(updateData);
            
            // Clear selections and bulk quantity
            setSelectedItems(new Set());
            setBulkQuantity('');

            if (onItemUpdate) {
                onItemUpdate();
            }
        } catch (err) {
            console.error('Error bulk updating items:', err);
        } finally {
            setSaving(false);
        }
    };

    // Toggle item selection
    const toggleItemSelection = (itemId) => {
        setSelectedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    // Select all items
    const selectAllItems = () => {
        if (selectedItems.size === inventoryCountItems.length) {
            setSelectedItems(new Set());
        } else {
            setSelectedItems(new Set(inventoryCountItems.map(item => item.id)));
        }
    };

    // Get difference color class
    const getDifferenceColorClass = (difference) => {
        if (!difference || difference === 0) return '';
        return difference > 0 ? 'text-success' : 'text-danger';
    };

    // Get difference badge
    const getDifferenceBadge = (difference) => {
        if (!difference || difference === 0) {
            return <Badge bg="secondary">Exact</Badge>;
        }
        return difference > 0 ? 
            <Badge bg="success">+{difference}</Badge> : 
            <Badge bg="danger">{difference}</Badge>;
    };

    return (
        <Card>
            <Card.Header>
                <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">Inventory Count Items</h5>
                    <div className="d-flex gap-2 align-items-center">
                        <span className="text-muted">
                            {inventoryCountItems.length} items
                        </span>
                        {canEdit && selectedItems.size > 0 && (
                            <div className="d-flex gap-2 align-items-center">
                                <Form.Control
                                    type="number"
                                    placeholder="Quantity"
                                    value={bulkQuantity}
                                    onChange={(e) => setBulkQuantity(e.target.value)}
                                    style={{ width: '100px' }}
                                    step="0.001"
                                    min="0"
                                />
                                <Button
                                    variant="primary"
                                    size="sm"
                                    onClick={handleBulkUpdate}
                                    disabled={saving || !bulkQuantity}
                                >
                                    Update Selected ({selectedItems.size})
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </Card.Header>
            <Card.Body>
                {/* Search and Filter Section */}
                <Row className="mb-3">
                    <Col md={4}>
                        <Form.Group>
                            <Form.Label>Search Products</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Search by product name or code..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                            />
                        </Form.Group>
                    </Col>
                    <Col md={3}>
                        <Form.Group>
                            <Form.Label>Discrepancy Filter</Form.Label>
                            <Form.Select
                                value={discrepancyFilter}
                                onChange={(e) => setDiscrepancyFilter(e.target.value)}
                            >
                                <option value="">All Items</option>
                                <option value="exact">Exact Matches</option>
                                <option value="discrepancy">With Discrepancies</option>
                                <option value="shortage">Shortages</option>
                                <option value="surplus">Surplus</option>
                            </Form.Select>
                        </Form.Group>
                    </Col>
                    <Col md={2}>
                        <Form.Group>
                            <Form.Label>Items per page</Form.Label>
                            <Form.Select
                                value={pageSize}
                                onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                            >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                            </Form.Select>
                        </Form.Group>
                    </Col>
                    <Col md={3} className="d-flex align-items-end">
                        <Button variant="outline-primary" onClick={handleSearch}>
                            Search
                        </Button>
                    </Col>
                </Row>

                {/* Error Alert */}
                {error && (
                    <Alert variant="danger" dismissible onClose={() => setError(null)}>
                        {error}
                    </Alert>
                )}

                {/* Items Table */}
                {loading ? (
                    <div className="text-center py-4">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </Spinner>
                    </div>
                ) : inventoryCountItems.length === 0 ? (
                    <div className="text-center py-4">
                        <p className="text-muted">No items found.</p>
                    </div>
                ) : (
                    <>
                        <Table responsive striped hover>
                            <thead>
                                <tr>
                                    {canEdit && (
                                        <th>
                                            <Form.Check
                                                type="checkbox"
                                                checked={selectedItems.size === inventoryCountItems.length && inventoryCountItems.length > 0}
                                                onChange={selectAllItems}
                                            />
                                        </th>
                                    )}
                                    <th>Product</th>
                                    <th>System Qty</th>
                                    <th>Recorded Qty</th>
                                    <th>Difference</th>
                                    <th>Unit</th>
                                    <th>Notes</th>
                                    {canEdit && <th>Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {inventoryCountItems.map((item) => (
                                    <tr key={item.id}>
                                        {canEdit && (
                                            <td>
                                                <Form.Check
                                                    type="checkbox"
                                                    checked={selectedItems.has(item.id)}
                                                    onChange={() => toggleItemSelection(item.id)}
                                                />
                                            </td>
                                        )}
                                        <td>
                                            <div>
                                                <strong>{item.product_name}</strong>
                                                <br />
                                                <small className="text-muted">{item.product_code}</small>
                                            </div>
                                        </td>
                                        <td>{item.system_quantity || 0}</td>
                                        <td>
                                            {canEdit ? (
                                                <Form.Control
                                                    type="number"
                                                    value={editingItems[item.id]?.recorded_quantity ?? item.recorded_quantity ?? ''}
                                                    onChange={(e) => handleEditChange(item.id, 'recorded_quantity', e.target.value)}
                                                    onBlur={() => saveItemChanges(item)}
                                                    step="0.001"
                                                    min="0"
                                                    style={{ width: '100px' }}
                                                />
                                            ) : (
                                                item.recorded_quantity || 0
                                            )}
                                        </td>
                                        <td className={getDifferenceColorClass(item.difference)}>
                                            {getDifferenceBadge(item.difference)}
                                        </td>
                                        <td>{item.unit_type}</td>
                                        <td>
                                            {canEdit ? (
                                                <Form.Control
                                                    as="textarea"
                                                    rows={1}
                                                    value={editingItems[item.id]?.notes ?? item.notes ?? ''}
                                                    onChange={(e) => handleEditChange(item.id, 'notes', e.target.value)}
                                                    onBlur={() => saveItemChanges(item)}
                                                    placeholder="Add notes..."
                                                    style={{ minWidth: '150px' }}
                                                />
                                            ) : (
                                                item.notes || '-'
                                            )}
                                        </td>
                                        {canEdit && (
                                            <td>
                                                <Button
                                                    variant="outline-success"
                                                    size="sm"
                                                    onClick={() => saveItemChanges(item)}
                                                    disabled={saving || !editingItems[item.id]}
                                                >
                                                    {saving ? 'Saving...' : 'Save'}
                                                </Button>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </Table>

                        {/* Pagination */}
                        {pagination.count > pagination.page_size && (
                            <div className="d-flex justify-content-center mt-3">
                                <Pagination>
                                    <Pagination.First 
                                        onClick={() => handlePageChange(1)}
                                        disabled={pagination.page === 1}
                                    />
                                    <Pagination.Prev 
                                        onClick={() => handlePageChange(pagination.page - 1)}
                                        disabled={pagination.page === 1}
                                    />
                                    
                                    {/* Page numbers */}
                                    {Array.from({ length: Math.ceil(pagination.count / pagination.page_size) }, (_, i) => i + 1)
                                        .filter(page => Math.abs(page - pagination.page) <= 2)
                                        .map(page => (
                                            <Pagination.Item
                                                key={page}
                                                active={page === pagination.page}
                                                onClick={() => handlePageChange(page)}
                                            >
                                                {page}
                                            </Pagination.Item>
                                        ))
                                    }
                                    
                                    <Pagination.Next 
                                        onClick={() => handlePageChange(pagination.page + 1)}
                                        disabled={pagination.page === Math.ceil(pagination.count / pagination.page_size)}
                                    />
                                    <Pagination.Last 
                                        onClick={() => handlePageChange(Math.ceil(pagination.count / pagination.page_size))}
                                        disabled={pagination.page === Math.ceil(pagination.count / pagination.page_size)}
                                    />
                                </Pagination>
                            </div>
                        )}
                    </>
                )}
            </Card.Body>
        </Card>
    );
};

export default InventoryCountItemsTable;
