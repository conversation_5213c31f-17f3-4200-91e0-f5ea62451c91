from factory import SubFactory
from factory.django import DjangoModelFactory

from purchases.models.supplier import Supplier
from utils.test.factories.accounts.account import AccountFactory


class SupplierFactory(DjangoModelFactory):
    """
    Factory for creating Supplier instances for testing.
    """

    class Meta:
        model = Supplier
        skip_postgeneration_save = True

    account = SubFactory(AccountFactory)
