from rest_framework import serializers

from expenses.models import Expense
from pos.models import POSSession

from .expense_category import ExpenseCategorySerializer


class ExpenseWriteSerializer(serializers.ModelSerializer):
    pos_session_id = serializers.PrimaryKeyRelatedField(
        queryset=POSSession.objects.all(),
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Expense
        fields = [
            "id",
            "expense_category",
            "amount",
            "description",
            "created",
            "modified",
            "pos_session_id",
        ]
        read_only_fields = ["id", "created", "modified"]

    def save(self, **kwargs):
        self.validated_data.pop("pos_session_id", None)
        return super().save(**kwargs)


class ExpenseReadSerializer(serializers.ModelSerializer):
    expense_category = ExpenseCategorySerializer(read_only=True)

    class Meta:
        model = Expense
        fields = [
            "id",
            "expense_category",
            "amount",
            "description",
            "created",
            "modified",
            "pos_session_transaction",
        ]
        read_only_fields = fields
