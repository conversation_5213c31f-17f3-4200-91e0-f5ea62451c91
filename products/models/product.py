from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from products.models.category import Category


def product_image_upload_path(instance, filename):
    """
    Generate upload path for product images
    Format: products/images/{product_id}/{filename}
    """
    return f"products/images/{instance.id}-{filename}"


class Product(TimeStampedModel):
    """
    Product model representing items available in the inventory.
    Each product belongs to a category and has pricing information.
    """

    # Unit type choices
    UNIT_TYPES = [
        ("piece", _("Piece")),
        ("kg", _("Kilogram")),
        ("g", _("Gram")),
        ("l", _("Liter")),
        ("ml", _("Milliliter")),
    ]

    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="products",
        verbose_name=_("category"),
        help_text=_("Select a category for this product"),
    )

    name = models.CharField(
        _("name"), max_length=200, help_text=_("Enter the product name")
    )

    description = models.TextField(
        _("description"),
        blank=True,
        null=True,
        help_text=_("Enter a detailed description of the product (optional)"),
    )

    cost = models.DecimalField(
        _("cost price"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Cost price of the product"),
        validators=[MinValueValidator(0.0)],
    )

    price = models.DecimalField(
        _("selling price"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Selling price of the product"),
        validators=[MinValueValidator(0.0)],
    )

    barcode = models.CharField(
        _("barcode"),
        max_length=100,
        unique=True,
        blank=True,
        null=True,
        help_text=_("Barcode number (optional)"),
    )

    image = models.ImageField(
        _("image"),
        upload_to=product_image_upload_path,
        blank=True,
        null=True,
        help_text=_("Upload a product image (optional)"),
    )

    unit_type = models.CharField(
        _("unit type"),
        max_length=10,
        choices=UNIT_TYPES,
        default="piece",
        help_text=_("Select the unit type for this product"),
    )

    class Meta:
        verbose_name = _("product")
        verbose_name_plural = _("products")
        ordering = ["name"]
        indexes = [
            models.Index(fields=["name"]),
            models.Index(fields=["barcode"]),
            models.Index(fields=["category"]),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_unit_type_display()})"

    @property
    def profit_margin(self):
        """Calculate the profit margin percentage"""
        if self.cost == 0:
            return 0
        return ((self.price - self.cost) / self.cost) * 100

    def save(self, *args, **kwargs):
        # Ensure price is not less than cost
        if self.price < self.cost:
            self.price = self.cost
        super().save(*args, **kwargs)
