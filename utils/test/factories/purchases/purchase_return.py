import random
from decimal import Decimal

import factory
from factory import <PERSON>zy<PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory, post_generation
from factory.django import DjangoModelFactory

from purchases_return.models.purchase_return import PurchaseReturn
from utils.test.factories.purchases.supplier import SupplierFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class PurchaseReturnFactory(DjangoModelFactory):
    """
    Factory for creating PurchaseReturn instances for testing.
    """

    class Meta:
        model = PurchaseReturn
        skip_postgeneration_save = True

    warehouse = SubFactory(WarehouseFactory)
    supplier = SubFactory(SupplierFactory)
    
    # Generate random total amount (will be recalculated from items)
    total_amount = LazyAttribute(
        lambda _: Decimal(random.uniform(50, 500)).quantize(Decimal('0.01'))
    )
    
    payment_method = factory.Iterator([
        PurchaseReturn.PaymentMethod.CASH,
        PurchaseReturn.PaymentMethod.CREDIT,
        PurchaseReturn.PaymentMethod.CARD,
        PurchaseReturn.PaymentMethod.BANK_TRANSFER,
    ])
    
    notes = factory.Faker('paragraph', nb_sentences=1)

    @post_generation
    def items(self, create, extracted, **kwargs):
        """
        Create return items if specified.
        Usage:
            return = PurchaseReturnFactory.create(items=3)  # Creates a return with 3 random items
            return = PurchaseReturnFactory.create(items=[item1, item2])  # Creates a return with specific items
        """
        from utils.test.factories.purchases.purchase_return_item import PurchaseReturnItemFactory
        
        if not create:
            return
            
        if extracted:
            if isinstance(extracted, int):
                # Create the specified number of items
                for _ in range(extracted):
                    PurchaseReturnItemFactory.create(purchase_return=self)
            else:
                # Add the specified items
                for item in extracted:
                    if hasattr(item, 'purchase_return'):
                        item.purchase_return = self
                        item.save()
                    else:
                        self.items.add(item)

    @classmethod
    def create_cash_return(cls, **kwargs):
        """
        Create a cash return.
        
        Usage:
            return = PurchaseReturnFactory.create_cash_return()
        """
        kwargs['payment_method'] = PurchaseReturn.PaymentMethod.CASH
        return cls.create(**kwargs)

    @classmethod
    def create_credit_return(cls, **kwargs):
        """
        Create a credit return.
        
        Usage:
            return = PurchaseReturnFactory.create_credit_return()
        """
        kwargs['payment_method'] = PurchaseReturn.PaymentMethod.CREDIT
        return cls.create(**kwargs)

    @classmethod
    def create_with_pos_session(cls, pos_session, **kwargs):
        """
        Create a return with a specific POS session.
        
        Usage:
            return = PurchaseReturnFactory.create_with_pos_session(pos_session)
        """
        kwargs['pos_session'] = pos_session
        kwargs['warehouse'] = pos_session.pos.warehouse
        return cls.create(**kwargs)

    @classmethod
    def create_for_supplier(cls, supplier, **kwargs):
        """
        Create a return for a specific supplier.
        
        Usage:
            return = PurchaseReturnFactory.create_for_supplier(supplier)
        """
        kwargs['supplier'] = supplier
        return cls.create(**kwargs)
