

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Inventory Counts Styles */
.listing-table-header th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  cursor: pointer;
  user-select: none;
}

.listing-table-header th:hover {
  background-color: #e9ecef;
}

/* Status badges */
.badge {
  font-size: 0.75em;
}

/* Difference highlighting */
.text-success {
  color: #198754 !important;
}

.text-danger {
  color: #dc3545 !important;
}

/* Inline editing styles */
.table td .form-control {
  border: 1px solid transparent;
  background: transparent;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.table td .form-control:focus {
  border-color: #86b7fe;
  background: #fff;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Bulk operations */
.bulk-operations {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Loading states */
.table-loading {
  position: relative;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
