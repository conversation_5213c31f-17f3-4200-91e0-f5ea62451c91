# Generated by Django 5.2.1 on 2025-07-07 07:57

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("pos", "0001_initial"),
        ("products", "0001_initial"),
        ("warehouses", "0004_inventorycount_total_price_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(
                        help_text="Unique invoice number", max_length=50, unique=True
                    ),
                ),
                (
                    "net_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Final amount after discounts and taxes",
                        max_digits=12,
                    ),
                ),
                (
                    "profit",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Profit amount for this invoice",
                        max_digits=12,
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[("cash", "Cash")],
                        default="cash",
                        help_text="Method of payment used",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes or comments about the invoice",
                        null=True,
                    ),
                ),
                (
                    "pos_session",
                    models.ForeignKey(
                        blank=True,
                        help_text="POS session this invoice belongs to (if any)",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invoices",
                        to="pos.possession",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse where the invoice was created",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="invoices",
                        to="warehouses.warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice",
                "verbose_name_plural": "Invoices",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        default=1,
                        help_text="Quantity of the product",
                        max_digits=10,
                    ),
                ),
                (
                    "unit_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price per unit of the product",
                        max_digits=12,
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total price (quantity × unit_price)",
                        max_digits=12,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this invoice item",
                        null=True,
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        help_text="The invoice this item belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="invoices.invoice",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        help_text="The product being invoiced",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="invoice_items",
                        to="products.product",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Item",
                "verbose_name_plural": "Invoice Items",
                "ordering": ["-created"],
            },
        ),
    ]
