from factory import Faker
from factory.django import DjangoModelFactory

from expenses.models.expense_category import ExpenseCategory


class ExpenseCategoryFactory(DjangoModelFactory):
    """
    Factory for creating ExpenseCategory instances for testing.
    """

    class Meta:
        model = ExpenseCategory
        skip_postgeneration_save = True
        django_get_or_create = ("name",)

    name = Faker("word")
    description = Faker("paragraph", nb_sentences=1)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unique names."""
        # Generate a unique name by appending a random string
        if "name" not in kwargs:
            base_name = Faker("word").generate({})
            counter = 1
            name = base_name
            while ExpenseCategory.objects.filter(name=name).exists():
                name = f"{base_name} {counter}"
                counter += 1
            kwargs["name"] = name
        return super()._create(model_class, *args, **kwargs)
