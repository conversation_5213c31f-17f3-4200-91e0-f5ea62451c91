from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path, re_path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions

# Schema View for Swagger/ReDoc
schema_view = get_schema_view(
    openapi.Info(
        title="El Quds ERP API",
        default_version="v1",
        description="""
        API documentation for El Quds ERP system.
        This API provides endpoints for user authentication and management.
        """,
        terms_of_service="https://www.elquds-erp.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # Admin site
    path("admin/", admin.site.urls),
    # API v1
    path("api/v1/auth/", include(("users.api.urls", "users_api"), namespace="auth")),
    path("api/v1/", include(("products.urls", "products"), namespace="products")),
    path("api/v1/", include(("warehouses.urls", "warehouses"), namespace="warehouses")),
    path("api/v1/", include(("pos.urls", "pos"), namespace="pos")),
    path("api/v1/", include(("expenses.urls", "expenses"), namespace="expenses")),
    path("api/v1/", include(("employees.urls", "employees"), namespace="employees")),
    path("api/v1/", include(("accounts.urls", "accounts"), namespace="accounts")),
    path("api/v1/", include(("invoices.urls", "invoices"), namespace="invoices")),
    path("api/v1/", include(("purchases.urls", "purchases"), namespace="purchases")),
    path("api/v1/", include(("purchases_return.urls", "purchases_return"), namespace="purchases_return")),
    # API Documentation
    re_path(
        r"^swagger(?P<format>\.json|\.yaml)$",
        schema_view.without_ui(cache_timeout=0),
        name="schema-json",
    ),
    path(
        "swagger/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),
]

# Serve media files in development
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
