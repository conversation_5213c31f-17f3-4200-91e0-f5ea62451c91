from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from purchases.models.supplier import Supplier
from warehouses.models.warehouse import Warehouse


class Purchase(TimeStampedModel):
    """
    Purchase model representing purchases made from suppliers.
    Each purchase is associated with a warehouse and supplier, and includes
    financial details such as total amount, discount, and net amount.
    """

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name="purchases",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse where the purchased items will be stored"),
    )

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name="purchases",
        verbose_name=_("supplier"),
        help_text=_("The supplier from whom the purchase is made"),
    )

    total_amount = models.DecimalField(
        _("total amount"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Total amount of the purchase before discount"),
        validators=[MinValueValidator(0.01)],
    )

    discount = models.DecimalField(
        _("discount"),
        max_digits=12,
        decimal_places=2,
        default=0.00,
        help_text=_("Discount amount applied to the purchase"),
        validators=[MinValueValidator(0.00)],
    )

    bonus = models.DecimalField(
        _("bonus"),
        max_digits=12,
        decimal_places=2,
        default=0.00,
        help_text=_("Bonus amount applied to the purchase"),
        validators=[MinValueValidator(0.00)],
    )

    net_amount = models.DecimalField(
        _("net amount"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Net amount after discount (total_amount - discount)"),
        validators=[MinValueValidator(0.01)],
    )
    paid_amount = models.DecimalField(
        _("paid amount"),
        max_digits=12,
        decimal_places=2,
        default=0.00,
        help_text=_("Amount paid for this purchase"),
        validators=[MinValueValidator(0.00)],
    )
    reminder_amount = models.DecimalField(
        _("reminder amount"),
        max_digits=12,
        decimal_places=2,
        default=0.00,
        help_text=_("Amount remaining to be paid for this purchase"),
        validators=[MinValueValidator(0.00)],
    )

    PAYMENT_METHODS = [("cash", _("Cash"))]

    payment_method = models.CharField(
        _("payment method"),
        max_length=20,
        choices=PAYMENT_METHODS,
        default="cash",
        help_text=_("Method of payment for this purchase"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this purchase"),
    )

    class Meta:
        verbose_name = _("purchase")
        verbose_name_plural = _("purchases")
        ordering = ["-created"]

    def __str__(self):
        return f"Purchase {self.id} - {self.supplier.name} - {self.net_amount}"

    def save(self, *args, **kwargs):
        # Calculate net_amount before saving if not explicitly set
        if self.pk is None and self.net_amount is None:
            self.net_amount = self.total_amount - self.discount
        super().save(*args, **kwargs)
