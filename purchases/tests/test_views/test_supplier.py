from django.urls import reverse
from rest_framework import status

from accounts.models import Account
from purchases.models import Supplier
from utils.test.base_test import BaseTestCase
from utils.test.factories.purchases.supplier import SupplierFactory


class SupplierViewSetTestCase(BaseTestCase):
    """
    Test cases for SupplierViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("purchases:supplier-list")
        self.detail_url_name = "purchases:supplier-detail"

        # Create test suppliers
        self.supplier1 = SupplierFactory.create(
            name="Test Supplier 1", phone_number="***********"
        )

        self.supplier2 = SupplierFactory.create(
            name="Test Supplier 2", phone_number="***********"
        )

    def get_detail_url(self, supplier_id):
        """Helper method to get detail URL for a supplier."""
        return reverse(self.detail_url_name, kwargs={"pk": supplier_id})

    # Authentication and Permission Tests
    def test_list_suppliers_unauthenticated(self):
        """Test that unauthenticated users cannot list suppliers."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_suppliers_as_admin(self):
        """Admin should see all suppliers."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test suppliers
        self.assertEqual(len(response.data["results"]), 2)

    def test_list_suppliers_as_manager(self):
        """Manager should see all suppliers."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test suppliers
        self.assertEqual(len(response.data["results"]), 2)

    def test_list_suppliers_as_cashier(self):
        """Cashier should not be able to list suppliers."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_supplier_as_admin(self):
        """Admin should be able to retrieve any supplier."""
        url = self.get_detail_url(self.supplier1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.supplier1.id)
        self.assertEqual(response.data["name"], "Test Supplier 1")
        self.assertEqual(response.data["phone_number"], "***********")
        self.assertIn("account", response.data)
        self.assertIn("created", response.data)
        self.assertIn("modified", response.data)

    def test_retrieve_supplier_as_manager(self):
        """Manager should be able to retrieve suppliers."""
        url = self.get_detail_url(self.supplier1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_supplier_as_cashier(self):
        """Cashier should not be able to retrieve suppliers."""
        url = self.get_detail_url(self.supplier1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_supplier(self):
        """Test retrieving a non-existent supplier."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_supplier_as_admin(self):
        """Admin should be able to create suppliers."""
        data = {"name": "New Test Supplier", "phone_number": "***********"}

        initial_supplier_count = Supplier.objects.count()
        initial_account_count = Account.objects.count()

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that supplier was created
        self.assertEqual(Supplier.objects.count(), initial_supplier_count + 1)

        # Check that account was automatically created
        self.assertEqual(Account.objects.count(), initial_account_count + 1)

        # Check response data
        supplier = Supplier.objects.get(id=response.data["id"])
        self.assertEqual(supplier.name, "New Test Supplier")
        self.assertEqual(supplier.phone_number, "***********")

        # Check that account was created with correct properties
        account = supplier.account
        self.assertEqual(account.name, "New Test Supplier")
        self.assertEqual(account.account_type, "Supplier")

    def test_create_supplier_as_manager(self):
        """Manager should be able to create suppliers."""
        data = {"name": "Manager Supplier", "phone_number": "***********"}

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_supplier_as_cashier(self):
        """Cashier should not be able to create suppliers."""
        data = {"name": "Cashier Supplier", "phone_number": "***********"}

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_supplier_unauthenticated(self):
        """Unauthenticated users should not be able to create suppliers."""
        data = {"name": "Unauthorized Supplier", "phone_number": "***********"}

        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_supplier_missing_required_fields(self):
        """Test creating supplier with missing required fields."""
        data = {
            "phone_number": "01234567896"
            # Missing name
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Update Tests
    def test_update_supplier_as_admin(self):
        """Admin should be able to update suppliers."""
        url = self.get_detail_url(self.supplier1.id)
        data = {"name": "Updated Supplier Name", "phone_number": "***********"}

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that supplier was updated
        self.supplier1.refresh_from_db()
        self.assertEqual(self.supplier1.name, "Updated Supplier Name")
        self.assertEqual(self.supplier1.phone_number, "***********")

        # Check that account name was also updated
        self.supplier1.account.refresh_from_db()
        self.assertEqual(self.supplier1.account.name, "Updated Supplier Name")

    def test_update_supplier_as_manager(self):
        """Manager should not be able to update suppliers (admin only)."""
        url = self.get_detail_url(self.supplier1.id)
        data = {"name": "Manager Update"}

        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_supplier_as_cashier(self):
        """Cashier should not be able to update suppliers."""
        url = self.get_detail_url(self.supplier1.id)
        data = {"name": "Cashier Update"}

        response = self.cashier_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partial_update_supplier_as_admin(self):
        """Admin should be able to partially update suppliers."""
        url = self.get_detail_url(self.supplier1.id)
        data = {"phone_number": "***********"}

        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only phone number was updated
        self.supplier1.refresh_from_db()
        self.assertEqual(self.supplier1.phone_number, "***********")
        self.assertEqual(
            self.supplier1.name, "Test Supplier 1"
        )  # Should remain unchanged

    # Delete Tests
    def test_delete_supplier_as_admin(self):
        """Admin should be able to delete suppliers."""
        url = self.get_detail_url(self.supplier2.id)
        initial_count = Supplier.objects.count()

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that supplier was deleted
        self.assertEqual(Supplier.objects.count(), initial_count - 1)
        self.assertFalse(Supplier.objects.filter(id=self.supplier2.id).exists())

    def test_delete_supplier_as_manager(self):
        """Manager should not be able to delete suppliers (admin only)."""
        url = self.get_detail_url(self.supplier1.id)

        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_supplier_as_cashier(self):
        """Cashier should not be able to delete suppliers."""
        url = self.get_detail_url(self.supplier1.id)

        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_nonexistent_supplier(self):
        """Test deleting a non-existent supplier."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Search Tests
    def test_search_suppliers_by_name(self):
        """Test searching suppliers by name."""
        url = f"{self.list_url}?search=Test Supplier 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return suppliers with matching name
        self.assertGreater(len(response.data["results"]), 0)
        self.assertEqual(response.data["results"][0]["name"], "Test Supplier 1")

    def test_search_suppliers_by_phone_number(self):
        """Test searching suppliers by phone number."""
        url = f"{self.list_url}?search=***********"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return suppliers with matching phone number
        self.assertGreater(len(response.data["results"]), 0)
        self.assertEqual(response.data["results"][0]["phone_number"], "***********")

    def test_search_suppliers_case_insensitive(self):
        """Test that search is case insensitive."""
        url = f"{self.list_url}?search=test supplier"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return suppliers even with lowercase search
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_suppliers_no_results(self):
        """Test searching with term that has no results."""
        url = f"{self.list_url}?search=NonexistentSupplier"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return no results
        self.assertEqual(len(response.data["results"]), 0)

    # Ordering Tests
    def test_default_ordering_by_name(self):
        """Test that suppliers are ordered by name by default."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that suppliers are ordered by name
        names = [supplier["name"] for supplier in response.data["results"]]
        self.assertEqual(names, sorted(names))

    def test_ordering_by_name_descending(self):
        """Test ordering suppliers by name in descending order."""
        url = f"{self.list_url}?ordering=-name"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that suppliers are ordered by name (descending)
        names = [supplier["name"] for supplier in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))

    def test_ordering_by_created(self):
        """Test ordering suppliers by creation date."""
        url = f"{self.list_url}?ordering=created"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that suppliers are ordered by created date
        created_dates = [supplier["created"] for supplier in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates))

    def test_ordering_by_created_descending(self):
        """Test ordering suppliers by creation date in descending order."""
        url = f"{self.list_url}?ordering=-created"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that suppliers are ordered by created date (descending)
        created_dates = [supplier["created"] for supplier in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    # Combined Tests
    def test_search_and_ordering_combined(self):
        """Test combining searching and ordering."""
        # Create additional suppliers for testing
        SupplierFactory.create(name="Test Supplier 3", phone_number="***********")
        SupplierFactory.create(name="Test Supplier 4", phone_number="***********")

        url = f"{self.list_url}?search=Test Supplier&ordering=-name"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return filtered results in descending name order
        names = [supplier["name"] for supplier in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))

    # Account Tests
    def test_account_created_with_supplier(self):
        """Test that an account is automatically created when creating a supplier."""
        data = {"name": "Account Test Supplier", "phone_number": "***********"}

        initial_account_count = Account.objects.count()

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that a new account was created
        self.assertEqual(Account.objects.count(), initial_account_count + 1)

        # Check that the account has the correct properties
        supplier = Supplier.objects.get(id=response.data["id"])
        account = supplier.account

        self.assertEqual(account.name, "Account Test Supplier")
        self.assertEqual(account.account_type, "Supplier")

    def test_account_updated_with_supplier(self):
        """Test that the associated account is updated when updating a supplier."""
        url = self.get_detail_url(self.supplier1.id)
        data = {
            "name": "Updated Account Name",
            "phone_number": self.supplier1.phone_number,
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that the account name was updated
        self.supplier1.refresh_from_db()
        self.supplier1.account.refresh_from_db()

        self.assertEqual(self.supplier1.name, "Updated Account Name")
        self.assertEqual(self.supplier1.account.name, "Updated Account Name")

    def test_supplier_with_too_long_name(self):
        """Test creating a supplier with a name that exceeds max length."""
        too_long_name = "A" * 201  # 201 characters (exceeds max length of 200)
        data = {"name": too_long_name, "phone_number": "***********"}

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_supplier_with_empty_name(self):
        """Test creating a supplier with an empty name."""
        data = {"name": "", "phone_number": "***********"}

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_supplier_with_empty_phone_number(self):
        """Test creating a supplier with an empty phone number."""
        data = {"name": "Empty Phone Supplier", "phone_number": ""}

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Model String Representation Test
    def test_supplier_string_representation(self):
        """Test the string representation of Supplier model."""
        self.assertEqual(str(self.supplier1), "Test Supplier 1")
