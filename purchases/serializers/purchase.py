from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from rest_framework import serializers

from pos.models import POSSession, POSSessionTransaction
from pos.models.pos_session_transaction import (
    TransactionType as POSTransactionType,
)
from purchases.models.purchase import Purchase
from purchases.models.supplier import Supplier
from purchases.serializers.purchase_item import PurchaseItemSerializer


class PurchaseSerializer(serializers.ModelSerializer):
    """
    Serializer for the Purchase model.
    Handles creation and updating of Purchase instances along with their associated items.
    """

    pos_session_id = serializers.PrimaryKeyRelatedField(
        queryset=POSSession.objects.filter(status=POSSession.Status.OPEN).all(),
        source="pos_session",
        write_only=True,
        required=True,
    )
    warehouse_name = serializers.StringRelatedField(
        source="warehouse.name", read_only=True
    )

    supplier_id = serializers.PrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), source="supplier", write_only=True
    )
    supplier_name = serializers.StringRelatedField(
        source="supplier.name", read_only=True
    )

    items = PurchaseItemSerializer(many=True, required=True)

    class Meta:
        model = Purchase
        fields = [
            "id",
            "warehouse_name",
            "pos_session_id",
            "supplier_id",
            "supplier_name",
            "total_amount",
            "discount",
            "bonus",
            "net_amount",
            "paid_amount",
            "reminder_amount",
            "notes",
            "items",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
            "net_amount",
            "bonus",
            "reminder_amount",
            "total_amount",
        ]

    def validate(self, data):
        """
        Calculate net_amount based on total_amount, discount, and bonus.
        """

        # Validate POS session ownership for non-admin users
        pos_session = data.get("pos_session")
        if not self.context["request"].user.is_admin:
            if not (
                pos_session.pos.employee_set.filter(
                    user=self.context["request"].user
                ).exists()
            ):
                raise serializers.ValidationError(
                    "Cannot create purchase for POS session not assigned to you"
                )
        data["warehouse"] = pos_session.pos.warehouse

        # Calculate total_amount and bonus from items
        items_serializer = PurchaseItemSerializer(
            data=self.initial_data.get("items", []), many=True
        )
        items_serializer.is_valid(raise_exception=True)
        items_data = items_serializer.validated_data
        total_amount = Decimal(0)
        bonus = Decimal(0)
        for item_data in items_data:
            total_amount += item_data["total_cost"]
            if item_data["total_cost"] == 0:
                bonus += item_data["product"].price * item_data["quantity"]
        discount = data.get("discount", 0)
        data["total_amount"] = total_amount
        data["net_amount"] = total_amount - discount
        # Ensure net_amount is not negative
        if data["net_amount"] <= 0:
            raise serializers.ValidationError("Net amount must be greater than zero")

        if data["paid_amount"] > data["net_amount"]:
            raise serializers.ValidationError(
                "Paid amount cannot be greater than net amount"
            )

        data["reminder_amount"] = data["net_amount"] - data.get("paid_amount", 0)
        data["bonus"] = bonus
        return data

    def create(self, validated_data):
        """
        Create a new Purchase with its associated items.
        """
        items_data = validated_data.pop("items", [])
        pos_session = validated_data.pop("pos_session")
        purchase = Purchase.objects.create(**validated_data)
        POSSessionTransaction.objects.create(
            session=pos_session,
            transaction_type=POSTransactionType.PURCHASE,
            amount=purchase.paid_amount,
            content_type=ContentType.objects.get_for_model(Purchase),
            object_id=purchase.id,
            description=f"Purchase from {purchase.supplier.name}",
        )

        for item_data in items_data:
            PurchaseItemSerializer().create({**item_data, "purchase": purchase})

        return purchase

    def update(self, instance, validated_data):
        """
        Update an existing Purchase and its associated items.
        """
        validated_data.pop("items", None)

        # Update purchase fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
