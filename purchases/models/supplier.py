from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from accounts.models.account import Account


class Supplier(TimeStampedModel):
    """
    Supplier model representing vendors who provide products to the business.
    Each supplier has a name, phone number, and is linked to an account for financial tracking.
    """

    name = models.Char<PERSON>ield(
        _("name"), max_length=200, help_text=_("Enter the supplier name")
    )

    phone_number = models.Char<PERSON><PERSON>(
        _("phone number"),
        max_length=20,
        help_text=_("Enter the supplier's phone number"),
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name="suppliers",
        verbose_name=_("account"),
        help_text=_("The financial account associated with this supplier"),
    )

    class Meta:
        verbose_name = _("supplier")
        verbose_name_plural = _("suppliers")
        ordering = ["name"]

    def __str__(self):
        return f"{self.name}"
