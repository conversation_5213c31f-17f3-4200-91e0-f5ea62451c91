from rest_framework.routers import DefaultRouter

from employees.views.attendance import AttendanceViewSet
from employees.views.employee import EmployeeViewSet
from employees.views.salary import SalaryViewSet

router = DefaultRouter()
router.register(r"employees", EmployeeViewSet, basename="employee")
router.register(r"attendance", AttendanceViewSet, basename="attendance")
router.register(r"salaries", SalaryViewSet, basename="salary")

urlpatterns = router.urls
