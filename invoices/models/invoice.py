from django.db import models
from django_extensions.db.models import TimeStampedModel

from pos.models import POSSession
from warehouses.models.warehouse import Warehouse


class Invoice(TimeStampedModel):
    """
    Invoice model representing a sales or purchase invoice.
    """

    PAYMENT_METHODS = [
        ("cash", "Cash"),
    ]

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name="invoices",
        help_text="The warehouse where the invoice was created",
    )
    net_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Final amount after discounts and taxes",
        null=True,
        blank=True,
    )
    profit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Profit amount for this invoice",
        null=True,
        blank=True,
    )
    pos_session = models.ForeignKey(
        POSSession,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoices",
        help_text="POS session this invoice belongs to (if any)",
    )
    payment_method = models.Char<PERSON>ield(
        max_length=20,
        choices=PAYMENT_METHODS,
        default="cash",
        help_text="Method of payment used",
    )
    notes = models.TextField(
        blank=True,
        null=True,
        help_text="Additional notes or comments about the invoice",
    )

    class Meta:
        ordering = ["-created"]
        verbose_name = "Invoice"
        verbose_name_plural = "Invoices"

    def __str__(self):
        return f"{self.id} - {self.net_amount}"
