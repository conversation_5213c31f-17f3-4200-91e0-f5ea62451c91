services:
  backend:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    env_file:
      - .env
    entrypoint: ["/entrypoint.sh"]
    command: ["python", "manage.py", "runserver", "0.0.0.0:8000"]
    depends_on:
      - db

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend

  db:
    image: postgres:latest
    environment:
      POSTGRES_USER: erp
      POSTGRES_PASSWORD: passw0rd
      POSTGRES_DB: erp
    ports:
      - "5433:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    user: postgres

volumes:
  pgdata:
