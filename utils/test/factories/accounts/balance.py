import random
from decimal import Decimal

import factory
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from accounts.models import Balance
from utils.test.factories.accounts.account import AccountFactory
from utils.test.factories.pos.pos import POSFactory


class BalanceFactory(DjangoModelFactory):
    """
    Factory for creating Balance instances for testing.
    """

    class Meta:
        model = Balance
        skip_postgeneration_save = True

    account = SubFactory(AccountFactory)
    pos = SubFactory(POSFactory)
    
    # Generate random amount between -1000 and 1000
    amount = LazyAttribute(
        lambda _: Decimal(random.uniform(-1000, 1000)).quantize(Decimal("0.01"))
    )
    
    notes = factory.Faker("paragraph", nb_sentences=1)

    @classmethod
    def create_with_positive_amount(cls, **kwargs):
        """Create a balance with positive amount."""
        amount = Decimal(random.uniform(100, 1000)).quantize(Decimal("0.01"))
        return cls.create(amount=amount, **kwargs)

    @classmethod
    def create_with_negative_amount(cls, **kwargs):
        """Create a balance with negative amount."""
        amount = Decimal(random.uniform(-1000, -100)).quantize(Decimal("0.01"))
        return cls.create(amount=amount, **kwargs)

    @classmethod
    def create_with_zero_amount(cls, **kwargs):
        """Create a balance with zero amount."""
        return cls.create(amount=Decimal("0.00"), **kwargs)

    @classmethod
    def create_for_account_and_pos(cls, account, pos, **kwargs):
        """
        Create a balance for specific account and POS.
        
        Usage:
            balance = BalanceFactory.create_for_account_and_pos(account, pos, amount=Decimal("500.00"))
        """
        return cls.create(account=account, pos=pos, **kwargs)
