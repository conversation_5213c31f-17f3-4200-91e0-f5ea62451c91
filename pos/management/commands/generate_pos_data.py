from django.core.management.base import BaseCommand
from django.utils import timezone
from faker import Faker
import random
from decimal import Decimal
from datetime import timedelta

# Import models
from pos.models.pos import POS
from pos.models import POSSession, POSSessionTransaction, TransactionType
from users.models import User
from warehouses.models import Warehouse


class Command(BaseCommand):
    help = "Generate fake data for POS app"

    def add_arguments(self, parser):
        parser.add_argument(
            "--pos-count",
            type=int,
            default=30,
            help="Number of POS terminals to create (default: 3)",
        )
        parser.add_argument(
            "--sessions-per-pos",
            type=int,
            default=50,
            help="Number of sessions to create per POS (default: 5)",
        )
        parser.add_argument(
            "--transactions-per-session",
            type=int,
            default=1000,
            help="Number of transactions to create per session (default: 10)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing data before generating new data",
        )
        parser.add_argument(
            "--user_email",
            type=str,
            help="Email of the user to be used for all transactions (default: <EMAIL>)",
        )

    def handle(self, *args, **options):
        fake = Faker()
        pos_count = options["pos_count"]
        sessions_per_pos = options["sessions_per_pos"]
        transactions_per_session = options["transactions_per_session"]
        clear = options["clear"]
        user_email = options["user_email"]

        self.stdout.write(self.style.SUCCESS("Starting to generate POS data..."))

        # Clear existing data if requested
        if clear:
            self.stdout.write("Clearing existing data...")
            POSSessionTransaction.objects.all().delete()
            POSSession.objects.all().delete()
            POS.objects.all().delete()

        # Get or create a test user
        try:
            user = User.objects.get(email=user_email)
        except Exception:
            user = User.objects.create_user(
                email=user_email,
                phone_number="1234567890",
                password="password123",
                first_name="Test",
                last_name="Cashier",
            )

        # Create Warehouses and POS terminals
        for i in range(pos_count):
            # Create a warehouse for each POS
            warehouse, _ = Warehouse.objects.get_or_create(
                name=f"Warehouse {i + 1}",
                defaults={
                    "location": fake.address(),
                    "description": f"Warehouse {i + 1} for POS",
                },
            )

            # Create a POS terminal
            pos, created = POS.objects.get_or_create(
                name=f"POS Terminal {i + 1}",
                defaults={
                    "warehouse": warehouse,
                    "description": f"POS Terminal {i + 1} at {warehouse.name}",
                },
            )

            if created:
                self.stdout.write(f"Created POS: {pos.name} at {warehouse.name}")

            # Create sessions for this POS
            for j in range(sessions_per_pos):
                # Random date within the last 30 days
                opened_at = timezone.now() - timedelta(days=random.randint(0, 30))

                # Randomly decide if session is open, closed, or suspended
                status = random.choice(
                    [
                        POSSession.Status.OPEN,
                        POSSession.Status.CLOSED,
                        POSSession.Status.SUSPENDED,
                    ]
                )

                # Create the session
                session = POSSession.objects.create(
                    pos=pos,
                    user=user,
                    opening_balance=Decimal("100.00"),
                    status=status,
                    opened_at=opened_at,
                    notes=f"Session {j + 1} for {pos.name}",
                )

                # If session is closed, set closed_at and closing balance
                if status == POSSession.Status.CLOSED:
                    session.closed_at = opened_at + timedelta(
                        hours=random.randint(1, 8)
                    )
                    session.closing_balance = Decimal(
                        random.uniform(100, 1000)
                    ).quantize(Decimal("0.01"))
                    session.save()

                # Create transactions for this session
                total_sales = Decimal("0")
                transactions_to_create = []

                for k in range(transactions_per_session):
                    # Random transaction type
                    trans_type = random.choice(
                        [
                            TransactionType.SALE,
                            TransactionType.REFUND,
                            TransactionType.CASH_IN,
                            TransactionType.CASH_OUT,
                            TransactionType.EXPENSE,
                        ]
                    )

                    # Random amount based on transaction type
                    if trans_type in [TransactionType.SALE, TransactionType.REFUND]:
                        amount = Decimal(random.uniform(10, 500)).quantize(
                            Decimal("0.01")
                        )
                    elif trans_type == TransactionType.CASH_IN:
                        amount = Decimal(random.uniform(50, 1000)).quantize(
                            Decimal("0.01")
                        )
                    else:  # CASH_OUT or EXPENSE
                        amount = Decimal(random.uniform(5, 200)).quantize(
                            Decimal("0.01")
                        )

                    # Create transaction object and add to list
                    transaction = POSSessionTransaction(
                        session=session,
                        transaction_type=trans_type,
                        amount=amount,
                        description=f"{trans_type.capitalize()} transaction #{k + 1}",
                    )
                    transactions_to_create.append(transaction)

                    # Update session totals
                    if trans_type in [TransactionType.SALE, TransactionType.CASH_IN]:
                        total_sales += amount
                    elif trans_type in [
                        TransactionType.REFUND,
                        TransactionType.CASH_OUT,
                        TransactionType.EXPENSE,
                    ]:
                        total_sales -= amount

                # Bulk create all transactions for this session
                POSSessionTransaction.objects.bulk_create(transactions_to_create)

                # Update session total sales
                session.total_sales = max(total_sales, Decimal("0"))
                session.save()

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully generated:\n"
                f"- {POS.objects.count()} POS terminals\n"
                f"- {POSSession.objects.count()} sessions\n"
                f"- {POSSessionTransaction.objects.count()} transactions"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                "Test user created/updated:\n"
                "Username: cashier\n"
                "Password: password123"
            )
        )
