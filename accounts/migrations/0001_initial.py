# Generated by Django 5.2.1 on 2025-07-02 10:35

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Account",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "object_id",
                    models.PositiveIntegerField(help_text="ID of the related entity"),
                ),
                (
                    "balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Current account balance",
                        max_digits=15,
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        help_text="The model type of the related entity",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="account_owner",
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Account",
                "verbose_name_plural": "Accounts",
            },
        ),
        migrations.CreateModel(
            name="AccountTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("debit", "Debit"), ("credit", "Credit")],
                        help_text="Type of transaction (debit/credit)",
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Transaction amount (always positive)",
                        max_digits=15,
                    ),
                ),
                (
                    "related_object_id",
                    models.PositiveIntegerField(
                        blank=True, help_text="ID of the related object", null=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Additional details about the transaction",
                        null=True,
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        help_text="The account this transaction belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="accounts.account",
                    ),
                ),
                (
                    "related_content_type",
                    models.ForeignKey(
                        blank=True,
                        help_text="The model type this transaction is related to",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Account Transaction",
                "verbose_name_plural": "Account Transactions",
                "ordering": ["-created"],
            },
        ),
        migrations.AddIndex(
            model_name="account",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="accounts_ac_content_d1dadb_idx",
            ),
        ),
    ]
