import api from './api';

/**
 * POS API service
 * Handles all API calls related to POS terminals, sessions, and transactions
 */
export const posService = {
    /**
     * Get all POS terminals with optional pagination, search, and filtering
     * @param {{page_size: number}} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @returns {Promise} API response
     */
    getPOSList: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/pos/?${queryString}` : '/pos/';

        return api.get(url);
    },

    /**
     * Get POS terminal details by ID
     * @param {number} id - POS terminal ID
     * @returns {Promise} API response
     */
    getPOSDetails: (id) => {
        return api.get(`/pos/${id}/`);
    },

    /**
     * Create a new POS terminal
     * @param {Object} data - POS terminal data
     * @param {string} data.name - POS terminal name
     * @param {number} data.warehouse - Warehouse ID
     * @param {string} data.description - POS terminal description
     * @returns {Promise} API response
     */
    createPOS: (data) => {
        return api.post('/pos/', data);
    },

    /**
     * Update an existing POS terminal
     * @param {number} id - POS terminal ID
     * @param {Object} data - Updated POS terminal data
     * @returns {Promise} API response
     */
    updatePOS: (id, data) => {
        return api.put(`/pos/${id}/`, data);
    },

    /**
     * Delete a POS terminal
     * @param {number} id - POS terminal ID
     * @returns {Promise} API response
     */
    deletePOS: (id) => {
        return api.delete(`/pos/${id}/`);
    },

    /**
     * Start a new session for a POS terminal
     * @param {number} posId - POS terminal ID
     * @param {Object} data - Session data
     * @param {number} data.opening_balance - Opening balance for the session
     * @returns {Promise} API response
     */
    startSession: (posId, data) => {
        return api.post(`/pos/${posId}/start_session/`, data);
    },

    /**
     * Get all sessions with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.status - Filter by status
     * @param {number} params.pos - Filter by POS terminal ID
     * @param {string} params.opened_at_after - Filter sessions opened after this date
     * @param {string} params.opened_at_before - Filter sessions opened before this date
     * @returns {Promise} API response
     */
    getSessions: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/sessions/?${queryString}` : '/sessions/';

        return api.get(url);
    },

    /**
     * Get session details by ID
     * @param {number} id - Session ID
     * @returns {Promise} API response
     */
    getSessionDetails: (id) => {
        return api.get(`/sessions/${id}/`);
    },

    /**
     * Close a session
     * @param {number} id - Session ID
     * @param {Object} data - Close session data
     * @param {number} data.closing_balance - Closing balance for the session
     * @returns {Promise} API response
     */
    closeSession: (id, data) => {
        return api.post(`/sessions/${id}/close/`, data);
    },

    /**
     * Suspend a session
     * @param {number} id - Session ID
     * @returns {Promise} API response
     */
    suspendSession: (id) => {
        return api.post(`/sessions/${id}/suspend/`);
    },

    /**
     * Resume a suspended session
     * @param {number} id - Session ID
     * @returns {Promise} API response
     */
    resumeSession: (id) => {
        return api.post(`/sessions/${id}/resume/`);
    },

    /**
     * Get transactions for a specific session
     * @param {number} sessionId - Session ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.transaction_type - Filter by transaction type
     * @returns {Promise} API response
     */
    getSessionTransactions: (sessionId, params = {}) => {
        const queryParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/session/${sessionId}/transactions/?${queryString}` : `/session/${sessionId}/transactions/`;

        return api.get(url);
    },

    /**
     * Create a new transaction for a session
     * @param {number} sessionId - Session ID
     * @param {Object} data - Transaction data
     * @param {string} data.transaction_type - Type of transaction
     * @param {number} data.amount - Transaction amount
     * @param {string} data.description - Transaction description
     * @returns {Promise} API response
     */
    createSessionTransaction: (sessionId, data) => {
        return api.post(`/session/${sessionId}/transactions/`, data);
    },

    /**
     * Update a session transaction
     * @param {number} sessionId - Session ID
     * @param {number} transactionId - Transaction ID
     * @param {Object} data - Updated transaction data
     * @returns {Promise} API response
     */
    updateSessionTransaction: (sessionId, transactionId, data) => {
        return api.put(`/session/${sessionId}/transactions/${transactionId}/`, data);
    },

    /**
     * Delete a session transaction
     * @param {number} sessionId - Session ID
     * @param {number} transactionId - Transaction ID
     * @returns {Promise} API response
     */
    deleteSessionTransaction: (sessionId, transactionId) => {
        return api.delete(`/session/${sessionId}/transactions/${transactionId}/`);
    },

    /**
     * Delete a session (Note: This may not be available in the backend)
     * @param {number} sessionId - Session ID
     * @returns {Promise} API response
     */
    deleteSession: (sessionId) => {
        return api.delete(`/sessions/${sessionId}/`);
    }
};

export default posService;
