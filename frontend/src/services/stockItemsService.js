import api from './api';

/**
 * Stock Items API service
 * Handles all API calls related to stock items CRUD operations
 */
export const stockItemsService = {
    /**
     * Get all stock items for a warehouse with optional pagination, search, and filtering
     * @param {number} warehouseId - Warehouse ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @returns {Promise} API response
     */
    getStockItems: (warehouseId, params = {}) => {
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        
        // Add search parameter
        if (params.search) queryParams.append('search', params.search);
        
        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        const queryString = queryParams.toString();
        const url = `/warehouses/${warehouseId}/stock-items/${queryString ? `?${queryString}` : ''}`;
        
        return api.get(url);
    },

    /**
     * Get a single stock item by ID
     * @param {number} warehouseId - Warehouse ID
     * @param {number} stockItemId - Stock Item ID
     * @returns {Promise} API response
     */
    getStockItem: (warehouseId, stockItemId) => {
        return api.get(`/warehouses/${warehouseId}/stock-items/${stockItemId}/`);
    },

    /**
     * Create a new stock item
     * @param {number} warehouseId - Warehouse ID
     * @param {Object} stockItemData - Stock item data
     * @param {number} stockItemData.product - Product ID
     * @param {number} stockItemData.quantity - Quantity
     * @param {number} stockItemData.min_stock - Minimum stock level
     * @returns {Promise} API response
     */
    createStockItem: (warehouseId, stockItemData) => {
        return api.post(`/warehouses/${warehouseId}/stock-items/`, stockItemData);
    },

    /**
     * Update an existing stock item
     * @param {number} warehouseId - Warehouse ID
     * @param {number} stockItemId - Stock Item ID
     * @param {Object} stockItemData - Updated stock item data
     * @returns {Promise} API response
     */
    updateStockItem: (warehouseId, stockItemId, stockItemData) => {
        return api.put(`/warehouses/${warehouseId}/stock-items/${stockItemId}/`, stockItemData);
    },

    /**
     * Delete a stock item
     * @param {number} warehouseId - Warehouse ID
     * @param {number} stockItemId - Stock Item ID
     * @returns {Promise} API response
     */
    deleteStockItem: (warehouseId, stockItemId) => {
        return api.delete(`/warehouses/${warehouseId}/stock-items/${stockItemId}/`);
    }
};

export default stockItemsService;