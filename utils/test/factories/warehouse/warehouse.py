from factory import Faker
from factory.django import DjangoModelFactory

from warehouses.models.warehouse import Warehouse


class WarehouseFactory(DjangoModelFactory):
    """
    Factory for creating Warehouse instances for testing.
    """

    class Meta:
        model = Warehouse
        skip_postgeneration_save = True

    name = Faker("company")
    location = Faker("address")
    description = Faker("paragraph", nb_sentences=2)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unique names."""
        # Generate a unique name by appending a random string
        if "name" not in kwargs:
            base_name = Faker("company").generate({})
            counter = 1
            name = base_name
            while Warehouse.objects.filter(name=name).exists():
                name = f"{base_name} {counter}"
                counter += 1
            kwargs["name"] = name
        return super()._create(model_class, *args, **kwargs)
