from django.db.models.aggregates import Sum
from rest_framework import serializers

from invoices.models.invoice import Invoice
from invoices.serializers.invoice_item import (
    InvoiceItemReadSerializer,
    InvoiceItemSerializer,
)
from pos.models import POSSession
from pos.serializers.pos_session import POSSessionSerializer
from warehouses.models.stock_item import StockItem
from warehouses.serializers.warehouse import WarehouseSerializer


class InvoiceSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating Invoice model.
    """

    items = InvoiceItemSerializer(many=True, required=True)
    profit = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    pos_session = serializers.PrimaryKeyRelatedField(
        queryset=POSSession.objects.all(), required=True
    )

    class Meta:
        model = Invoice
        fields = [
            "id",
            "net_amount",
            "profit",
            "pos_session",
            "payment_method",
            "notes",
            "items",
        ]
        read_only_fields = ["profit", "warehouse", "net_amount"]
        required_fields = ["pos_session", "items"]

    def validate_pos_session(self, value):
        """
        Validate that the POS session is not already closed.
        """
        if not self.context["request"].user.is_admin:
            if value.pos.employee_set.filter(
                user=self.context["request"].user
            ).exists():
                raise serializers.ValidationError(
                    "Cannot create invoice for POS session not assigned to you"
                )

        if value and value.status == "closed":
            raise serializers.ValidationError(
                "Cannot create invoice for closed POS session"
            )
        return value

    def _calculate_profit(self, invoice):
        """
        Calculate the total profit for an invoice based on its items.
        Profit for each item = (unit_price - product.cost) * quantity
        """

        return invoice.items.aggregate(total_profit=Sum("profit"))["total_profit"] or 0

    def _calaculate_net_amount(self, invoice):
        """
        Calculate the total net amount for an invoice based on its items.
        Net amount for each item = unit_price * quantity
        """
        return (
            invoice.items.aggregate(total_amount=Sum("total_price"))["total_amount"]
            or 0
        )

    def _decrease_stock(self, invoice_items, warehouse):
        """
        Decrease stock in warehouse based on invoice items.
        """
        for item in invoice_items:
            product = item.product
            quantity = item.quantity
            # Get or create stock item for this product in the warehouse
            stock_item, created = StockItem.objects.get_or_create(
                warehouse=warehouse, product=product, defaults={"quantity": 0}
            )
            # Decrease stock
            stock_item.remove_stock(quantity)

    def _increates_pos_session_total_sales(self, invoice):
        """
        Increase the total sales of the POS session associated with the invoice.
        """
        pos_session = invoice.pos_session
        pos_session.total_sales += invoice.net_amount
        pos_session.save(update_fields=["total_sales"])

    def _restore_stock(self, invoice_items, warehouse):
        """
        Restore stock in warehouse based on invoice items.
        This is used when updating an invoice to restore the stock before applying new quantities.
        """
        for item in invoice_items:
            product = item.product
            quantity = item.quantity

            # Get stock item for this product in the warehouse
            try:
                stock_item = StockItem.objects.get(warehouse=warehouse, product=product)

                # Add back the quantity to restore stock
                stock_item.add_stock(quantity)
            except StockItem.DoesNotExist:
                # If stock item doesn't exist, we don't need to restore anything
                pass

    def create(self, validated_data):
        items_data = validated_data.pop("items", [])
        validated_data["warehouse"] = validated_data["pos_session"].pos.warehouse
        # Create invoice
        invoice = Invoice.objects.create(**validated_data)

        # Create invoice items
        for item_data in items_data:
            InvoiceItemSerializer().create({**item_data, "invoice": invoice})

        # Calculate and set profit
        invoice.profit = self._calculate_profit(invoice)
        invoice.net_amount = self._calaculate_net_amount(invoice)
        invoice.save(update_fields=["profit", "net_amount"])
        # Decrease stock in warehouse
        self._decrease_stock(invoice.items.all(), invoice.warehouse)
        self._increates_pos_session_total_sales(invoice)
        return invoice

    def update(self, instance, validated_data):
        items_data = validated_data.pop("items", None)

        # If items are provided, update them
        if items_data is not None:
            # Restore stock for existing items before deleting them
            self._restore_stock(instance.items.all(), instance.warehouse)

            # Clear existing items
            instance.items.all().delete()

            # Create new items
            for item_data in items_data:
                InvoiceItemSerializer().create({**item_data, "invoice": instance})

            # Recalculate and update profit
            instance.profit = self._calculate_profit(instance)
            instance.net_amount = self._calaculate_net_amount(instance)
            instance.save(update_fields=["profit", "net_amount"])

            # Decrease stock for new items
            self._decrease_stock(instance.items.all(), instance.warehouse)

        # Update invoice fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class InvoiceReadSerializer(serializers.ModelSerializer):
    """
    Serializer for reading Invoice model with expanded relationships.
    """

    items = InvoiceItemReadSerializer(many=True, read_only=True)
    warehouse = WarehouseSerializer(read_only=True)
    pos_session = POSSessionSerializer(read_only=True)

    class Meta:
        model = Invoice
        fields = [
            "id",
            "warehouse",
            "net_amount",
            "profit",
            "pos_session",
            "payment_method",
            "notes",
            "items",
            "created",
            "modified",
        ]
