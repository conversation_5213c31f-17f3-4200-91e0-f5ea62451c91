from django.contrib import admin

from .models import PurchaseReturn, PurchaseReturnItem


class PurchaseReturnItemInline(admin.TabularInline):
    model = PurchaseReturnItem
    extra = 0
    readonly_fields = ("total_cost", "created", "modified")
    fields = ("product", "quantity", "unit_cost", "total_cost", "notes")


@admin.register(PurchaseReturn)
class PurchaseReturnAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "supplier",
        "warehouse",
        "total_amount",
        "payment_method",
        "created"
    )
    list_filter = ("warehouse", "supplier", "payment_method", "created", "modified")
    search_fields = ("supplier__name", "notes")
    readonly_fields = ("created", "modified", "total_amount")
    date_hierarchy = "created"
    inlines = [PurchaseReturnItemInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("supplier", "warehouse", "pos_session")


@admin.register(PurchaseReturnItem)
class PurchaseReturnItemAdmin(admin.ModelAdmin):
    list_display = ("id", "purchase_return", "product", "quantity", "unit_cost", "total_cost", "created")
    list_filter = ("purchase_return__supplier", "product", "created")
    search_fields = ("purchase_return__supplier__name", "product__name", "notes")
    readonly_fields = ("created", "modified", "total_cost")
    date_hierarchy = "created"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("purchase_return", "product")
