from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class Warehouse(TimeStampedModel):
    """
    Warehouse model representing physical storage locations.
    """
    name = models.CharField(
        _('name'),
        max_length=100,
        help_text=_('Name of the warehouse')
    )
    
    location = models.CharField(
        _('location'),
        max_length=255,
        help_text=_('Physical location/address of the warehouse')
    )
    
    description = models.TextField(
        _('description'),
        blank=True,
        null=True,
        help_text=_('Additional information about the warehouse')
    )
    
    class Meta:
        verbose_name = _('warehouse')
        verbose_name_plural = _('warehouses')
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.location})"
    