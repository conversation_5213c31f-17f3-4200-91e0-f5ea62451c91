from django_filters.rest_framework import <PERSON>jango<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import status, viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from products.models.category import Category
from products.serialzers.category import CategorySerializer
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows categories to be viewed or edited.
    """

    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    pagination_class = PaginationClass
    search_fields = ["name", "description"]
    ordering_fields = ["name", "created"]
    ordering = ["name"]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["list", "retrieve"]:
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAdminOnly]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Optionally filter categories by parent_id
        Example: /api/categories/?parent__isnull=true
        """
        queryset = super().get_queryset()
        parent_id = self.request.query_params.get("parent_id")

        if parent_id is not None:
            if parent_id.lower() == "null":
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)

        return queryset

    def destroy(self, request, *args, **kwargs):
        """
        Custom destroy method to handle category deletion with products
        """
        instance = self.get_object()

        # Check if category has products
        if instance.products.exists():
            return Response(
                {
                    "detail": "Cannot delete category with associated products. Please remove or reassign products first."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if category has subcategories
        if instance.children.exists():
            return Response(
                {
                    "detail": "Cannot delete category with subcategories. Please delete or reassign subcategories first."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)
