import random
from decimal import Decimal

from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from utils.test.factories.product.product import ProductFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models.stock_item import StockItem


class StockItemFactory(DjangoModelFactory):
    """
    Factory for creating StockItem instances for testing.
    """

    class Meta:
        model = StockItem
        skip_postgeneration_save = True
        django_get_or_create = ("warehouse", "product")

    warehouse = SubFactory(WarehouseFactory)
    product = SubFactory(ProductFactory)

    # Generate a random quantity between 10 and 100
    quantity = LazyAttribute(
        lambda _: Decimal(random.uniform(10, 100)).quantize(Decimal("0.001"))
    )

    # Generate a minimum stock level between 1 and 10
    min_stock = LazyAttribute(
        lambda _: Decimal(random.uniform(1, 10)).quantize(Decimal("0.001"))
    )
