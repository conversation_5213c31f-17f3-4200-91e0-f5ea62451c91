from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from accounts.models.account import Account
from pos.models import POS


class Balance(TimeStampedModel):
    """
    Model to track balances related to both accounts and POS.
    This creates a many-to-many relationship between accounts and POS.
    """

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name="balances",
        verbose_name=_("account"),
        help_text=_("The account this balance is associated with"),
    )

    pos = models.ForeignKey(
        POS,
        on_delete=models.CASCADE,
        related_name="balances",
        verbose_name=_("POS"),
        help_text=_("The POS this balance is associated with"),
        null=True,
        blank=True,
    )

    amount = models.DecimalField(
        _("amount"),
        max_digits=15,
        decimal_places=2,
        help_text=_("The amount of the balance"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this balance record"),
    )

    class Meta:
        verbose_name = _("balance")
        verbose_name_plural = _("balances")
        ordering = ["-created"]
        unique_together = [["account", "pos"]]

    def __str__(self):
        return f"{self.account} - {self.amount} - {self.pos}"
