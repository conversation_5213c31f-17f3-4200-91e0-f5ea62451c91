import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Row,
    Spinner
} from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { posService } from '../../services/posService';
import { warehousesService } from '../../services/warehousesService';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { FaArrowLeft, FaSave } from 'react-icons/fa';

/**
 * POSEdit component
 * Form for editing an existing POS terminal
 */
const POSEdit = () => {
    const navigate = useNavigate();
    const { posId } = useParams();
    const { currentUser } = useAuth();
    
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [error, setError] = useState('');
    const [pos, setPOS] = useState(null);
    const [warehouses, setWarehouses] = useState([]);
    const [warehousesLoading, setWarehousesLoading] = useState(true);

    // Validation schema
    const validationSchema = Yup.object().shape({
        name: Yup.string()
            .required('POS name is required')
            .min(2, 'POS name must be at least 2 characters')
            .max(100, 'POS name must not exceed 100 characters'),
        warehouse: Yup.number()
            .required('Warehouse is required')
            .typeError('Please select a warehouse'),
        description: Yup.string()
            .max(500, 'Description must not exceed 500 characters')
    });

    // Fetch POS details
    const fetchPOSDetails = async () => {
        try {
            setInitialLoading(true);
            setError('');
            
            const response = await posService.getPOSDetails(posId);
            setPOS(response.data);
            
        } catch (err) {
            console.error('Error fetching POS details:', err);
            setError('Failed to load POS details. Please try again.');
        } finally {
            setInitialLoading(false);
        }
    };

    // Fetch warehouses
    const fetchWarehouses = async () => {
        try {
            setWarehousesLoading(true);
            const response = await warehousesService.getWarehouses();
            setWarehouses(response.data.results || response.data || []);
        } catch (err) {
            console.error('Error fetching warehouses:', err);
            setError('Failed to load warehouses. Please try again.');
        } finally {
            setWarehousesLoading(false);
        }
    };

    // Handle form submission
    const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
        try {
            setLoading(true);
            setError('');

            const response = await posService.updatePOS(posId, values);
            
            // Navigate to the updated POS details page
            navigate(`/pos/${posId}`);

        } catch (err) {
            console.error('Error updating POS:', err);
            
            if (err.response?.data) {
                // Handle field-specific errors
                const errors = err.response.data;
                Object.keys(errors).forEach(field => {
                    if (typeof errors[field] === 'string') {
                        setFieldError(field, errors[field]);
                    } else if (Array.isArray(errors[field])) {
                        setFieldError(field, errors[field][0]);
                    }
                });
            } else {
                setError('Failed to update POS terminal. Please try again.');
            }
        } finally {
            setLoading(false);
            setSubmitting(false);
        }
    };

    // Load data on component mount
    useEffect(() => {
        if (posId) {
            fetchPOSDetails();
            fetchWarehouses();
        }
    }, [posId]);

    if (initialLoading) {
        return (
            <Container>
                <div className="text-center p-4">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            </Container>
        );
    }

    if (!pos) {
        return (
            <Container>
                <Alert variant="danger">
                    POS terminal not found or you don't have permission to edit it.
                </Alert>
            </Container>
        );
    }

    // Initial form values from POS data
    const initialValues = {
        name: pos.name || '',
        warehouse: pos.warehouse?.id || '',
        description: pos.description || ''
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate(`/pos/${posId}`)}
                                className="me-3"
                            >
                                <FaArrowLeft />
                            </Button>
                            <h2>Edit POS Terminal: {pos.name}</h2>
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row>
                <Col md={8} className="mx-auto">
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Edit POS Terminal Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Formik
                                initialValues={initialValues}
                                validationSchema={validationSchema}
                                onSubmit={handleSubmit}
                                enableReinitialize={true}
                            >
                                {({
                                    values,
                                    errors,
                                    touched,
                                    handleChange,
                                    handleBlur,
                                    handleSubmit,
                                    isSubmitting
                                }) => (
                                    <Form onSubmit={handleSubmit}>
                                        <Row>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>
                                                        POS Name <span className="text-danger">*</span>
                                                    </Form.Label>
                                                    <Form.Control
                                                        type="text"
                                                        name="name"
                                                        value={values.name}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        isInvalid={touched.name && errors.name}
                                                        placeholder="Enter POS terminal name"
                                                    />
                                                    <Form.Control.Feedback type="invalid">
                                                        {errors.name}
                                                    </Form.Control.Feedback>
                                                </Form.Group>
                                            </Col>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>
                                                        Warehouse <span className="text-danger">*</span>
                                                    </Form.Label>
                                                    <Form.Select
                                                        name="warehouse"
                                                        value={values.warehouse}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        isInvalid={touched.warehouse && errors.warehouse}
                                                        disabled={warehousesLoading}
                                                    >
                                                        <option value="">Select a warehouse</option>
                                                        {warehouses.map(warehouse => (
                                                            <option key={warehouse.id} value={warehouse.id}>
                                                                {warehouse.name}
                                                                {warehouse.location && ` - ${warehouse.location}`}
                                                            </option>
                                                        ))}
                                                    </Form.Select>
                                                    <Form.Control.Feedback type="invalid">
                                                        {errors.warehouse}
                                                    </Form.Control.Feedback>
                                                    {warehousesLoading && (
                                                        <Form.Text className="text-muted">
                                                            Loading warehouses...
                                                        </Form.Text>
                                                    )}
                                                </Form.Group>
                                            </Col>
                                        </Row>

                                        <Row>
                                            <Col>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Description</Form.Label>
                                                    <Form.Control
                                                        as="textarea"
                                                        rows={3}
                                                        name="description"
                                                        value={values.description}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        isInvalid={touched.description && errors.description}
                                                        placeholder="Enter description (optional)"
                                                    />
                                                    <Form.Control.Feedback type="invalid">
                                                        {errors.description}
                                                    </Form.Control.Feedback>
                                                    <Form.Text className="text-muted">
                                                        Optional description for the POS terminal
                                                    </Form.Text>
                                                </Form.Group>
                                            </Col>
                                        </Row>

                                        <div className="d-flex justify-content-end gap-2">
                                            <Button
                                                variant="secondary"
                                                onClick={() => navigate(`/pos/${posId}`)}
                                                disabled={isSubmitting || loading}
                                            >
                                                Cancel
                                            </Button>
                                            <Button
                                                type="submit"
                                                variant="primary"
                                                disabled={isSubmitting || loading || warehousesLoading}
                                            >
                                                {isSubmitting || loading ? (
                                                    <>
                                                        <Spinner
                                                            as="span"
                                                            animation="border"
                                                            size="sm"
                                                            role="status"
                                                            aria-hidden="true"
                                                            className="me-2"
                                                        />
                                                        Updating...
                                                    </>
                                                ) : (
                                                    <>
                                                        <FaSave className="me-2" />
                                                        Update POS Terminal
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </Form>
                                )}
                            </Formik>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default POSEdit;
