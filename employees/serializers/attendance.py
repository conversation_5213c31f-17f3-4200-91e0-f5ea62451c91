from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import serializers

from ..models import Attendance, Employee

User = get_user_model()


class AttendanceAuthSerializer(serializers.Serializer):
    username = serializers.CharField(required=True, help_text="Email or phone number")
    password = serializers.CharField(required=True, write_only=True)
    image = serializers.ImageField(required=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")
        # Determine if the username is an email or phone number
        is_email = "@" in username
        try:
            if is_email:
                user = User.objects.get(email=username)
            else:
                user = User.objects.get(phone_number=username)
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid credentials")
            # Verify password
        if not user.check_password(password):
            raise serializers.ValidationError("Invalid credentials")
        # Check if user has an associated employee
        try:
            employee = user.employee
        except Employee.DoesNotExist:
            raise serializers.ValidationError("No employee associated with this user")
        attrs["employee"] = employee
        return attrs


class TimeInSerializer(AttendanceAuthSerializer):
    def validate(self, attrs):
        attrs = super().validate(attrs)
        employee = attrs["employee"]

        # Check if there's an open attendance record
        last_attendance = (
            Attendance.objects.filter(employee=employee, time_out__isnull=True)
            .order_by("-time_in")
            .first()
        )

        if last_attendance:
            raise serializers.ValidationError(
                "You already have an open attendance record"
            )

        attrs["employee"] = employee
        return attrs

    def create(self, validated_data):
        employee = validated_data["employee"]
        image = validated_data["image"]

        # Create new attendance record for time-in
        attendance = Attendance.objects.create(
            employee=employee, time_in=timezone.now(), time_in_image=image
        )
        return attendance


class TimeOutSerializer(AttendanceAuthSerializer):
    def validate(self, attrs):
        attrs = super().validate(attrs)
        employee = attrs["employee"]

        # Check if there's an open attendance record
        last_attendance = (
            Attendance.objects.filter(employee=employee, time_out__isnull=True)
            .order_by("-time_in")
            .first()
        )

        if not last_attendance:
            raise serializers.ValidationError("No open attendance record found")

        attrs["attendance"] = last_attendance
        return attrs

    def update(self, instance, validated_data):
        instance.time_out = timezone.now()
        instance.time_out_image = validated_data["image"]
        instance.save()
        return instance


class AttendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attendance
        fields = [
            "id",
            "employee",
            "time_in",
            "time_out",
            "total_hours",
            "time_in_image",
            "time_out_image",
            "notes",
            "created",
            "modified",
        ]
        read_only_fields = ["id", "created", "modified"]
