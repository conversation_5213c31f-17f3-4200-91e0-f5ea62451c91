import random
from datetime import date, timed<PERSON>ta
from decimal import Decimal
import os

import factory
from factory import <PERSON>zy<PERSON><PERSON><PERSON>but<PERSON>, SubFactory
from factory.django import DjangoModelFactory
from django.core.files import File

from employees.models.employee import Employee
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.user import UserFactory


class EmployeeFactory(DjangoModelFactory):
    """
    Factory for creating Employee instances for testing.
    """

    class Meta:
        model = Employee
        skip_postgeneration_save = True

    user = SubFactory(UserFactory)
    address = factory.Faker("address")
    type = Employee.EmployeeType.FULL_TIME

    # Generate random rates
    hour_rate = LazyAttribute(
        lambda _: Decimal(random.uniform(10, 30)).quantize(Decimal("0.01"))
    )
    day_rate = LazyAttribute(
        lambda _: Decimal(random.uniform(80, 240)).quantize(Decimal("0.01"))
    )

    # Generate a hire date within the last 2 years
    hire_date = LazyAttribute(
        lambda _: date.today() - timedelta(days=random.randint(1, 730))
    )

    status = Employee.Status.ACTIVE
    pos = SubFactory(POSFactory)

    # Use a placeholder for identification
    identification = None

    @factory.post_generation
    def identification(self, create, extracted, **kwargs):
        """
        Handle identification image.
        If no image is provided, use the test image file.
        """
        if not create:
            return

        if extracted:
            self.identification = extracted
        else:
            # Use the real test image file
            test_image_path = os.path.join(
                os.path.dirname(__file__), 'test_image.png'
            )
            if os.path.exists(test_image_path):
                with open(test_image_path, 'rb') as f:
                    self.identification.save(
                        f'test_id_{self.id}.png',
                        File(f),
                        save=True
                    )
