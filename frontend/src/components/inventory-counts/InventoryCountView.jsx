import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Mo<PERSON>,
    <PERSON>,
    Spinner
} from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useInventoryCount, useInventoryCounts } from '../../hooks/useInventoryCounts';
import InventoryCountItemsTable from './InventoryCountItemsTable';

/**
 * Component for viewing inventory count details
 * @returns {JSX.Element} The inventory count view component
 */
const InventoryCountView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { currentUser } = useAuth();
    const { inventoryCount, loading, error, refetch } = useInventoryCount(id);
    const { completeInventoryCount, cancelInventoryCount } = useInventoryCounts();
    
    const [actionLoading, setActionLoading] = useState(false);
    const [actionError, setActionError] = useState(null);
    const [showCompleteModal, setShowCompleteModal] = useState(false);
    const [showCancelModal, setShowCancelModal] = useState(false);

    // Get status badge variant
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'draft':
                return 'secondary';
            case 'in_progress':
                return 'primary';
            case 'completed':
                return 'success';
            case 'cancelled':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    // Check permissions
    const canEdit = () => {
        return currentUser?.role === 'admin' && 
               inventoryCount && 
               ['draft', 'in_progress'].includes(inventoryCount.status);
    };

    const canComplete = () => {
        return currentUser?.role === 'admin' && 
               inventoryCount && 
               ['draft', 'in_progress'].includes(inventoryCount.status);
    };

    const canCancel = () => {
        return currentUser?.role === 'admin' && 
               inventoryCount && 
               ['draft', 'in_progress'].includes(inventoryCount.status);
    };

    // Handle complete action
    const handleComplete = async () => {
        try {
            setActionLoading(true);
            setActionError(null);
            
            await completeInventoryCount(id);
            await refetch(); // Refresh the inventory count data
            setShowCompleteModal(false);
        } catch (err) {
            setActionError(err.response?.data?.detail || 'Failed to complete inventory count');
        } finally {
            setActionLoading(false);
        }
    };

    // Handle cancel action
    const handleCancel = async () => {
        try {
            setActionLoading(true);
            setActionError(null);
            
            await cancelInventoryCount(id);
            await refetch(); // Refresh the inventory count data
            setShowCancelModal(false);
        } catch (err) {
            setActionError(err.response?.data?.detail || 'Failed to cancel inventory count');
        } finally {
            setActionLoading(false);
        }
    };

    // Handle item updates (refresh inventory count data)
    const handleItemUpdate = () => {
        refetch();
    };

    if (loading) {
        return (
            <Container fluid>
                <div className="text-center py-5">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            </Container>
        );
    }

    if (error) {
        return (
            <Container fluid>
                <Alert variant="danger">
                    <Alert.Heading>Error</Alert.Heading>
                    <p>{error}</p>
                    <Button variant="outline-danger" onClick={() => navigate('/inventory-counts')}>
                        Back to Inventory Counts
                    </Button>
                </Alert>
            </Container>
        );
    }

    if (!inventoryCount) {
        return (
            <Container fluid>
                <Alert variant="warning">
                    <Alert.Heading>Not Found</Alert.Heading>
                    <p>The requested inventory count was not found.</p>
                    <Button variant="outline-warning" onClick={() => navigate('/inventory-counts')}>
                        Back to Inventory Counts
                    </Button>
                </Alert>
            </Container>
        );
    }

    return (
        <Container fluid>
            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-start">
                        <div>
                            <h2>Inventory Count #{inventoryCount.id}</h2>
                            <p className="text-muted mb-0">
                                {inventoryCount.warehouse_name} • Created {new Date(inventoryCount.created).toLocaleString()}
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/inventory-counts')}
                            >
                                Back to List
                            </Button>
                            {canEdit() && (
                                <Button
                                    variant="outline-primary"
                                    onClick={() => navigate(`/inventory-counts/${id}/edit`)}
                                >
                                    Edit
                                </Button>
                            )}
                            {canComplete() && (
                                <Button
                                    variant="success"
                                    onClick={() => setShowCompleteModal(true)}
                                    disabled={actionLoading}
                                >
                                    Complete Count
                                </Button>
                            )}
                            {canCancel() && (
                                <Button
                                    variant="danger"
                                    onClick={() => setShowCancelModal(true)}
                                    disabled={actionLoading}
                                >
                                    Cancel Count
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Action Error Alert */}
            {actionError && (
                <Alert variant="danger" dismissible onClose={() => setActionError(null)}>
                    {actionError}
                </Alert>
            )}

            {/* Inventory Count Details */}
            <Row className="mb-4">
                <Col lg={8}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Count Details</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <dl className="row">
                                        <dt className="col-sm-4">Status:</dt>
                                        <dd className="col-sm-8">
                                            <Badge bg={getStatusBadgeVariant(inventoryCount.status)}>
                                                {inventoryCount.status_display}
                                            </Badge>
                                        </dd>
                                        
                                        <dt className="col-sm-4">Warehouse:</dt>
                                        <dd className="col-sm-8">{inventoryCount.warehouse_name}</dd>
                                        
                                        <dt className="col-sm-4">Started At:</dt>
                                        <dd className="col-sm-8">
                                            {inventoryCount.started_at ? 
                                                new Date(inventoryCount.started_at).toLocaleString() : 
                                                'Not started'
                                            }
                                        </dd>
                                        
                                        <dt className="col-sm-4">Ended At:</dt>
                                        <dd className="col-sm-8">
                                            {inventoryCount.ended_at ? 
                                                new Date(inventoryCount.ended_at).toLocaleString() : 
                                                'Not completed'
                                            }
                                        </dd>
                                    </dl>
                                </Col>
                                <Col md={6}>
                                    <dl className="row">
                                        <dt className="col-sm-4">Item Count:</dt>
                                        <dd className="col-sm-8">{inventoryCount.item_count || 0}</dd>
                                        
                                        <dt className="col-sm-4">Total Cost:</dt>
                                        <dd className="col-sm-8">${inventoryCount.total_cost || '0.00'}</dd>
                                        
                                        <dt className="col-sm-4">Total Price:</dt>
                                        <dd className="col-sm-8">${inventoryCount.total_price || '0.00'}</dd>
                                        
                                        <dt className="col-sm-4">Noted By:</dt>
                                        <dd className="col-sm-8">
                                            {inventoryCount.noted_by || 'Not assigned'}
                                        </dd>
                                    </dl>
                                </Col>
                            </Row>
                            
                            {inventoryCount.notes && (
                                <div className="mt-3">
                                    <strong>Notes:</strong>
                                    <p className="mt-1 mb-0">{inventoryCount.notes}</p>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
                <Col lg={4}>
                    <Card>
                        <Card.Header>
                            <h6 className="mb-0">Summary Statistics</h6>
                        </Card.Header>
                        <Card.Body>
                            <div className="text-center">
                                <div className="mb-3">
                                    <h4 className="text-primary">{inventoryCount.item_count || 0}</h4>
                                    <small className="text-muted">Total Items</small>
                                </div>
                                
                                <div className="row text-center">
                                    <div className="col-6">
                                        <div className="border-end">
                                            <h5 className="text-success">${inventoryCount.total_cost || '0.00'}</h5>
                                            <small className="text-muted">Cost Difference</small>
                                        </div>
                                    </div>
                                    <div className="col-6">
                                        <h5 className="text-info">${inventoryCount.total_price || '0.00'}</h5>
                                        <small className="text-muted">Price Difference</small>
                                    </div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Inventory Count Items */}
            <InventoryCountItemsTable
                inventoryCountId={id}
                inventoryCountStatus={inventoryCount.status}
                onItemUpdate={handleItemUpdate}
            />

            {/* Complete Confirmation Modal */}
            <Modal show={showCompleteModal} onHide={() => setShowCompleteModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Complete Inventory Count</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>
                        Are you sure you want to complete this inventory count?
                    </p>
                    <p className="text-muted">
                        This action will finalize the count and update stock levels based on 
                        the recorded quantities. This cannot be undone.
                    </p>
                </Modal.Body>
                <Modal.Footer>
                    <Button 
                        variant="secondary" 
                        onClick={() => setShowCompleteModal(false)}
                        disabled={actionLoading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        variant="success" 
                        onClick={handleComplete}
                        disabled={actionLoading}
                    >
                        {actionLoading ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Completing...
                            </>
                        ) : (
                            'Complete Count'
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Cancel Confirmation Modal */}
            <Modal show={showCancelModal} onHide={() => setShowCancelModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Cancel Inventory Count</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>
                        Are you sure you want to cancel this inventory count?
                    </p>
                    <p className="text-muted">
                        This action will cancel the count and no changes will be made to stock levels. 
                        The count will be marked as cancelled and cannot be resumed.
                    </p>
                </Modal.Body>
                <Modal.Footer>
                    <Button 
                        variant="secondary" 
                        onClick={() => setShowCancelModal(false)}
                        disabled={actionLoading}
                    >
                        Keep Count
                    </Button>
                    <Button 
                        variant="danger" 
                        onClick={handleCancel}
                        disabled={actionLoading}
                    >
                        {actionLoading ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Cancelling...
                            </>
                        ) : (
                            'Cancel Count'
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default InventoryCountView;
