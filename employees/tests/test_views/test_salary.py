from datetime import date, timed<PERSON><PERSON>
from decimal import Decimal

from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from employees.models import Attendance, Employee, Salary
from users.models import User
from utils.test.base_test import BaseTestCase
from utils.test.factories.employee.employee import EmployeeFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class SalaryViewSetTestCase(BaseTestCase):
    """
    Test cases for SalaryViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("employees:salary-list")
        self.detail_url_name = "employees:salary-detail"

        # Create additional test data
        self.test_warehouse = WarehouseFactory.create(name="Test Warehouse 2")
        self.test_pos = POSFactory.create(warehouse=self.test_warehouse, name="Test POS 2")

        # Create test employees
        self.test_employee1 = EmployeeFactory.create(
            user__first_name="<PERSON>",
            user__last_name="<PERSON><PERSON>",
            user__email="<EMAIL>",
            user__phone_number="01234567890",
            user__role=User.Role.CASHIER,
            hour_rate=Decimal("25.00"),
            day_rate=Decimal("200.00"),
            status=Employee.Status.ACTIVE,
            pos=self.pos,
        )

        self.test_employee2 = EmployeeFactory.create(
            user__first_name="Jane",
            user__last_name="Smith",
            user__email="<EMAIL>",
            user__phone_number="01234567891",
            user__role=User.Role.MANAGER,
            hour_rate=Decimal("30.00"),
            day_rate=Decimal("240.00"),
            status=Employee.Status.ACTIVE,
            pos=self.test_pos,
        )

        # Create test attendance records
        self.attendance1 = Attendance.objects.create(
            employee=self.test_employee1,
            time_in=timezone.now() - timedelta(hours=8),
            time_out=timezone.now() - timedelta(hours=1),
            notes="Test attendance 1"
        )

        self.attendance2 = Attendance.objects.create(
            employee=self.test_employee2,
            time_in=timezone.now() - timedelta(days=1, hours=6),
            time_out=timezone.now() - timedelta(days=1),
            notes="Test attendance 2"
        )

        # Create test salary records
        self.salary1 = Salary.objects.create(
            employee=self.test_employee1,
            attendance=self.attendance1,
            salary=Decimal("175.00"),
            notes="Test salary 1"
        )

        self.salary2 = Salary.objects.create(
            employee=self.test_employee2,
            attendance=self.attendance2,
            salary=Decimal("180.00"),
            notes="Test salary 2"
        )

    def get_detail_url(self, salary_id):
        """Helper method to get detail URL for a salary record."""
        return reverse(self.detail_url_name, kwargs={"pk": salary_id})

    # Authentication and Permission Tests
    def test_list_salaries_unauthenticated(self):
        """Test that unauthenticated users cannot list salary records."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_salaries_as_manager(self):
        """Test that manager users cannot list salary records (admin only)."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_salaries_as_cashier(self):
        """Test that cashier users cannot list salary records (admin only)."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_salaries_as_admin(self):
        """Admin should see all salary records."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test salary records
        self.assertGreaterEqual(len(response.data["results"]), 2)

        # Check that salary records are ordered by created (default ordering)
        created_dates = [sal["created"] for sal in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    def test_retrieve_salary_as_admin(self):
        """Admin should be able to retrieve any salary record."""
        url = self.get_detail_url(self.salary1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data (detailed serializer)
        self.assertEqual(response.data["id"], self.salary1.id)
        self.assertEqual(response.data["employee_id"], self.test_employee1.id)
        self.assertEqual(response.data["employee_name"], "John Doe")
        self.assertEqual(response.data["employee_first_name"], "John")
        self.assertEqual(response.data["employee_last_name"], "Doe")
        self.assertEqual(response.data["salary"], "175.00")
        self.assertEqual(response.data["notes"], "Test salary 1")
        self.assertIsNotNone(response.data["attendance_id"])
        self.assertIsNotNone(response.data["hourly_equivalent"])
        self.assertIsNotNone(response.data["work_date"])

    def test_retrieve_salary_as_manager(self):
        """Manager should not be able to retrieve salary records (admin only)."""
        url = self.get_detail_url(self.salary1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_salary_as_cashier(self):
        """Cashier should not be able to retrieve salary records (admin only)."""
        url = self.get_detail_url(self.salary1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_salary(self):
        """Test retrieving a non-existent salary record."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Filtering Tests
    def test_filter_salaries_by_employee(self):
        """Test filtering salary records by employee."""
        url = f"{self.list_url}?employee={self.test_employee1.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only salary records for test_employee1
        for salary in response.data["results"]:
            # Using summary serializer, so we check employee_name
            self.assertEqual(salary["employee_name"], "John Doe")

    def test_filter_salaries_by_salary_range(self):
        """Test filtering salary records by salary amount range."""
        url = f"{self.list_url}?salary_min=170&salary_max=180"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries within the range
        for salary in response.data["results"]:
            salary_amount = Decimal(salary["salary"])
            self.assertGreaterEqual(salary_amount, Decimal("170"))
            self.assertLessEqual(salary_amount, Decimal("180"))

    def test_filter_salaries_by_custom_salary_range(self):
        """Test filtering using custom salary_min and salary_max parameters."""
        url = f"{self.list_url}?salary_min=175"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries >= 175
        for salary in response.data["results"]:
            salary_amount = Decimal(salary["salary"])
            self.assertGreaterEqual(salary_amount, Decimal("175"))

    def test_filter_salaries_by_date_range(self):
        """Test filtering salary records by creation date range."""
        today = date.today()
        url = f"{self.list_url}?created_after={today}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries created today or later
        self.assertGreaterEqual(len(response.data["results"]), 2)

    def test_filter_salaries_by_employee_type(self):
        """Test filtering salary records by employee type."""
        url = f"{self.list_url}?employee__type={Employee.EmployeeType.FULL_TIME}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries for full-time employees only
        # (Note: This depends on the employee factory default type)

    def test_filter_salaries_by_employee_status(self):
        """Test filtering salary records by employee status."""
        url = f"{self.list_url}?employee__status={Employee.Status.ACTIVE}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries for active employees only
        self.assertGreaterEqual(len(response.data["results"]), 2)

    def test_filter_salaries_by_pos(self):
        """Test filtering salary records by POS."""
        url = f"{self.list_url}?employee__pos={self.pos.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salaries for employees at the specified POS
        self.assertGreater(len(response.data["results"]), 0)

    # Search Tests
    def test_search_salaries_by_employee_first_name(self):
        """Test searching salary records by employee first name."""
        url = f"{self.list_url}?search=John"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salary records for employees with first name containing "John"
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_salaries_by_employee_last_name(self):
        """Test searching salary records by employee last name."""
        url = f"{self.list_url}?search=Smith"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salary records for employees with last name containing "Smith"
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_salaries_by_notes(self):
        """Test searching salary records by notes."""
        url = f"{self.list_url}?search=Test salary 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salary records with notes containing the search term
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_salaries_case_insensitive(self):
        """Test that search is case insensitive."""
        url = f"{self.list_url}?search=john"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return salary records even with lowercase search
        self.assertGreater(len(response.data["results"]), 0)

    # Ordering Tests
    def test_default_ordering_by_created(self):
        """Test that salary records are ordered by created date by default (descending)."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that salary records are ordered by created date (descending)
        created_dates = [sal["created"] for sal in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    def test_ordering_by_salary_ascending(self):
        """Test ordering salary records by salary amount (ascending)."""
        url = f"{self.list_url}?ordering=salary"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that salary records are ordered by salary amount (ascending)
        salaries = [Decimal(sal["salary"]) for sal in response.data["results"]]
        self.assertEqual(salaries, sorted(salaries))

    def test_ordering_by_salary_descending(self):
        """Test ordering salary records by salary amount (descending)."""
        url = f"{self.list_url}?ordering=-salary"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that salary records are ordered by salary amount (descending)
        salaries = [Decimal(sal["salary"]) for sal in response.data["results"]]
        self.assertEqual(salaries, sorted(salaries, reverse=True))

    def test_ordering_by_employee_name(self):
        """Test ordering salary records by employee first name."""
        url = f"{self.list_url}?ordering=employee__user__first_name"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that salary records are ordered by employee first name
        employee_names = [sal["employee_name"].split()[0] for sal in response.data["results"]]
        self.assertEqual(employee_names, sorted(employee_names))

    # Combined Tests
    def test_filter_search_and_ordering_combined(self):
        """Test combining filtering, searching, and ordering."""
        url = f"{self.list_url}?employee__status=active&search=John&ordering=-salary"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return filtered and searched results in descending salary order
        if response.data["results"]:
            salaries = [Decimal(sal["salary"]) for sal in response.data["results"]]
            self.assertEqual(salaries, sorted(salaries, reverse=True))

    # Serializer Tests
    def test_list_uses_summary_serializer(self):
        """Test that list action uses SalarySummarySerializer."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Summary serializer should have limited fields
        if response.data["results"]:
            salary_data = response.data["results"][0]
            expected_fields = {
                "id", "salary", "employee_name", "employee_pos_name",
                "work_date", "total_hours", "created"
            }
            self.assertEqual(set(salary_data.keys()), expected_fields)

    def test_retrieve_uses_detailed_serializer(self):
        """Test that retrieve action uses detailed SalarySerializer."""
        url = self.get_detail_url(self.salary1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Detailed serializer should have many fields
        detailed_fields = {
            "id", "salary", "notes", "created", "modified",
            "employee_id", "employee_name", "employee_first_name", "employee_last_name",
            "employee_email", "employee_phone", "employee_role", "employee_type",
            "employee_status", "employee_pos_name", "employee_hour_rate", "employee_day_rate",
            "attendance_id", "attendance_time_in", "attendance_time_out",
            "attendance_total_hours", "attendance_notes",
            "hourly_equivalent", "work_date"
        }
        self.assertEqual(set(response.data.keys()), detailed_fields)

    # Edge Cases
    def test_readonly_viewset_restrictions(self):
        """Test that SalaryViewSet is read-only (no create/update/delete)."""
        # Test that POST to list endpoint is not allowed
        data = {
            "employee": self.test_employee1.id,
            "attendance": self.attendance1.id,
            "salary": "100.00",
            "notes": "Test note"
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

        # Test that PUT to detail endpoint is not allowed
        url = self.get_detail_url(self.salary1.id)
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

        # Test that DELETE to detail endpoint is not allowed
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)