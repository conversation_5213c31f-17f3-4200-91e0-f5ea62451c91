from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from utils.test.base_test import BaseTestCase
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.warehouse.inventory_count import (
    InventoryCountFactory,
)
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models import InventoryCount


class InventoryCountViewSetTestCase(BaseTestCase):
    """Test cases for InventoryCountViewSet"""

    def setUp(self):
        super().setUp()
        self.url = reverse("warehouses:inventory-count-list")
        self.completed_count = InventoryCountFactory(
            warehouse=self.warehouse, status=InventoryCount.Status.COMPLETED
        )
        self.cancelled_count = InventoryCountFactory(
            warehouse=self.warehouse, status=InventoryCount.Status.CANCELLED
        )
        self.draft_count = InventoryCountFactory(warehouse=self.warehouse)
        self.warehouse2 = WarehouseFactory.create(name="Test Warehouse2")
        self.pos2 = POSFactory.create(warehouse=self.warehouse2)
        self.draft_count2 = InventoryCountFactory(warehouse=self.warehouse2)

    def test_list_inventory_counts_admin(self):
        """Admin can list all inventory counts"""
        response = self.admin_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 4)

    def test_list_inventory_counts_manager(self):
        """Manager can list draft and in-progress counts
        in warehouse that related to the pos that he works there"""
        response = self.manager_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

    def test_list_inventory_counts_cashier(self):
        """Cashier cannot list inventory counts"""
        response = self.cashier_client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_inventory_counts_unauthorized(self):
        """Unauthenticated users cannot access the list"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_inventory_count_admin(self):
        """Admin can retrieve any inventory count"""
        url = reverse(
            "warehouses:inventory-count-detail", args=[self.completed_count.id]
        )
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.completed_count.id)

    def test_retrieve_inventory_count_manager(self):
        """Manager can retrieve draft and in-progress counts"""
        # Can access draft count
        url = reverse("warehouses:inventory-count-detail", args=[self.draft_count.id])
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Cannot access completed count
        url = reverse(
            "warehouses:inventory-count-detail", args=[self.completed_count.id]
        )
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Cannot access other warehouse count
        url = reverse("warehouses:inventory-count-detail", args=[self.draft_count2.id])
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_inventory_count_cashier(self):
        """Cashier cannot retrieve inventory counts"""
        url = reverse("warehouses:inventory-count-detail", args=[self.draft_count.id])
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_inventory_count_admin(self):
        """Admin can create a new inventory count and if there is an inventory count in progress or draft in this
        warehouse, it should be cancelled before creating a new one"""
        data = {"warehouse": self.warehouse.id, "notes": "New inventory count"}
        response = self.admin_client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["non_field_errors"],
            [
                ErrorDetail(
                    string="An inventory count is already in progress or draft in this warehouse",
                    code="invalid",
                )
            ],
        )
        self.draft_count.delete()
        response = self.admin_client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["status"], InventoryCount.Status.DRAFT)

    def test_create_inventory_count_unauthorized(self):
        """Non-admin users cannot create inventory counts"""
        data = {"warehouse": self.warehouse.id}
        response = self.manager_client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.cashier_client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_draft_inventory_count(self):
        """Admin can delete a draft inventory count"""
        count_id = self.draft_count.id
        url = reverse("warehouses:inventory-count-detail", args=[count_id])
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(InventoryCount.objects.filter(id=count_id).exists())

    def test_delete_draft_inventory_count_unauthorized(self):
        """Admin can delete a draft inventory count"""
        count_id = self.draft_count.id
        url = reverse("warehouses:inventory-count-detail", args=[count_id])
        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_non_draft_inventory_count(self):
        """Cannot delete non-draft inventory counts"""
        url = reverse(
            "warehouses:inventory-count-detail", args=[self.completed_count.id]
        )
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertTrue(
            InventoryCount.objects.filter(id=self.completed_count.id).exists()
        )

    def test_cancel_draft_inventory_count(self):
        """Admin can cancel an in-progress inventory count"""
        url = reverse("warehouses:inventory-count-cancel", args=[self.draft_count.id])
        self.assertEqual(InventoryCount.objects.all().count(), 4)
        response = self.admin_client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.draft_count.refresh_from_db()
        self.assertEqual(self.draft_count.status, InventoryCount.Status.CANCELLED)
        self.assertIsNotNone(self.draft_count.ended_at)
        # Create New Inventory Count
        self.assertEqual(InventoryCount.objects.all().count(), 5)
        self.assertEqual(
            InventoryCount.objects.filter(warehouse=self.draft_count.warehouse)
            .order_by("created")
            .last()
            .status,
            InventoryCount.Status.DRAFT,
        )

    def test_cancel_inventory_count_unauthorized(self):
        url = reverse("warehouses:inventory-count-cancel", args=[self.draft_count.id])
        response = self.cashier_client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.manager_client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_cancel_completed_inventory_count(self):
        url = reverse(
            "warehouses:inventory-count-cancel", args=[self.completed_count.id]
        )
        response = self.admin_client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            "Only in-progress or draft inventory counts can be cancelled",
        )

    def test_filter_by_status(self):
        """Test filtering inventory counts by status"""
        url = f"{self.url}?status={InventoryCount.Status.COMPLETED}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.completed_count.id)

    def test_filter_by_warehouse(self):
        """Test filtering inventory counts by warehouse"""
        url = f"{self.url}?warehouse={self.warehouse2.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data["results"]), 1
        )  # All counts are for this warehouse
