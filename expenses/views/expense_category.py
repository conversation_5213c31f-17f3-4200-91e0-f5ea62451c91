from rest_framework import status, viewsets
from rest_framework.response import Response

from expenses.models.expense_category import ExpenseCategory
from expenses.serializers.expense_category import ExpenseCategorySerializer
from utils.permissions import IsAdminOnly, IsAdminOrCashierOrManager


class ExpenseCategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing expense categories.
    Only accessible by admin and manager users.
    """

    queryset = ExpenseCategory.objects.all()
    serializer_class = ExpenseCategorySerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["list", "retrieve"]:
            permission_classes = [IsAdminOrCashierOrManager]
        else:
            permission_classes = [IsAdminOnly]
        return [permission() for permission in permission_classes]

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.expenses.exists():
            return Response(
                {"detail": "Cannot delete category that is in use by expenses."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return super().destroy(instance)
