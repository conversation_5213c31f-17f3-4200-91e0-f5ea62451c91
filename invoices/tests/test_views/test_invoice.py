from decimal import Decimal

from django.db.models.aggregates import Sum
from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from invoices.models import Invoice
from utils.test.base_test import BaseTestCase
from utils.test.factories.invoice.invoice import InvoiceFactory
from utils.test.factories.invoice.invoice_items import InvoiceItemFactory
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.product.product import ProductFactory
from utils.test.factories.warehouse.stock_item import StockItemFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class InvoiceViewSetTestCase(BaseTestCase):
    """
    Test cases for InvoiceViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("invoices:invoice-list")
        self.detail_url_name = "invoices:invoice-detail"

        # Create additional test data
        self.test_warehouse = WarehouseFactory.create(name="Test Warehouse 2")
        self.test_pos_session = POSSessionFactory.create()
        self.test_pos_session2 = POSSessionFactory.create()

        # Create test products with stock
        self.product1 = ProductFactory.create(
            name="Test Product 1", cost=Decimal("10.00"), price=Decimal("15.00")
        )
        self.product2 = ProductFactory.create(
            name="Test Product 2", cost=Decimal("20.00"), price=Decimal("30.00")
        )

        # Create stock items for products in the POS session warehouse
        self.stock1 = StockItemFactory.create(
            warehouse=self.test_pos_session.pos.warehouse,
            product=self.product1,
            quantity=100,
        )
        self.stock2 = StockItemFactory.create(
            warehouse=self.test_pos_session.pos.warehouse,
            product=self.product2,
            quantity=50,
        )

        # Create test invoices
        self.invoice1 = InvoiceFactory.create(
            warehouse=self.test_pos_session.pos.warehouse,
            pos_session=self.test_pos_session,
            net_amount=Decimal("100.00"),
            profit=Decimal("20.00"),
            payment_method="cash",
            notes="Test invoice 1",
        )

        self.invoice2 = InvoiceFactory.create(
            warehouse=self.test_pos_session.pos.warehouse,
            pos_session=self.test_pos_session2,
            net_amount=Decimal("200.00"),
            profit=Decimal("40.00"),
            payment_method="cash",
            notes="Test invoice 2",
        )

        # Create invoice items (profit and total_price will be calculated automatically)
        self.item1 = InvoiceItemFactory.create(
            invoice=self.invoice1,
            product=self.product1,
            quantity=Decimal("2.00"),
            unit_price=Decimal("15.00"),
        )

        self.item2 = InvoiceItemFactory.create(
            invoice=self.invoice1,
            product=self.product2,
            quantity=Decimal("1.00"),
            unit_price=Decimal("30.00"),
        )

    def get_detail_url(self, invoice_id):
        """Helper method to get detail URL for an invoice."""
        return reverse(self.detail_url_name, kwargs={"pk": invoice_id})

    # Authentication and Permission Tests
    def test_list_invoices_unauthenticated(self):
        """Test that unauthenticated users cannot list invoices."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_invoices_as_admin(self):
        """Admin should see all invoices."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test invoices
        self.assertEqual(len(response.data["results"]), 2)

    def test_list_invoices_as_manager(self):
        """Manager should only see invoices from their POS sessions."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_invoices_as_cashier(self):
        """Cashier should only see invoices from their POS sessions."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_invoice_as_admin(self):
        """Admin should be able to retrieve any invoice."""
        url = self.get_detail_url(self.invoice1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data (should use InvoiceReadSerializer)
        self.assertEqual(response.data["id"], self.invoice1.id)
        self.assertEqual(response.data["net_amount"], "100.00")
        self.assertEqual(response.data["profit"], "20.00")
        self.assertEqual(response.data["payment_method"], "cash")
        self.assertEqual(response.data["notes"], "Test invoice 1")
        self.assertIn("warehouse", response.data)
        self.assertIn("pos_session", response.data)
        self.assertIn("items", response.data)
        self.assertEqual(len(response.data["items"]), 2)

    def test_retrieve_invoice_as_manager(self):
        """Manager should not be able to retrieve invoices (admin only for retrieve)."""
        url = self.get_detail_url(self.invoice1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_invoice_as_cashier(self):
        """Cashier should not be able to retrieve invoices (admin only for retrieve)."""
        url = self.get_detail_url(self.invoice1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_invoice(self):
        """Test retrieving a non-existent invoice."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_invoice_as_admin(self):
        """Admin should be able to create invoices."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "notes": "New test invoice",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "15.00",
                    "total_price": "45.00",
                    "notes": "Item 1",
                },
                {
                    "product": self.product2.id,
                    "quantity": "2.00",
                    "unit_price": "30.00",
                    "total_price": "60.00",
                    "notes": "Item 2",
                },
            ],
        }

        initial_count = Invoice.objects.count()
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that invoice was created
        self.assertEqual(Invoice.objects.count(), initial_count + 1)

        # Check response data
        invoice = Invoice.objects.get(id=response.data["id"])
        self.assertEqual(invoice.pos_session, self.test_pos_session)
        self.assertEqual(invoice.warehouse, self.test_pos_session.pos.warehouse)
        self.assertEqual(invoice.payment_method, "cash")
        self.assertEqual(invoice.notes, "New test invoice")

        # Check that items were created
        self.assertEqual(invoice.items.count(), 2)

        # Check that net_amount and profit were calculated
        self.assertIsNotNone(invoice.net_amount)

        net_amount = invoice.items.aggregate(total_amount=Sum("total_price"))[
            "total_amount"
        ]
        self.assertEqual(invoice.net_amount, net_amount)

        product1_profit = (Decimal("15.00") - self.product1.cost) * Decimal("3.00")
        product2_profit = (Decimal("30.00") - self.product2.cost) * Decimal("2.00")

        profit = invoice.items.aggregate(total_profit=Sum("profit"))["total_profit"]
        self.assertEqual(invoice.profit, profit)
        self.assertEqual(invoice.profit, product1_profit + product2_profit)

        self.assertIsNotNone(invoice.profit)

        # Check that stock was decreased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 - Decimal("3.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 - Decimal("2.000"))

    def test_create_invoice_as_manager(self):
        """Manager should be able to create invoices."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "notes": "New test invoice",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "15.00",
                    "total_price": "45.00",
                    "notes": "Item 1",
                },
                {
                    "product": self.product2.id,
                    "quantity": "2.00",
                    "unit_price": "30.00",
                    "total_price": "60.00",
                    "notes": "Item 2",
                },
            ],
        }

        initial_count = Invoice.objects.count()
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that invoice was created
        self.assertEqual(Invoice.objects.count(), initial_count + 1)

        # Check response data
        invoice = Invoice.objects.get(id=response.data["id"])
        self.assertEqual(invoice.pos_session, self.test_pos_session)
        self.assertEqual(invoice.warehouse, self.test_pos_session.pos.warehouse)
        self.assertEqual(invoice.payment_method, "cash")
        self.assertEqual(invoice.notes, "New test invoice")

        # Check that items were created
        self.assertEqual(invoice.items.count(), 2)

        # Check that net_amount and profit were calculated
        self.assertIsNotNone(invoice.net_amount)

        net_amount = invoice.items.aggregate(total_amount=Sum("total_price"))[
            "total_amount"
        ]
        self.assertEqual(invoice.net_amount, net_amount)

        product1_profit = (Decimal("15.00") - self.product1.cost) * Decimal("3.00")
        product2_profit = (Decimal("30.00") - self.product2.cost) * Decimal("2.00")

        profit = invoice.items.aggregate(total_profit=Sum("profit"))["total_profit"]
        self.assertEqual(invoice.profit, profit)
        self.assertEqual(invoice.profit, product1_profit + product2_profit)

        self.assertIsNotNone(invoice.profit)

        # Check that stock was decreased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 - Decimal("3.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 - Decimal("2.000"))

    def test_create_invoice_as_cashier(self):
        """Cashier should be able to create invoices."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "notes": "New test invoice",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "15.00",
                    "total_price": "45.00",
                    "notes": "Item 1",
                },
                {
                    "product": self.product2.id,
                    "quantity": "2.00",
                    "unit_price": "30.00",
                    "total_price": "60.00",
                    "notes": "Item 2",
                },
            ],
        }

        initial_count = Invoice.objects.count()
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that invoice was created
        self.assertEqual(Invoice.objects.count(), initial_count + 1)

        # Check response data
        invoice = Invoice.objects.get(id=response.data["id"])
        self.assertEqual(invoice.pos_session, self.test_pos_session)
        self.assertEqual(invoice.warehouse, self.test_pos_session.pos.warehouse)
        self.assertEqual(invoice.payment_method, "cash")
        self.assertEqual(invoice.notes, "New test invoice")

        # Check that items were created
        self.assertEqual(invoice.items.count(), 2)

        # Check that net_amount and profit were calculated
        self.assertIsNotNone(invoice.net_amount)

        net_amount = invoice.items.aggregate(total_amount=Sum("total_price"))[
            "total_amount"
        ]
        self.assertEqual(invoice.net_amount, net_amount)

        product1_profit = (Decimal("15.00") - self.product1.cost) * Decimal("3.00")
        product2_profit = (Decimal("30.00") - self.product2.cost) * Decimal("2.00")

        profit = invoice.items.aggregate(total_profit=Sum("profit"))["total_profit"]
        self.assertEqual(invoice.profit, profit)
        self.assertEqual(invoice.profit, product1_profit + product2_profit)

        self.assertIsNotNone(invoice.profit)

        # Check that stock was decreased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 - Decimal("3.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 - Decimal("2.000"))

    def test_create_invoice_unauthenticated(self):
        """Unauthenticated users should not be able to create invoices."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "notes": "Unauthorized invoice",
        }

        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_invoice_without_items(self):
        """Test creating invoice without items."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "notes": "Invoice without items",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"items": [ErrorDetail(string="This field is required.", code="required")]},
        )

    def test_create_invoice_invalid_unit_price(self):
        """Test creating invoice with unit price less than product cost."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "5.00",  # Less than product cost (10.00)
                    "total_price": "5.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["items"][0]["unit_price"],
            [
                ErrorDetail(
                    string="Unit price ($5.00) cannot be less than product cost ($10.00)",
                    code="invalid",
                )
            ],
        )

    # Update Tests
    def test_update_invoice_as_admin(self):
        """Admin should be able to update invoices."""
        url = self.get_detail_url(self.invoice1.id)

        # Store original stock quantities
        original_stock1 = self.stock1.quantity
        original_stock2 = self.stock2.quantity

        data = {
            "pos_session": self.invoice1.pos_session.id,
            "payment_method": "cash",
            "notes": "Updated invoice notes",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "5.00",  # Different quantity
                    "unit_price": "15.00",
                    "total_price": "75.00",
                }
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that invoice was updated
        self.invoice1.refresh_from_db()
        self.assertEqual(self.invoice1.notes, "Updated invoice notes")

        # Check that old items were deleted and new ones created
        self.assertEqual(self.invoice1.items.count(), 1)
        item = self.invoice1.items.first()
        self.assertEqual(item.product, self.product1)
        self.assertEqual(item.quantity, Decimal("5.00"))
        self.assertEqual(item.unit_price, Decimal("15.00"))
        self.assertEqual(item.total_price, Decimal("75.00"))

        # Check that stock was updated correctly
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(
            self.stock1.quantity, original_stock1 + Decimal("2.000") - Decimal("5.000")
        )
        self.assertEqual(self.stock2.quantity, original_stock2 + Decimal("1.000"))

    def test_update_invoice_as_manager(self):
        """Manager should not be able to update invoices (admin only)."""
        url = self.get_detail_url(self.invoice1.id)
        data = {"notes": "Manager update"}

        response = self.manager_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_invoice_as_cashier(self):
        """Cashier should not be able to update invoices (admin only)."""
        url = self.get_detail_url(self.invoice1.id)
        data = {"notes": "Cashier update"}

        response = self.cashier_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partial_update_invoice_as_admin(self):
        """Admin should be able to partially update invoices."""
        url = self.get_detail_url(self.invoice1.id)
        data = {"notes": "Partially updated notes"}

        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only notes were updated
        self.invoice1.refresh_from_db()
        self.assertEqual(self.invoice1.notes, "Partially updated notes")

    # Delete Tests
    def test_delete_invoice_as_admin(self):
        """Admin should be able to delete invoices."""
        url = self.get_detail_url(self.invoice2.id)
        initial_count = Invoice.objects.count()

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    # Filtering Tests
    def test_filter_invoices_by_warehouse(self):
        """Test filtering invoices by warehouse."""
        url = f"{self.list_url}?warehouse={self.warehouse.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only invoices from the specified warehouse
        for invoice in response.data["results"]:
            self.assertEqual(invoice["warehouse"]["id"], self.warehouse.id)

    def test_filter_invoices_by_pos_session(self):
        """Test filtering invoices by POS session."""
        url = f"{self.list_url}?pos_session={self.test_pos_session.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only invoices from the specified POS session
        for invoice in response.data["results"]:
            self.assertEqual(invoice["pos_session"]["id"], self.test_pos_session.id)

    def test_filter_invoices_by_payment_method(self):
        """Test filtering invoices by payment method."""
        url = f"{self.list_url}?payment_method=cash"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only cash invoices
        for invoice in response.data["results"]:
            self.assertEqual(invoice["payment_method"], "cash")

    def test_filter_invoices_nonexistent_warehouse(self):
        """Test filtering by non-existent warehouse."""
        url = f"{self.list_url}?warehouse=99999"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Search Tests
    def test_search_invoices_by_notes(self):
        """Test searching invoices by notes."""
        url = f"{self.list_url}?search=Test invoice 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return invoices with matching notes
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_invoices_case_insensitive(self):
        """Test that search is case insensitive."""
        url = f"{self.list_url}?search=test invoice"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return invoices even with lowercase search
        self.assertGreater(len(response.data["results"]), 0)

    def test_search_invoices_no_results(self):
        """Test searching with term that has no results."""
        url = f"{self.list_url}?search=NonexistentInvoice"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return no results
        self.assertEqual(len(response.data["results"]), 0)

    # Ordering Tests
    def test_default_ordering_by_created(self):
        """Test that invoices are ordered by created date by default (descending)."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that invoices are ordered by created date (descending)
        created_dates = [inv["created"] for inv in response.data["results"]]
        self.assertEqual(created_dates, sorted(created_dates, reverse=True))

    def test_ordering_by_net_amount_ascending(self):
        """Test ordering invoices by net amount (ascending)."""
        url = f"{self.list_url}?ordering=net_amount"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that invoices are ordered by net amount (ascending)
        amounts = [
            Decimal(inv["net_amount"]) if inv["net_amount"] else Decimal("0")
            for inv in response.data["results"]
        ]
        self.assertEqual(amounts, sorted(amounts))

    def test_ordering_by_profit_descending(self):
        """Test ordering invoices by profit (descending)."""
        url = f"{self.list_url}?ordering=-profit"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that invoices are ordered by profit (descending)
        profits = [
            Decimal(inv["profit"]) if inv["profit"] else Decimal("0")
            for inv in response.data["results"]
        ]
        self.assertEqual(profits, sorted(profits, reverse=True))

    # Combined Tests
    def test_filter_search_and_ordering_combined(self):
        """Test combining filtering, searching, and ordering."""
        url = f"{self.list_url}?warehouse={self.warehouse.id}&search=Test&ordering=-net_amount"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return filtered and searched results in descending net_amount order
        if response.data["results"]:
            amounts = [
                Decimal(inv["net_amount"]) if inv["net_amount"] else Decimal("0")
                for inv in response.data["results"]
            ]
            self.assertEqual(amounts, sorted(amounts, reverse=True))

    # Profit and Net Amount Calculation Tests
    def test_profit_calculation_on_create(self):
        """Test that profit is calculated correctly when creating invoice."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,  # cost: 10.00
                    "quantity": "2.00",
                    "unit_price": "15.00",  # profit per unit: 5.00
                    "total_price": "30.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that profit was calculated correctly
        invoice = Invoice.objects.get(id=response.data["id"])
        expected_profit = Decimal("2.00") * (
            Decimal("15.00") - Decimal("10.00")
        )  # 2 * 5 = 10
        self.assertEqual(invoice.profit, expected_profit)

    def test_net_amount_calculation_on_create(self):
        """Test that net amount is calculated correctly when creating invoice."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "2.00",
                    "unit_price": "15.00",
                    "total_price": "30.00",
                },
                {
                    "product": self.product2.id,
                    "quantity": "1.00",
                    "unit_price": "30.00",
                    "total_price": "30.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that net amount was calculated correctly
        invoice = Invoice.objects.get(id=response.data["id"])
        expected_net_amount = Decimal("30.00") + Decimal("30.00")  # 60.00
        self.assertEqual(invoice.net_amount, expected_net_amount)

    # Invoice Items Tests
    def test_invoice_items_creation(self):
        """Test that invoice items are created correctly with nested serializer."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "15.00",
                    "total_price": "45.00",
                    "notes": "Special item note",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that item was created with correct values
        invoice = Invoice.objects.get(id=response.data["id"])
        item = invoice.items.first()
        self.assertEqual(item.product, self.product1)
        self.assertEqual(item.quantity, Decimal("3.00"))
        self.assertEqual(item.unit_price, Decimal("15.00"))
        self.assertEqual(item.total_price, Decimal("45.00"))
        self.assertEqual(item.notes, "Special item note")

    def test_invoice_items_profit_calculation(self):
        """Test that invoice item profit is calculated correctly."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,  # cost: 10.00
                    "quantity": "2.00",
                    "unit_price": "15.00",  # profit per unit: 5.00
                    "total_price": "30.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that item profit was calculated correctly
        invoice = Invoice.objects.get(id=response.data["id"])
        item = invoice.items.first()
        expected_profit = Decimal("2.00") * (
            Decimal("15.00") - Decimal("10.00")
        )  # 2 * 5 = 10
        self.assertEqual(item.profit, expected_profit)

    # Validation Tests
    def test_create_invoice_missing_required_fields(self):
        """Test creating invoice with missing required fields."""
        data = {
            "payment_method": "cash"
            # Missing pos_session
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_invoice_invalid_payment_method(self):
        """Test creating invoice with invalid payment method."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "invalid_method",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_invoice_invalid_pos_session(self):
        """Test creating invoice with non-existent POS session."""
        data = {"pos_session": 99999, "payment_method": "cash"}

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Serializer Tests
    def test_list_uses_read_serializer(self):
        """Test that list action uses InvoiceReadSerializer."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Read serializer should have expanded fields
        if response.data["results"]:
            invoice_data = response.data["results"][0]
            expected_fields = {
                "id",
                "warehouse",
                "net_amount",
                "profit",
                "pos_session",
                "payment_method",
                "notes",
                "items",
                "created",
                "modified",
            }
            self.assertEqual(set(invoice_data.keys()), expected_fields)

    def test_retrieve_uses_read_serializer(self):
        """Test that retrieve action uses InvoiceReadSerializer."""
        url = self.get_detail_url(self.invoice1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Read serializer should have expanded fields
        expected_fields = {
            "id",
            "warehouse",
            "net_amount",
            "profit",
            "pos_session",
            "payment_method",
            "notes",
            "items",
            "created",
            "modified",
        }
        self.assertEqual(set(response.data.keys()), expected_fields)

    # Model String Representation Tests
    def test_invoice_string_representation(self):
        """Test the string representation of Invoice model."""
        expected_str = f"{self.invoice1.id} - {self.invoice1.net_amount}"
        self.assertEqual(str(self.invoice1), expected_str)

    def test_invoice_item_string_representation(self):
        """Test the string representation of InvoiceItem model."""
        expected_str = f"{self.item1.quantity} x {self.item1.product.name} - {self.item1.total_price}"
        self.assertEqual(str(self.item1), expected_str)

    # Edge Cases
    def test_invoice_with_zero_quantity_item(self):
        """Test creating invoice with zero quantity item."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "0.00",
                    "unit_price": "15.00",
                    "total_price": "0.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "quantity": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ],
                        "total_price": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ],
                    }
                ]
            },
        )

    def test_invoice_with_negative_quantity_item(self):
        """Test creating invoice with negative quantity item."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "-1.00",
                    "unit_price": "15.00",
                    "total_price": "-15.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        # This should likely be rejected
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "quantity": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ],
                        "total_price": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ],
                    }
                ]
            },
        )

    def test_precision_handling(self):
        """Test that decimal precision is handled correctly."""
        data = {
            "pos_session": self.test_pos_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product": self.product1.id,
                    "quantity": "1.12",  # 2 decimal places
                    "unit_price": "15.99",  # 2 decimal places
                    "total_price": "17.91",  # 1.12 * 15.99 rounded
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that values are handled correctly
        invoice = Invoice.objects.get(id=response.data["id"])
        item = invoice.items.first()
        self.assertEqual(item.quantity, Decimal("1.12"))
        self.assertEqual(item.unit_price, Decimal("15.99"))
        self.assertEqual(item.total_price, Decimal("17.91"))
