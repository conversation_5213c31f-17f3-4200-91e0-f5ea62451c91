import factory
from django.contrib.contenttypes.models import ContentType
from factory.django import DjangoModelFactory

from accounts.models import Account
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class AccountFactory(DjangoModelFactory):
    """
    Factory for creating Account instances for testing.
    """

    class Meta:
        model = Account
        skip_postgeneration_save = True

    # Default to Warehouse content type
    content_type = factory.LazyAttribute(
        lambda _: ContentType.objects.get_for_model(WarehouseFactory._meta.model)
    )

    # Create a warehouse and use its ID
    object_id = factory.LazyAttribute(lambda obj: WarehouseFactory.create().id)

    @classmethod
    def create_for_object(cls, content_object, **kwargs):
        """
        Create an account for a specific object.

        Usage:
            warehouse = WarehouseFactory.create()
            account = AccountFactory.create_for_object(warehouse)
        """
        content_type = ContentType.objects.get_for_model(content_object)
        return cls.create(
            content_type=content_type, object_id=content_object.id, **kwargs
        )
