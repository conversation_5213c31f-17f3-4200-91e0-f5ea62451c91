from django.contrib import admin

from .models import Account, AccountTransaction, Balance


class TransactionInline(admin.TabularInline):
    model = AccountTransaction
    extra = 0
    readonly_fields = ("created",)
    fields = ("type", "amount", "description", "created")
    ordering = ("-created",)


class BalanceInline(admin.TabularInline):
    model = Balance
    extra = 0
    readonly_fields = ("created", "modified")
    fields = ("pos", "amount", "notes", "created")
    ordering = ("-created",)


class BalanceAdmin(admin.ModelAdmin):
    list_display = ("id", "account", "pos", "amount", "created", "modified")
    list_filter = ("pos", "created", "modified")
    search_fields = ("account__name", "pos__name", "notes")
    readonly_fields = ("created", "modified")
    list_select_related = ("account", "pos")
    date_hierarchy = "created"
    inlines = [TransactionInline]

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("transactions")


class AccountAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "account_type", "balance", "created", "modified")
    list_filter = ("account_type", "content_type", "created", "modified")
    search_fields = ("id", "name", "account_type", "object_id")
    readonly_fields = ("created", "modified")
    list_select_related = ("content_type",)
    date_hierarchy = "created"
    inlines = [BalanceInline]

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("balances")


class AccountTransactionAdmin(admin.ModelAdmin):
    list_display = ("id", "balance", "type", "amount", "description", "created")
    list_filter = ("type", "created")
    search_fields = ("description", "balance__account__name", "balance__pos__name")
    readonly_fields = ("created",)
    list_select_related = ("balance", "balance__account", "balance__pos")
    date_hierarchy = "created"


admin.site.register(Account, AccountAdmin)
admin.site.register(Balance, BalanceAdmin)
admin.site.register(AccountTransaction, AccountTransactionAdmin)
