import { renderHook, act } from '@testing-library/react';
import { useProducts, useProduct } from '../useProducts';
import { productsService } from '../../services/productsService';

// Mock the products service
jest.mock('../../services/productsService');

describe('useProducts', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('fetchProducts', () => {
        test('fetches products successfully', async () => {
            const mockResponse = {
                data: {
                    results: [
                        { id: 1, name: 'Product 1' },
                        { id: 2, name: 'Product 2' }
                    ],
                    count: 2,
                    next: null,
                    previous: null
                }
            };

            productsService.getProducts.mockResolvedValue(mockResponse);

            const { result } = renderHook(() => useProducts());

            await act(async () => {
                await result.current.fetchProducts({ page: 1, page_size: 10 });
            });

            expect(result.current.products).toEqual(mockResponse.data.results);
            expect(result.current.pagination).toEqual({
                count: 2,
                next: null,
                previous: null,
                page: 1,
                page_size: 10
            });
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBe(null);
        });

        test('handles fetch products error', async () => {
            const errorMessage = 'Failed to fetch products';
            productsService.getProducts.mockRejectedValue({
                response: { data: { detail: errorMessage } }
            });

            const { result } = renderHook(() => useProducts());

            await act(async () => {
                await result.current.fetchProducts();
            });

            expect(result.current.products).toEqual([]);
            expect(result.current.error).toBe(errorMessage);
            expect(result.current.loading).toBe(false);
        });
    });

    describe('createProduct', () => {
        test('creates product successfully', async () => {
            const mockProduct = { id: 1, name: 'New Product' };
            const mockResponse = { data: mockProduct };

            productsService.createProduct.mockResolvedValue(mockResponse);

            const { result } = renderHook(() => useProducts());

            let createdProduct;
            await act(async () => {
                createdProduct = await result.current.createProduct({
                    name: 'New Product',
                    cost: 10,
                    price: 15
                });
            });

            expect(createdProduct).toEqual(mockProduct);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBe(null);
        });

        test('handles create product error', async () => {
            const errorMessage = 'Failed to create product';
            productsService.createProduct.mockRejectedValue({
                response: { data: { detail: errorMessage } }
            });

            const { result } = renderHook(() => useProducts());

            await act(async () => {
                try {
                    await result.current.createProduct({ name: 'New Product' });
                } catch (error) {
                    // Expected to throw
                }
            });

            expect(result.current.error).toBe(errorMessage);
            expect(result.current.loading).toBe(false);
        });
    });

    describe('updateProduct', () => {
        test('updates product successfully', async () => {
            const mockProduct = { id: 1, name: 'Updated Product' };
            const mockResponse = { data: mockProduct };

            productsService.updateProduct.mockResolvedValue(mockResponse);

            const { result } = renderHook(() => useProducts());

            // Set initial products
            act(() => {
                result.current.products = [
                    { id: 1, name: 'Original Product' },
                    { id: 2, name: 'Other Product' }
                ];
            });

            let updatedProduct;
            await act(async () => {
                updatedProduct = await result.current.updateProduct(1, {
                    name: 'Updated Product'
                });
            });

            expect(updatedProduct).toEqual(mockProduct);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBe(null);
        });

        test('handles update product error', async () => {
            const errorMessage = 'Failed to update product';
            productsService.updateProduct.mockRejectedValue({
                response: { data: { detail: errorMessage } }
            });

            const { result } = renderHook(() => useProducts());

            await act(async () => {
                try {
                    await result.current.updateProduct(1, { name: 'Updated Product' });
                } catch (error) {
                    // Expected to throw
                }
            });

            expect(result.current.error).toBe(errorMessage);
            expect(result.current.loading).toBe(false);
        });
    });

    describe('deleteProduct', () => {
        test('deletes product successfully', async () => {
            productsService.deleteProduct.mockResolvedValue({});

            const { result } = renderHook(() => useProducts());

            // Set initial products
            act(() => {
                result.current.products = [
                    { id: 1, name: 'Product 1' },
                    { id: 2, name: 'Product 2' }
                ];
            });

            let deleteResult;
            await act(async () => {
                deleteResult = await result.current.deleteProduct(1);
            });

            expect(deleteResult).toBe(true);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBe(null);
        });

        test('handles delete product error', async () => {
            const errorMessage = 'Failed to delete product';
            productsService.deleteProduct.mockRejectedValue({
                response: { data: { detail: errorMessage } }
            });

            const { result } = renderHook(() => useProducts());

            await act(async () => {
                try {
                    await result.current.deleteProduct(1);
                } catch (error) {
                    // Expected to throw
                }
            });

            expect(result.current.error).toBe(errorMessage);
            expect(result.current.loading).toBe(false);
        });
    });
});

describe('useProduct', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('fetches single product successfully', async () => {
        const mockProduct = { id: 1, name: 'Test Product' };
        const mockResponse = { data: mockProduct };

        productsService.getProduct.mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useProduct(1));

        // Wait for the effect to run
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(result.current.product).toEqual(mockProduct);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe(null);
    });

    test('handles fetch single product error', async () => {
        const errorMessage = 'Product not found';
        productsService.getProduct.mockRejectedValue({
            response: { data: { detail: errorMessage } }
        });

        const { result } = renderHook(() => useProduct(1));

        // Wait for the effect to run
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(result.current.product).toBe(null);
        expect(result.current.error).toBe(errorMessage);
        expect(result.current.loading).toBe(false);
    });

    test('does not fetch when id is not provided', () => {
        const { result } = renderHook(() => useProduct(null));

        expect(productsService.getProduct).not.toHaveBeenCalled();
        expect(result.current.product).toBe(null);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe(null);
    });

    test('refetch function works correctly', async () => {
        const mockProduct = { id: 1, name: 'Test Product' };
        const mockResponse = { data: mockProduct };

        productsService.getProduct.mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useProduct(1));

        // Wait for initial fetch
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        // Clear the mock and call refetch
        productsService.getProduct.mockClear();
        
        await act(async () => {
            await result.current.refetch();
        });

        expect(productsService.getProduct).toHaveBeenCalledWith(1);
    });
});
