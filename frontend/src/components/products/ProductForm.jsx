import React, { useState, useEffect } from 'react';
import { 
    <PERSON>, 
    But<PERSON>, 
    Card, 
    Alert, 
    Spinner, 
    Row, 
    Col,
    InputGroup 
} from 'react-bootstrap';
import { useParentCategories } from '../../hooks/useCategories';
import { useUnitTypes } from '../../hooks/useProducts';

const ProductForm = ({ 
    initialData = {}, 
    onSubmit, 
    loading = false, 
    error = null,
    isEdit = false 
}) => {
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        cost: '',
        price: '',
        barcode: '',
        unit_type: 'piece',
        category_id: '',
        image: null
    });
    const [validationErrors, setValidationErrors] = useState({});
    const [imagePreview, setImagePreview] = useState(null);
    const { parentCategories, loading: categoriesLoading } = useParentCategories();
    const unitTypes = useUnitTypes();

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                description: initialData.description || '',
                cost: initialData.cost || '',
                price: initialData.price || '',
                barcode: initialData.barcode || '',
                unit_type: initialData.unit_type || 'piece',
                category_id: initialData.category_id || initialData.category?.id || '',
                image: null // Don't pre-fill image for security reasons
            });

            // Set image preview if editing and image exist
            if (isEdit && initialData.image) {
                setImagePreview(initialData.image);
            }
        }
    }, []);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value, type, files } = e.target;
        
        if (type === 'file') {
            const file = files[0];
            setFormData(prev => ({
                ...prev,
                [name]: file
            }));
            
            // Create image preview
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => setImagePreview(e.target.result);
                reader.readAsDataURL(file);
            } else {
                setImagePreview(null);
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        // Name validation
        if (!formData.name.trim()) {
            errors.name = 'Product name is required';
        } else if (formData.name.trim().length < 2) {
            errors.name = 'Product name must be at least 2 characters';
        } else if (formData.name.trim().length > 200) {
            errors.name = 'Product name must be less than 200 characters';
        }

        // Cost validation
        if (!formData.cost) {
            errors.cost = 'Cost price is required';
        } else if (isNaN(formData.cost) || parseFloat(formData.cost) < 0) {
            errors.cost = 'Cost price must be a valid positive number';
        }

        // Price validation
        if (!formData.price) {
            errors.price = 'Selling price is required';
        } else if (isNaN(formData.price) || parseFloat(formData.price) < 0) {
            errors.price = 'Selling price must be a valid positive number';
        } else if (parseFloat(formData.price) < parseFloat(formData.cost)) {
            errors.price = 'Selling price cannot be less than cost price';
        }

        // Description validation (optional but with length limit)
        if (formData.description && formData.description.length > 1000) {
            errors.description = 'Description must be less than 1000 characters';
        }

        // Barcode validation (optional but must be unique if provided)
        if (formData.barcode && formData.barcode.length > 100) {
            errors.barcode = 'Barcode must be less than 100 characters';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            cost: parseFloat(formData.cost),
            price: parseFloat(formData.price),
            barcode: formData.barcode.trim() || null,
            unit_type: formData.unit_type,
            category_id: formData.category_id ? parseInt(formData.category_id) : null,
        };

        // Add image if provided
        if (formData.image) {
            submitData.image = formData.image;
        }

        onSubmit(submitData);
    };

    // Calculate profit margin
    const calculateProfitMargin = () => {
        const cost = parseFloat(formData.cost) || 0;
        const price = parseFloat(formData.price) || 0;
        if (cost === 0) return 0;
        return ((price - cost) / cost * 100).toFixed(2);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Product' : 'Create New Product'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={8}>
                            {/* Product Name */}
                            <Form.Group className="mb-3">
                                <Form.Label>Product Name *</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.name}
                                    placeholder="Enter product name"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.name}
                                </Form.Control.Feedback>
                            </Form.Group>

                            {/* Description */}
                            <Form.Group className="mb-3">
                                <Form.Label>Description</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    name="description"
                                    value={formData.description}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.description}
                                    placeholder="Enter product description (optional)"
                                />
                                <Form.Text className="text-muted">
                                    {formData.description.length}/1000 characters
                                </Form.Text>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.description}
                                </Form.Control.Feedback>
                            </Form.Group>

                            {/* Pricing */}
                            <Row>
                                <Col md={4}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Cost Price *</Form.Label>
                                        <InputGroup>
                                            <InputGroup.Text>$</InputGroup.Text>
                                            <Form.Control
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                name="cost"
                                                value={formData.cost}
                                                onChange={handleChange}
                                                isInvalid={!!validationErrors.cost}
                                                placeholder="0.00"
                                            />
                                        </InputGroup>
                                        <Form.Control.Feedback type="invalid">
                                            {validationErrors.cost}
                                        </Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Selling Price *</Form.Label>
                                        <InputGroup>
                                            <InputGroup.Text>$</InputGroup.Text>
                                            <Form.Control
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                name="price"
                                                value={formData.price}
                                                onChange={handleChange}
                                                isInvalid={!!validationErrors.price}
                                                placeholder="0.00"
                                            />
                                        </InputGroup>
                                        <Form.Control.Feedback type="invalid">
                                            {validationErrors.price}
                                        </Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Profit Margin</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={`${calculateProfitMargin()}%`}
                                            readOnly
                                            className="bg-light"
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>

                            {/* Barcode and Unit Type */}
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Barcode</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="barcode"
                                            value={formData.barcode}
                                            onChange={handleChange}
                                            isInvalid={!!validationErrors.barcode}
                                            placeholder="Enter barcode (optional)"
                                        />
                                        <Form.Control.Feedback type="invalid">
                                            {validationErrors.barcode}
                                        </Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Unit Type *</Form.Label>
                                        <Form.Select
                                            name="unit_type"
                                            value={formData.unit_type}
                                            onChange={handleChange}
                                        >
                                            {unitTypes.map(unit => (
                                                <option key={unit.value} value={unit.value}>
                                                    {unit.label}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                            </Row>

                            {/* Category */}
                            <Form.Group className="mb-3">
                                <Form.Label>Category</Form.Label>
                                <Form.Select
                                    name="category_id"
                                    value={formData.category_id}
                                    onChange={handleChange}
                                    disabled={categoriesLoading}
                                >
                                    <option value="">Select a category (optional)</option>
                                    {parentCategories.map(category => (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    ))}
                                </Form.Select>
                                {categoriesLoading && (
                                    <Form.Text className="text-muted">
                                        Loading categories...
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </Col>

                        <Col md={4}>
                            {/* Product Image */}
                            <Form.Group className="mb-3">
                                <Form.Label>Product Image</Form.Label>
                                <Form.Control
                                    type="file"
                                    name="image"
                                    onChange={handleChange}
                                    accept="image/*"
                                />
                                <Form.Text className="text-muted">
                                    Upload a product image (optional)
                                </Form.Text>
                            </Form.Group>

                            {/* Image Preview */}
                            {imagePreview && (
                                <div className="mb-3">
                                    <Form.Label>Preview</Form.Label>
                                    <div className="border rounded p-2">
                                        <img
                                            src={imagePreview}
                                            alt="Product preview"
                                            className="img-fluid"
                                            style={{ maxHeight: '200px', width: '100%', objectFit: 'contain' }}
                                        />
                                    </div>
                                </div>
                            )}
                        </Col>
                    </Row>

                    <div className="d-flex gap-2">
                        <Button 
                            type="submit" 
                            variant="primary"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Product' : 'Create Product'
                            )}
                        </Button>
                        
                        <Button 
                            type="button" 
                            variant="secondary"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default ProductForm;
