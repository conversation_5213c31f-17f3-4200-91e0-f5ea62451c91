from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON><PERSON>
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers, status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from pos.models import POSSession, POSSessionTransaction
from pos.serializers import POSSessionTransactionSerializer
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly, IsAdminOrCashierOrManager


class POSSessionTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for managing POS session transactions.

    Provides CRUD operations for POSSessionTransaction with proper permissions:
    - Admin: Full access to all transactions
    - Manager/Cashier: Access only to transactions from their own POS sessions
    """

    queryset = POSSessionTransaction.objects.all()
    serializer_class = POSSessionTransactionSerializer
    permission_classes = [IsAdminOrCashierOrManager]

    def get_queryset(self):
        """
        Filter transactions based on user permissions.
        Admin sees all transactions, others see only their own POS transactions.
        """
        session_pk = self.kwargs.get("session_pk")
        queryset = super().get_queryset()
        queryset = queryset.filter(session_id=session_pk)
        if not self.request.user.is_admin:
            # Non-admin users can only see transactions from POS terminals they work at
            # Since Employee has ForeignKey to POS, we need to filter by the employee's POS
            try:
                employee = self.request.user.employee
                if employee.pos:
                    queryset = queryset.filter(session__pos=employee.pos)
                else:
                    # If user has no POS assigned, return empty queryset
                    queryset = queryset.none()
            except AttributeError:
                # If user has no employee record, return empty queryset
                queryset = queryset.none()

        return queryset

    @swagger_auto_schema(
        operation_summary="Retrieve a specific POS session transaction",
        tags=["pos session transactions"],
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
