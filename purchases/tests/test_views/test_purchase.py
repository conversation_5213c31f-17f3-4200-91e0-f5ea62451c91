from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from accounts.models import AccountTransaction
from accounts.models import TransactionType as AccountTransactionType
from pos.models import POSSession, POSSessionTransaction
from pos.models import TransactionType as POSTransactionType
from purchases.models import Purchase
from utils.test.base_test import BaseTestCase
from utils.test.factories.accounts import BalanceFactory
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.product.product import ProductFactory
from utils.test.factories.purchases.purchase import PurchaseFactory
from utils.test.factories.purchases.purchase_item import PurchaseItemFactory
from utils.test.factories.purchases.supplier import SupplierFactory
from utils.test.factories.warehouse.stock_item import StockItemFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class PurchaseViewSetTestCase(BaseTestCase):
    """
    Test cases for PurchaseViewSet.
    """

    @classmethod
    def setUpTestData(cls):
        """Set up test data."""
        super().setUpTestData()

        # URLs
        cls.list_url = reverse("purchases:purchase-list")
        cls.detail_url_name = "purchases:purchase-detail"

        # Create test suppliers
        cls.supplier1 = SupplierFactory.create(name="Supplier 1")
        cls.supplier2 = SupplierFactory.create(name="Supplier 2")

        # Create test POS sessions
        # Use the existing POS from base test setup
        cls.pos_session1 = POSSessionFactory.create(
            pos=cls.pos,  # Use existing POS from BaseTestCase
            status=POSSession.Status.OPEN,
        )
        # Create another warehouse for the second POS session
        cls.warehouse2 = WarehouseFactory.create(name="Test Warehouse 2")
        cls.pos_session2 = POSSessionFactory.create(
            pos__warehouse=cls.warehouse2, status=POSSession.Status.OPEN
        )

        # Create test products
        cls.product1 = ProductFactory.create(
            name="Test Product 1", cost=Decimal("10.00"), price=Decimal("15.00")
        )
        cls.product2 = ProductFactory.create(
            name="Test Product 2", cost=Decimal("20.00"), price=Decimal("30.00")
        )

        # Create stock items for products
        cls.stock1 = StockItemFactory.create(
            warehouse=cls.warehouse, product=cls.product1, quantity=Decimal("50.000")
        )
        cls.stock2 = StockItemFactory.create(
            warehouse=cls.warehouse, product=cls.product2, quantity=Decimal("30.000")
        )

        # Create test purchases
        cls.purchase1 = PurchaseFactory.create(
            warehouse=cls.warehouse,
            supplier=cls.supplier1,
            total_amount=Decimal("500.00"),
            discount=Decimal("50.00"),
            net_amount=Decimal("450.00"),
            paid_amount=Decimal("450.00"),
            reminder_amount=Decimal("0.00"),
            notes="Test purchase 1",
        )

        cls.purchase2 = PurchaseFactory.create(
            warehouse=cls.warehouse,
            supplier=cls.supplier2,
            total_amount=Decimal("300.00"),
            discount=Decimal("0.00"),
            net_amount=Decimal("300.00"),
            paid_amount=Decimal("200.00"),
            reminder_amount=Decimal("100.00"),
            notes="Test purchase 2",
        )

        cls.purchase3 = PurchaseFactory.create(
            warehouse=cls.warehouse2,
            supplier=cls.supplier2,
            total_amount=Decimal("300.00"),
            discount=Decimal("0.00"),
            net_amount=Decimal("300.00"),
            paid_amount=Decimal("200.00"),
            reminder_amount=Decimal("100.00"),
            notes="Test purchase 2",
        )
        cls.pos_transaction1 = POSSessionTransaction.objects.create(
            session=cls.pos_session1,
            transaction_type=POSTransactionType.PURCHASE,
            amount=cls.purchase1.paid_amount,
            related_object=cls.purchase1,
            description=f"Purchase from {cls.purchase1.supplier.name}",
        )
        cls.balance1 = BalanceFactory.create_for_account_and_pos(
            account=cls.supplier1.account, pos=cls.pos_session1.pos
        )
        cls.balance2 = BalanceFactory.create_for_account_and_pos(
            account=cls.supplier2.account, pos=cls.pos_session2.pos
        )
        cls.pos_transaction2 = POSSessionTransaction.objects.create(
            session=cls.pos_session2,
            transaction_type=POSTransactionType.PURCHASE,
            amount=cls.purchase2.paid_amount,
            related_object=cls.purchase2,
            description=f"Purchase from {cls.purchase2.supplier.name}",
        )
        cls.account_transaction2 = AccountTransaction.objects.create(
            balance=cls.balance2,
            type=AccountTransactionType.CREDIT,
            amount=cls.purchase2.reminder_amount,
            related_object=cls.purchase2,
            description=f"Credit purchase #{cls.purchase2.id}",
        )

        # Create purchase items
        cls.item1 = PurchaseItemFactory.create(
            purchase=cls.purchase1,
            product=cls.product1,
            quantity=Decimal("10.00"),
            unit_cost=Decimal("12.00"),
            unit_price=Decimal("15.00"),
        )

        cls.item2 = PurchaseItemFactory.create(
            purchase=cls.purchase1,
            product=cls.product2,
            quantity=Decimal("5.00"),
            unit_cost=Decimal("22.00"),
            unit_price=Decimal("30.00"),
        )

        cls.data = {
            "pos_session_id": cls.pos_session1.id,
            "supplier_id": cls.supplier1.id,
            "total_amount": "126.00",
            "discount": "20.00",
            "paid_amount": "56.00",
            "notes": "New test purchase",
            "items": [
                {
                    "product_id": cls.product1.id,
                    "quantity": "5.00",
                    "unit_cost": "12.00",  # 5 * 12 = 60
                    "unit_price": "15.00",
                    "notes": "Item 1",
                },
                {
                    "product_id": cls.product2.id,
                    "quantity": "3.00",
                    "unit_cost": "22.00",  # 3 * 22 = 66
                    "unit_price": "30.00",
                    "notes": "Item 2",
                },
            ],
        }

        cls.update_date = {
            "pos_session_id": cls.pos_session1.id,
            "supplier_id": cls.supplier1.id,
            "total_amount": "104.00",
            "discount": "0.00",
            "paid_amount": "44.00",
            "notes": "Updated purchase notes",
            "items": [
                {
                    "product_id": cls.product1.id,
                    "quantity": "8.00",
                    "unit_cost": "13.00",  # 8 * 13 = 104
                    "unit_price": "16.00",
                }
            ],
        }

    def get_detail_url(self, purchase_id):
        """Helper method to get detail URL for a purchase."""
        return reverse(self.detail_url_name, kwargs={"pk": purchase_id})

    # Authentication and Permission Tests
    def test_list_purchases_unauthenticated(self):
        """Test that unauthenticated users cannot list purchases."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_purchases_as_admin(self):
        """Admin should see all purchases."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test purchases
        self.assertGreaterEqual(len(response.data["results"]), 3)

    def test_list_purchases_as_manager(self):
        """Manager should see all purchases."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include our test purchases
        self.assertGreaterEqual(len(response.data["results"]), 2)

    def test_list_purchases_as_cashier(self):
        """Cashier should not be able to list purchases."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_purchase_as_admin(self):
        """Admin should be able to retrieve any purchase."""
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.purchase1.id)
        self.assertEqual(response.data["total_amount"], "500.00")
        self.assertEqual(response.data["discount"], "50.00")
        self.assertEqual(response.data["net_amount"], "450.00")
        self.assertEqual(response.data["paid_amount"], "450.00")
        self.assertEqual(response.data["reminder_amount"], "0.00")
        self.assertEqual(response.data["notes"], "Test purchase 1")
        self.assertIn("warehouse_name", response.data)
        self.assertIn("supplier_name", response.data)
        self.assertIn("items", response.data)
        self.assertGreaterEqual(len(response.data["items"]), 2)

    def test_retrieve_purchase_as_manager(self):
        """Manager should be able to retrieve purchases."""
        url = self.get_detail_url(self.purchase1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_purchase_as_cashier(self):
        """Cashier should not be able to retrieve purchases."""
        url = self.get_detail_url(self.purchase1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_purchase(self):
        """Test retrieving a non-existent purchase."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests

    def test_create_purchase_purchase_created(self):
        initial_purchase_count = Purchase.objects.count()
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that purchase was created
        self.assertEqual(Purchase.objects.count(), initial_purchase_count + 1)
        # Check response data
        purchase = Purchase.objects.get(id=response.data["id"])
        self.assertEqual(purchase.supplier, self.supplier1)
        self.assertEqual(purchase.warehouse, self.pos_session1.pos.warehouse)
        self.assertEqual(purchase.paid_amount, Decimal("56.00"))
        self.assertEqual(purchase.discount, Decimal("20.00"))
        self.assertEqual(purchase.reminder_amount, Decimal("50.00"))
        self.assertEqual(purchase.notes, "New test purchase")

    def test_create_purchase_items_created(self):
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that items were created
        purchase = Purchase.objects.get(id=response.data["id"])
        self.assertEqual(purchase.items.count(), 2)
        item1 = purchase.items.get(product=self.product1)
        item2 = purchase.items.get(product=self.product2)
        self.assertEqual(item1.quantity, Decimal("5.00"))
        self.assertEqual(item1.unit_cost, Decimal("12.00"))
        self.assertEqual(item1.unit_price, Decimal("15.00"))
        self.assertEqual(item1.total_cost, Decimal("60.00"))  # 5 * 12
        self.assertEqual(item1.notes, "Item 1")
        self.assertEqual(item2.quantity, Decimal("3.00"))
        self.assertEqual(item2.unit_cost, Decimal("22.00"))
        self.assertEqual(item2.unit_price, Decimal("30.00"))
        self.assertEqual(item2.total_cost, Decimal("66.00"))  # 3 * 22
        self.assertEqual(item2.notes, "Item 2")

    def test_create_purchase_purchase_net_amount(self):
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        purchase = Purchase.objects.get(id=response.data["id"])
        # Check that net_amount was calculated (sum of item total_costs - discount)
        # Item 1: 5 * 12 = 60, Item 2: 3 * 22 = 66, Total = 126, Discount = 20, Net = 106
        expected_net_amount = Decimal("106.00")  # (60 + 66) - 20 = 106
        self.assertEqual(purchase.net_amount, expected_net_amount)

    def test_create_purchase_stock_items_updated(self):
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that stock was increased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 + Decimal("5.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 + Decimal("3.000"))

    def test_create_purchase_pos_session_transaction_created(self):
        initial_pos_transaction_count = POSSessionTransaction.objects.count()
        initial_pos_session_total_expenses = self.pos_session1.total_expenses
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that POS session transaction was created
        purchase = Purchase.objects.get(id=response.data["id"])
        self.assertEqual(
            POSSessionTransaction.objects.count(), initial_pos_transaction_count + 1
        )
        pos_transaction = POSSessionTransaction.objects.filter(
            object_id=purchase.id
        ).first()
        self.assertIsNotNone(pos_transaction)
        self.assertEqual(pos_transaction.amount, purchase.paid_amount)
        self.assertEqual(pos_transaction.transaction_type, POSTransactionType.PURCHASE)
        self.assertEqual(
            pos_transaction.session.total_expenses,
            initial_pos_session_total_expenses + purchase.paid_amount,
        )

    def test_create_purchase_account_transaction_created(self):
        initial_account_balance = self.balance1.amount
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that POS session transaction was created
        purchase = Purchase.objects.get(id=response.data["id"])
        # Check that account balance was updated
        self.balance1.refresh_from_db()
        self.assertEqual(
            self.balance1.amount, initial_account_balance + purchase.reminder_amount
        )
        # Check that account transaction was created
        self.assertTrue(
            AccountTransaction.objects.filter(
                balance=self.balance1,
                related_object_id=purchase.id,
                related_content_type=ContentType.objects.get_for_model(Purchase),
                type=AccountTransactionType.CREDIT,
                amount=purchase.reminder_amount,
            ).exists()
        )
        self.assertEqual(
            self.balance1.amount, initial_account_balance + purchase.reminder_amount
        )

    def test_create_purchase_product_cost_price_updated(self):
        """Admin should be able to create purchases."""
        response = self.admin_client.post(self.list_url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # check porduct cost and price updated
        self.product1.refresh_from_db()
        self.product2.refresh_from_db()
        self.assertEqual(self.product1.cost, Decimal("12.00"))
        self.assertEqual(self.product1.price, Decimal("15.00"))
        self.assertEqual(self.product2.cost, Decimal("22.00"))
        self.assertEqual(self.product2.price, Decimal("30.00"))

    def test_create_purchase_as_manager(self):
        """Manager should be able to create purchases."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "24.00",
            "discount": "0.00",
            "paid_amount": "24.00",
            "notes": "Manager purchase",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_cost": "12.00",  # 2 * 12 = 24
                    "unit_price": "15.00",
                }
            ],
        }

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_purchase_as_cashier(self):
        """Cashier should not be able to create purchases."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
            "paid_amount": "100.00",
        }

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_purchase_unauthenticated(self):
        """Unauthenticated users should not be able to create purchases."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
        }

        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_purchase_without_items(self):
        """Test creating purchase without items."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "0.01",
            "paid_amount": "0.01",
            "notes": "Purchase without items",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"items": [ErrorDetail(string="This field is required.", code="required")]},
        )

    def test_create_purchase_missing_required_fields(self):
        """Test creating purchase with missing required fields."""
        data = {
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00"
            # Missing pos_session_id
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "pos_session_id": [
                    ErrorDetail(string="This field is required.", code="required")
                ],
                "items": [
                    ErrorDetail(string="This field is required.", code="required")
                ],
            },
        )

    def test_create_purchase_invalid_pos_session(self):
        """Test creating purchase with non-existent POS session."""
        data = {
            "pos_session_id": 99999,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "pos_session_id": [
                    ErrorDetail(
                        string='Invalid pk "99999" - object does not exist.',
                        code="does_not_exist",
                    )
                ],
                "items": [
                    ErrorDetail(string="This field is required.", code="required")
                ],
            },
        )

    def test_create_purchase_closed_pos_session(self):
        """Test creating purchase with closed POS session."""
        closed_session = POSSessionFactory.create(
            pos=self.pos_session2.pos, status=POSSession.Status.CLOSED
        )

        data = {
            "pos_session_id": closed_session.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                    "unit_price": "15.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "pos_session_id": [
                    ErrorDetail(
                        string='Invalid pk "3" - object does not exist.',
                        code="does_not_exist",
                    )
                ]
            },
        )

    def test_create_purchase_invalid_item_cost_price(self):
        """Test creating purchase with unit cost greater than unit price."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
            "paid_amount": "100.00",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "20.00",  # Greater than unit_price
                    "unit_price": "15.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "unit_cost": [
                            ErrorDetail(
                                string="Unit cost ($20.00) cannot be greater than unit price ($15.00)",
                                code="invalid",
                            )
                        ]
                    }
                ]
            },
        )

    # Business Logic Tests - Stock Management
    def test_bonus_calculation_with_zero_cost_items(self):
        """Test that bonus is calculated correctly for zero-cost items."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "20.00",
            "paid_amount": "20.00",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_cost": "0.00",  # Bonus item
                    "unit_price": "15.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "2.00",
                    "unit_cost": "10.00",
                    "unit_price": "15.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that bonus was calculated correctly
        purchase = Purchase.objects.get(id=response.data["id"])
        expected_bonus = Decimal("2.00") * Decimal("15.00")  # quantity * unit_price
        self.assertEqual(purchase.bonus, expected_bonus)

    # Update Tests
    def test_update_purchase_purchase_updated(self):
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase1.refresh_from_db()
        self.assertEqual(self.purchase1.paid_amount, Decimal("44.00"))
        self.assertEqual(self.purchase1.discount, Decimal("0.00"))
        self.assertEqual(self.purchase1.net_amount, Decimal("104.00"))
        self.assertEqual(self.purchase1.reminder_amount, Decimal("60.00"))
        self.assertEqual(self.purchase1.notes, "Updated purchase notes")
        self.assertEqual(self.purchase1.items.count(), 1)
        self.assertEqual(self.purchase1.bonus, Decimal("0.00"))

    def test_update_purchase_balance_updated(self):
        old_balance = self.balance1.amount
        old_reminder_amount = self.purchase1.reminder_amount
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase1.refresh_from_db()
        self.balance1.refresh_from_db()
        self.assertEqual(
            self.balance1.amount,
            old_balance - old_reminder_amount + self.purchase1.reminder_amount,
        )
        transaction = AccountTransaction.objects.get(
            related_object_id=self.purchase1.id,
            balance=self.balance1,
            related_content_type=ContentType.objects.get_for_model(Purchase),
        )
        self.assertEqual(transaction.amount, self.purchase1.reminder_amount)

    def test_update_purchase_balance_updated_with_exist_transaction(self):
        old_balance = self.balance2.amount
        old_reminder_amount = self.purchase2.reminder_amount
        url = self.get_detail_url(self.purchase2.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase2.refresh_from_db()
        self.balance2.refresh_from_db()
        self.assertEqual(
            self.balance2.amount,
            old_balance - old_reminder_amount + self.purchase2.reminder_amount,
        )
        transaction = AccountTransaction.objects.get(
            related_object_id=self.purchase2.id,
            balance=self.balance2,
            related_content_type=ContentType.objects.get_for_model(Purchase),
        )
        self.assertEqual(transaction.amount, self.purchase2.reminder_amount)

    def test_update_purchase_stock_updated(self):
        old_stock1 = self.stock1.quantity
        old_stock2 = self.stock2.quantity
        old_purchase_item1 = self.item1.quantity
        old_purchase_item2 = self.item2.quantity
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase1.refresh_from_db()

        # check stock refunded with old and remove new stock
        self.stock1.refresh_from_db()
        self.assertEqual(
            self.stock1.quantity, old_stock1 - old_purchase_item1 + Decimal("8.000")
        )
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock2.quantity, old_stock2 - old_purchase_item2)

    def test_update_purchase_pos_transaction_updated(self):
        old_pos_transaction_amount = self.pos_transaction1.amount
        old_pos_transaction_session_total = self.pos_session1.total_expenses
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase1.refresh_from_db()
        # check pos transaction updated
        self.pos_transaction1.refresh_from_db()
        self.assertEqual(self.pos_transaction1.amount, Decimal("44.00"))
        self.pos_session1.refresh_from_db()
        self.assertEqual(
            self.pos_session1.total_expenses,
            old_pos_transaction_session_total
            - old_pos_transaction_amount
            + self.pos_transaction1.amount,
        )

    def test_update_purchase_product_cost_price_updated(self):
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.update_date, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.purchase1.refresh_from_db()
        self.product1.refresh_from_db()
        self.assertEqual(self.product1.cost, Decimal("13.00"))
        self.assertEqual(self.product1.price, Decimal("16.00"))

    def test_update_purchase_as_manager(self):
        """Manager should be able to update purchases"""
        url = self.get_detail_url(self.purchase1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "104.00",
            "discount": "60.00",
            "paid_amount": "44.00",
            "notes": "Updated purchase notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "8.00",
                    "unit_cost": "13.00",  # 8 * 13 = 104
                    "unit_price": "16.00",
                }
            ],
        }

        response = self.manager_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_purchase_as_cashier(self):
        """Cashier should not be able to update purchases."""
        url = self.get_detail_url(self.purchase1.id)
        data = {"notes": "Cashier update"}

        response = self.cashier_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_purchase_closed_pos_session(self):
        """Should not be able to update purchase for closed POS session."""
        self.pos_session1.status = POSSession.Status.CLOSED
        self.pos_session1.save()
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.put(url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["detail"], "Cannot update purchase for closed POS session"
        )

    # Delete Tests

    def test_delete_purchase_balance_updated(self):
        old_balance = self.balance1.amount
        old_reminder_amount = self.purchase1.reminder_amount
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.balance1.refresh_from_db()
        self.assertEqual(
            self.balance1.amount,
            old_balance - old_reminder_amount,
        )
        self.assertFalse(
            AccountTransaction.objects.filter(
                related_object_id=self.purchase1.id,
                balance=self.balance1,
                related_content_type=ContentType.objects.get_for_model(Purchase),
            ).exists()
        )

    def test_delete_purchase_balance_updated_with_exist_transaction(self):
        old_balance = self.balance2.amount
        old_reminder_amount = self.purchase2.reminder_amount
        url = self.get_detail_url(self.purchase2.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.balance2.refresh_from_db()
        self.assertEqual(self.balance2.amount, old_balance - old_reminder_amount)
        self.assertFalse(
            AccountTransaction.objects.filter(
                related_object_id=self.purchase2.id,
                balance=self.balance2,
                related_content_type=ContentType.objects.get_for_model(Purchase),
            ).exists()
        )

    def test_delete_purchase_stock_updated(self):
        old_stock1 = self.stock1.quantity
        old_stock2 = self.stock2.quantity
        old_purchase_item1 = self.item1.quantity
        old_purchase_item2 = self.item2.quantity
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        # check stock refunded with old and remove new stock
        self.stock1.refresh_from_db()
        self.assertEqual(self.stock1.quantity, old_stock1 - old_purchase_item1)
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock2.quantity, old_stock2 - old_purchase_item2)

    def test_delete_purchase_pos_transaction_updated(self):
        old_pos_transaction_amount = self.pos_transaction1.amount
        old_pos_transaction_session_total = self.pos_session1.total_expenses
        url = self.get_detail_url(self.purchase1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        # check pos transaction updated
        self.assertFalse(
            POSSessionTransaction.objects.filter(id=self.pos_transaction1.id).exists()
        )
        self.pos_session1.refresh_from_db()
        self.assertEqual(
            self.pos_session1.total_expenses,
            old_pos_transaction_session_total - old_pos_transaction_amount,
        )

    def test_delete_purchase_as_manager(self):
        """Manager should not be able to delete purchases (admin only)."""
        url = self.get_detail_url(self.purchase1.id)

        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_delete_purchase_as_cashier(self):
        """Cashier should not be able to delete purchases."""
        url = self.get_detail_url(self.purchase1.id)

        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_nonexistent_purchase(self):
        """Test deleting a non-existent purchase."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Purchase Items Tests
    def test_purchase_items_creation(self):
        """Test that purchase items are created correctly with nested serializer."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "36.00",
            "paid_amount": "36.00",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "12.00",  # 3 * 12 = 36
                    "unit_price": "15.00",
                    "notes": "Special item note",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that item was created with correct values
        purchase = Purchase.objects.get(id=response.data["id"])
        item = purchase.items.first()
        self.assertEqual(item.product, self.product1)
        self.assertEqual(item.quantity, Decimal("3.00"))
        self.assertEqual(item.unit_cost, Decimal("12.00"))
        self.assertEqual(item.unit_price, Decimal("15.00"))
        self.assertEqual(item.total_cost, Decimal("36.00"))  # 3 * 12
        self.assertEqual(item.notes, "Special item note")

    # Validation Tests
    def test_create_purchase_invalid_supplier(self):
        """Test creating purchase with non-existent supplier."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": 99999,
            "total_amount": "100.00",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "supplier_id": [
                    ErrorDetail(
                        string='Invalid pk "99999" - object does not exist.',
                        code="does_not_exist",
                    )
                ],
                "items": [
                    ErrorDetail(string="This field is required.", code="required")
                ],
            },
        )

    def test_create_purchase_negative_amounts(self):
        """Test creating purchase with negative amounts."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "-100.00",  # Negative amount
            "paid_amount": "0.00",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_purchase_zero_net_amount(self):
        """Test creating purchase with zero net amount."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "100.00",
            "discount": "100.00",  # Discount equals total amount
            "paid_amount": "0.00",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                    "unit_price": "15.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Model String Representation Tests
    def test_purchase_string_representation(self):
        """Test the string representation of Purchase model."""
        expected_str = f"Purchase {self.purchase1.id} - {self.purchase1.supplier.name} - {self.purchase1.net_amount}"
        self.assertEqual(str(self.purchase1), expected_str)

    def test_purchase_item_string_representation(self):
        """Test the string representation of PurchaseItem model."""
        expected_str = f"{self.item1.quantity} x {self.item1.product.name} - {self.item1.total_cost}"
        self.assertEqual(str(self.item1), expected_str)

    # Edge Cases
    def test_precision_handling(self):
        """Test that decimal precision is handled correctly."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "total_amount": "14.54",
            "paid_amount": "14.54",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.12",
                    "unit_cost": "12.99",  # 1.12 * 12.99 = 14.5488 rounded to 14.54
                    "unit_price": "15.99",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that values are handled correctly
        purchase = Purchase.objects.get(id=response.data["id"])
        item = purchase.items.first()
        self.assertEqual(item.quantity, Decimal("1.12"))
        self.assertEqual(item.unit_cost, Decimal("12.99"))
        self.assertEqual(item.total_cost, Decimal("14.55"))  # 1.12 * 12.99 rounded
