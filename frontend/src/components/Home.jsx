
import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Home = () => {
    const { currentUser, permittedAdmin } = useAuth();

    return (
        <Container>
            <Row className="mb-4">
                <Col>
                    <h1>Welcome to El Quds Admin Dashboard</h1>
                    {currentUser && (
                        <p className="lead">
                            Hello, {currentUser.name}! You are logged in as {currentUser.role || 'User'}.
                        </p>
                    )}
                </Col>
            </Row>

            {currentUser && permittedAdmin() && (
                <Row>
                    <Col md={6} lg={4} className="mb-3">
                        <Card>
                            <Card.Header>
                                <h5 className="mb-0">Categories Management</h5>
                            </Card.Header>
                            <Card.Body>
                                <p>Manage categories and subcategories.</p>
                                <div className="d-flex gap-2">
                                    <Button
                                        variant="primary"
                                        as={Link}
                                        to="/categories"
                                        size="sm"
                                    >
                                        View Categories
                                    </Button>
                                    <Button
                                        variant="success"
                                        as={Link}
                                        to="/categories/create"
                                        size="sm"
                                    >
                                        Add Category
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>

                    <Col md={6} lg={4} className="mb-3">
                        <Card>
                            <Card.Header>
                                <h5 className="mb-0">Products</h5>
                            </Card.Header>
                            <Card.Body>
                                <p>Manage your product inventory.</p>
                                <div className="d-flex gap-2">
                                    <Button
                                        variant="primary"
                                        as={Link}
                                        to="/products"
                                        size="sm"
                                    >
                                        View Products
                                    </Button>
                                    <Button
                                        variant="success"
                                        as={Link}
                                        to="/products/create"
                                        size="sm"
                                    >
                                        Add Product
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>

                    <Col md={6} lg={4} className="mb-3">
                        <Card>
                            <Card.Header>
                                <h5 className="mb-0">Warehouses</h5>
                            </Card.Header>
                            <Card.Body>
                                <p>Manage warehouse locations and inventory.</p>
                                <div className="d-flex gap-2">
                                    <Button
                                        variant="primary"
                                        as={Link}
                                        to="/warehouses"
                                        size="sm"
                                    >
                                        View Warehouses
                                    </Button>
                                    <Button
                                        variant="success"
                                        as={Link}
                                        to="/warehouses/create"
                                        size="sm"
                                    >
                                        Add Warehouse
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            )}

            {!currentUser && (
                <Row>
                    <Col md={8} className="mx-auto text-center">
                        <Card>
                            <Card.Body>
                                <h3>Please Login</h3>
                                <p>You need to login to access the ERP system features.</p>
                                <Button variant="primary" as={Link} to="/login">
                                    Login
                                </Button>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            )}
        </Container>
    );
}

export default Home;