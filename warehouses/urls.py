# warehouses/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    StockItemViewSet,
    WarehouseViewSet,
    InventoryCountViewSet,
    InventoryCountItemViewSet,
)

router = DefaultRouter()
router.register(r"warehouses", WarehouseViewSet, basename="warehouse")
router.register(
    r"warehouses/(?P<warehouse_id>\d+)/stock-items",
    StockItemViewSet,
    basename="warehouse-stock-items",
)
router.register(r"inventory-counts", InventoryCountViewSet, basename="inventory-count")

# Nested router for inventory count items under inventory counts
router.register(
    r"inventory-counts/(?P<inventory_count_pk>\d+)/items",
    InventoryCountItemViewSet,
    basename="inventory-count-items",
)

urlpatterns = [
    path("", include(router.urls)),
]
