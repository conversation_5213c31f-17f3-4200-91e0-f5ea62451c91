from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from products.models.product import Product
from utils.test.base_test import BaseTestCase
from utils.test.factories.product.category import CategoryFactory
from utils.test.factories.product.product import ProductFactory


class ProductViewSetTestCase(BaseTestCase):
    """Test cases for ProductViewSet"""

    def setUp(self):
        super().setUp()
        self.list_url = reverse("products:product-list")
        self.detail_url_name = "products:product-detail"

        # Create test data using factories
        self.category = CategoryFactory()
        self.product = ProductFactory(category=self.category)
        self.detail_url = reverse(self.detail_url_name, kwargs={"pk": self.product.pk})

    def test_list_products_unauthenticated(self):
        """Test that unauthenticated users cannot list products"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_products_authenticated(self):
        """Test that authenticated users can list products"""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_retrieve_product(self):
        """Test retrieving a single product"""
        response = self.cashier_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.product.name)
        self.assertEqual(response.data["category_name"], self.category.name)

    def test_create_product_unauthenticated(self):
        """Test that unauthenticated users cannot create products"""
        data = {"name": "New Product", "price": 100, "cost": 50, "unit_type": "piece"}
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_product_unauthorized(self):
        """Test that cashier users cannot create products"""
        data = {"name": "New Product", "price": 100, "cost": 50, "unit_type": "piece"}
        response = self.cashier_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_product_manager(self):
        """Test that manager users can create products"""
        data = {
            "name": "New Product",
            "description": "Test product description",
            "price": "100.00",
            "cost": "50.00",
            "unit_type": "piece",
            "category_id": self.category.id,
            "barcode": "1234567890123",
        }
        response = self.manager_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Product.objects.count(), 2)
        self.assertEqual(response.data["name"], data["name"])

    def test_create_product_price_less_than_cost(self):
        """Test that price cannot be less than cost"""
        data = {
            "name": "Invalid Product",
            "price": "40.00",
            "cost": "50.00",  # Cost is higher than price
            "unit_type": "piece",
        }
        response = self.manager_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            [ErrorDetail(string="Price cannot be less than cost.", code="invalid")],
            response.data["price"],
        )

    def test_update_product_admin(self):
        """Test that admin users can update products"""
        data = {"name": "Updated Product Name"}
        response = self.admin_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.product.refresh_from_db()
        self.assertEqual(self.product.name, data["name"])

    def test_update_product_unauthorized(self):
        """Test that manager and cashier users cannot update products"""
        data = {"name": "Unauthorized Update"}

        # Test manager cannot update
        response = self.manager_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot update
        response = self.cashier_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_product_admin(self):
        """Test that admin users can delete products"""
        response = self.admin_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Product.objects.count(), 0)

    def test_delete_product_unauthorized(self):
        """Test that manager and cashier users cannot delete products"""
        # Test manager cannot delete
        response = self.manager_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot delete
        response = self.cashier_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_filter_products_by_category(self):
        """Test filtering products by category"""
        # Create a second category and product
        category2 = CategoryFactory()
        product2 = ProductFactory(category=category2)

        # Filter by first category
        response = self.cashier_client.get(
            self.list_url, {"category": self.category.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], self.product.id)

    def test_search_products(self):
        """Test searching products by name and barcode"""
        # Create a product with a unique name
        unique_product = ProductFactory(
            name="Unique Test Product", barcode="9876543210"
        )

        # Search by name
        response = self.cashier_client.get(self.list_url, {"search": "Unique Test"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], unique_product.id)

        # Search by barcode
        response = self.cashier_client.get(self.list_url, {"search": "9876543210"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], unique_product.id)

    def test_ordering_products(self):
        """Test ordering products by name and price"""
        # Create additional test products
        ProductFactory(name="A Product", price=50)
        ProductFactory(name="Z Product", price=200)

        # Test ordering by name ascending
        response = self.cashier_client.get(self.list_url, {"ordering": "name"})
        results = response.data["results"]
        self.assertEqual(results[0]["name"], "A Product")

        # Test ordering by price descending
        response = self.cashier_client.get(self.list_url, {"ordering": "-price"})
        results = response.data["results"]
        self.assertEqual(float(results[0]["price"]), 200)
