from django.urls import reverse
from rest_framework import status

from expenses.models import ExpenseCategory
from utils.test.base_test import BaseTestCase
from utils.test.factories.expenses.expense import ExpenseFactory
from utils.test.factories.expenses.expense_category import ExpenseCategoryFactory


class ExpenseCategoryViewSetTestCase(BaseTestCase):
    """
    Test cases for ExpenseCategoryViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("expenses:expense-category-list")
        self.detail_url_name = "expenses:expense-category-detail"

        # Create test data
        self.category1 = ExpenseCategoryFactory.create(
            name="Office Supplies", description="Office supplies and stationery"
        )
        self.category2 = ExpenseCategoryFactory.create(
            name="Travel", description="Travel and transportation expenses"
        )
        self.category3 = ExpenseCategoryFactory.create(
            name="Utilities", description="Utility bills and services"
        )

    def get_detail_url(self, category_id):
        """Helper method to get detail URL for a category."""
        return reverse(self.detail_url_name, kwargs={"pk": category_id})

    # Authentication Tests
    def test_list_categories_unauthenticated(self):
        """Test that unauthenticated users cannot list expense categories."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_category_unauthenticated(self):
        """Test that unauthenticated users cannot retrieve expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_category_unauthenticated(self):
        """Test that unauthenticated users cannot create expense categories."""
        data = {
            "name": "New Category",
            "description": "Test category",
        }
        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Permission Tests - List and Retrieve (Admin or Manager or Cashier)
    def test_list_categories_as_admin(self):
        """Admin should be able to list expense categories."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

        # Check that categories are ordered by name
        category_names = [cat["name"] for cat in response.data["results"]]
        self.assertEqual(category_names, ["Office Supplies", "Travel", "Utilities"])

    def test_list_categories_as_manager(self):
        """Manager should be able to list expense categories."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

    def test_list_categories_as_cashier(self):
        """Cashier should be able to list expense categories."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

    def test_retrieve_category_as_admin(self):
        """Admin should be able to retrieve expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Office Supplies")
        self.assertEqual(response.data["description"], "Office supplies and stationery")

    def test_retrieve_category_as_manager(self):
        """Manager should be able to retrieve expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Office Supplies")

    def test_retrieve_category_as_cashier(self):
        """Cashier should be able to retrieve expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Office Supplies")

    # Permission Tests - Create, Update, Delete (Admin Only)
    def test_create_category_as_admin(self):
        """Admin should be able to create expense categories."""
        data = {
            "name": "Marketing",
            "description": "Marketing and advertising expenses",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that category was created
        category = ExpenseCategory.objects.get(id=response.data["id"])
        self.assertEqual(category.name, "Marketing")
        self.assertEqual(category.description, "Marketing and advertising expenses")

    def test_create_category_as_manager(self):
        """Manager should not be able to create expense categories."""
        data = {
            "name": "Marketing",
            "description": "Marketing and advertising expenses",
        }
        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_category_as_cashier(self):
        """Cashier should not be able to create expense categories."""
        data = {
            "name": "Marketing",
            "description": "Marketing and advertising expenses",
        }
        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_category_with_duplicate_name(self):
        """Should not be able to create category with duplicate name."""
        data = {
            "name": "Office Supplies",  # Already exists
            "description": "Duplicate category",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("name", response.data)

    def test_create_category_missing_required_fields(self):
        """Should not be able to create category without required fields."""
        data = {
            "description": "Category without name",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("name", response.data)

    # Update Tests
    def test_update_category_as_admin(self):
        """Admin should be able to update expense categories."""
        url = self.get_detail_url(self.category1.id)
        data = {
            "name": "Updated Office Supplies",
            "description": "Updated description for office supplies",
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that category was updated
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.name, "Updated Office Supplies")
        self.assertEqual(
            self.category1.description, "Updated description for office supplies"
        )

    def test_partial_update_category_as_admin(self):
        """Admin should be able to partially update expense categories."""
        url = self.get_detail_url(self.category1.id)
        data = {
            "description": "Partially updated description",
        }
        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only description was updated
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.name, "Office Supplies")  # Unchanged
        self.assertEqual(self.category1.description, "Partially updated description")

    def test_update_category_as_manager(self):
        """Manager should not be able to update expense categories."""
        url = self.get_detail_url(self.category1.id)
        data = {
            "name": "Updated Office Supplies",
            "description": "Updated description",
        }
        response = self.manager_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_category_as_cashier(self):
        """Cashier should not be able to update expense categories."""
        url = self.get_detail_url(self.category1.id)
        data = {
            "name": "Updated Office Supplies",
            "description": "Updated description",
        }
        response = self.cashier_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Delete Tests
    def test_delete_category_as_admin(self):
        """Admin should be able to delete unused expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that category was deleted
        self.assertFalse(ExpenseCategory.objects.filter(id=self.category1.id).exists())

    def test_delete_category_as_manager(self):
        """Manager should not be able to delete expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_category_as_cashier(self):
        """Cashier should not be able to delete expense categories."""
        url = self.get_detail_url(self.category1.id)
        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_category_in_use(self):
        """Should not be able to delete category that is in use by expenses."""
        # Create an expense using this category
        ExpenseFactory.create(expense_category=self.category1)
        url = self.get_detail_url(self.category1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # Check that category was not deleted
        self.assertTrue(ExpenseCategory.objects.filter(id=self.category1.id).exists())

    def test_delete_nonexistent_category(self):
        """Should return 404 when trying to delete nonexistent category."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Validation Tests
    def test_create_category_with_empty_name(self):
        """Should not be able to create category with empty name."""
        data = {
            "name": "",
            "description": "Category with empty name",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("name", response.data)

    def test_create_category_with_long_name(self):
        """Should not be able to create category with name longer than 100 characters."""
        data = {
            "name": "A" * 101,  # 101 characters
            "description": "Category with very long name",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("name", response.data)

    def test_create_category_without_description(self):
        """Should be able to create category without description (optional field)."""
        data = {
            "name": "No Description Category",
        }
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        category = ExpenseCategory.objects.get(id=response.data["id"])
        self.assertEqual(category.name, "No Description Category")
        self.assertIsNone(category.description)

    def test_update_category_with_duplicate_name(self):
        """Should not be able to update category to have duplicate name."""
        url = self.get_detail_url(self.category1.id)
        data = {
            "name": "Travel",  # Already exists (category2)
            "description": "Updated description",
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("name", response.data)

    # Serializer and Field Tests
    def test_category_serializer_fields(self):
        """Test that category serializer includes all expected fields."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if response.data["results"]:
            category_data = response.data["results"][0]
            expected_fields = ["id", "name", "description", "created", "modified"]
            for field in expected_fields:
                self.assertIn(field, category_data)

    def test_category_read_only_fields(self):
        """Test that read-only fields cannot be modified."""
        url = self.get_detail_url(self.category1.id)
        original_created = self.category1.created
        original_modified = self.category1.modified

        data = {
            "name": "Updated Name",
            "description": "Updated description",
            "id": 99999,  # Should be ignored
            "created": "2020-01-01T00:00:00Z",  # Should be ignored
            "modified": "2020-01-01T00:00:00Z",  # Should be ignored
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.category1.refresh_from_db()
        self.assertEqual(self.category1.name, "Updated Name")
        self.assertEqual(self.category1.description, "Updated description")
        self.assertNotEqual(self.category1.id, 99999)  # ID unchanged
        self.assertEqual(self.category1.created, original_created)  # Created unchanged
        self.assertNotEqual(
            self.category1.modified, original_modified
        )  # Modified updated

    # Ordering Tests
    def test_categories_ordered_by_name(self):
        """Test that categories are returned ordered by name."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        category_names = [cat["name"] for cat in response.data["results"]]
        self.assertEqual(category_names, sorted(category_names))

    # Edge Cases
    def test_retrieve_nonexistent_category(self):
        """Should return 404 when trying to retrieve nonexistent category."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_nonexistent_category(self):
        """Should return 404 when trying to update nonexistent category."""
        url = self.get_detail_url(99999)
        data = {
            "name": "Updated Name",
            "description": "Updated description",
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
