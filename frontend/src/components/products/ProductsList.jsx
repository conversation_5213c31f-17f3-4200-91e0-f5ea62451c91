import React, { useState, useEffect } from 'react';
import { 
    Container, 
    Row, 
    Col, 
    Table, 
    Button, 
    Form, 
    InputGroup, 
    <PERSON><PERSON>, 
    Spin<PERSON>, 
    Badge,
    Card,
    Pagination
} from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useProducts } from '../../hooks/useProducts';
import { useAuth } from '../../contexts/AuthContext';
import DeleteConfirmModal from './DeleteConfirmModal';
import {formatCurrency} from "../../utils";

const ProductsList = () => {
    const navigate = useNavigate();
    const { currentUser } = useAuth();
    const { 
        products, 
        loading, 
        error, 
        pagination, 
        fetchProducts, 
        deleteProduct,
        setError 
    } = useProducts();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [ordering, setOrdering] = useState('name');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);

    // Load products on component mount
    useEffect(() => {
        fetchProducts({
            page: 1,
            page_size: 10,
            ordering: 'name'
        });
    }, [fetchProducts]);

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchProducts({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchProducts({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchProducts({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle delete confirmation
    const handleDeleteClick = (product) => {
        setProductToDelete(product);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteProduct(productToDelete.id);
            setShowDeleteModal(false);
            setProductToDelete(null);
            
            // Refresh the current page
            fetchProducts({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            });
        } catch (err) {
            // Error is handled by the hook
        }
    };

    // Check if user can perform admin actions
    const canPerformAdminActions = currentUser && 
        (currentUser.role === 'admin' || currentUser.role === 'manager');

    // Format unit type
    const formatUnitType = (unitType) => {
        const unitTypes = {
            'piece': 'Piece',
            'kg': 'Kilogram',
            'g': 'Gram',
            'l': 'Liter',
            'ml': 'Milliliter'
        };
        return unitTypes[unitType] || unitType;
    };

    // Calculate profit margin
    const calculateProfitMargin = (cost, price) => {
        if (cost === 0) return 0;
        return ((price - cost) / cost * 100).toFixed(2);
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // Previous button
        items.push(
            <Pagination.Prev 
                key="prev"
                disabled={!pagination.previous}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = 1; page <= totalPages; page++) {
            if (
                page === 1 || 
                page === totalPages || 
                (page >= currentPage - 2 && page <= currentPage + 2)
            ) {
                items.push(
                    <Pagination.Item
                        key={page}
                        active={page === currentPage}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </Pagination.Item>
                );
            } else if (
                page === currentPage - 3 || 
                page === currentPage + 3
            ) {
                items.push(<Pagination.Ellipsis key={`ellipsis-${page}`} />);
            }
        }

        // Next button
        items.push(
            <Pagination.Next 
                key="next"
                disabled={!pagination.next}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return items;
    };

    const getSortIcon = (field) => {
        if (ordering === field) return ' ↑';
        if (ordering === `-${field}`) return ' ↓';
        return '';
    };
    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Products</h2>
                        {canPerformAdminActions && (
                            <Button 
                                variant="primary" 
                                onClick={() => navigate('/products/create')}
                            >
                                Add New Product
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filters */}
            <Row className="mb-3">
                <Col md={8}>
                    <Form onSubmit={handleSearch}>
                        <InputGroup>
                            <Form.Control
                                type="text"
                                placeholder="Search products by name, barcode, or description..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            <Button variant="outline-secondary" type="submit">
                                Search
                            </Button>
                        </InputGroup>
                    </Form>
                </Col>
                <Col md={4}>
                    <Form.Select
                        value={ordering}
                        onChange={(e) => handleSort(e.target.value)}
                    >
                        <option value="name">Sort by Name (A-Z)</option>
                        <option value="-name">Sort by Name (Z-A)</option>
                        <option value="price">Sort by Price (Low-High)</option>
                        <option value="-price">Sort by Price (High-Low)</option>
                        <option value="created">Sort by Date (Oldest)</option>
                        <option value="-created">Sort by Date (Newest)</option>
                    </Form.Select>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Products Table */}
            <Card>
                <Card.Body>
                    {loading ? (
                        <div className="text-center py-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : products.length === 0 ? (
                        <div className="text-center py-4">
                            <p className="text-muted">No products found.</p>
                            {canPerformAdminActions && (
                                <Button 
                                    variant="primary" 
                                    onClick={() => navigate('/products/create')}
                                >
                                    Create First Product
                                </Button>
                            )}
                        </div>
                    ) : (
                        <>
                            <Table responsive striped hover>
                                <thead className="listing-table-header">
                                    <tr>
                                        <th onClick={() => {handleSort('name')}}>
                                            Name{getSortIcon('name')}
                                        </th>
                                        <th>Category</th>
                                        <th>Cost</th>
                                        <th onClick={() => {handleSort('price')}}>
                                            Price{getSortIcon('price')}
                                        </th>
                                        <th>Profit Margin</th>
                                        <th>Unit Type</th>
                                        <th>Barcode</th>
                                        <th onClick={() => {handleSort('created')}}>
                                            Date{getSortIcon('created')}
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {products.map((product) => (
                                        <tr key={product.id}>
                                            <td>
                                                <strong>{product.name}</strong>
                                                {product.description && (
                                                    <div className="text-muted small">
                                                        {product.description.length > 50 
                                                            ? `${product.description.substring(0, 50)}...`
                                                            : product.description
                                                        }
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                {product.category_name ||
                                                 <span className="text-muted">No category</span>}
                                            </td>
                                            <td>{formatCurrency(product.cost)}</td>
                                            <td>{formatCurrency(product.price)}</td>
                                            <td>
                                                <Badge 
                                                    bg={calculateProfitMargin(product.cost, product.price) > 20 ? 'success' : 'warning'}
                                                >
                                                    {calculateProfitMargin(product.cost, product.price)}%
                                                </Badge>
                                            </td>
                                            <td>{formatUnitType(product.unit_type)}</td>
                                            <td>
                                                {product.barcode || 
                                                 <span className="text-muted">-</span>}
                                            </td>
                                            <td>
                                                {new Date(product.created).toLocaleString()}
                                            </td>
                                            <td>
                                                <div className="d-flex gap-1">
                                                    <Button
                                                        variant="outline-info"
                                                        size="sm"
                                                        onClick={() => navigate(`/products/${product.id}`)}
                                                    >
                                                        View
                                                    </Button>
                                                    {canPerformAdminActions && (
                                                        <>
                                                            <Button
                                                                variant="outline-warning"
                                                                size="sm"
                                                                onClick={() => navigate(`/products/${product.id}/edit`)}
                                                            >
                                                                Edit
                                                            </Button>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => handleDeleteClick(product)}
                                                            >
                                                                Delete
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {pagination.count > pagination.page_size && (
                                <div className="d-flex justify-content-between align-items-center mt-3">
                                    <div className="text-muted">
                                        Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                        {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                        {pagination.count} products
                                    </div>
                                    <Pagination className="mb-0">
                                        {generatePaginationItems()}
                                    </Pagination>
                                </div>
                            )}
                        </>
                    )}
                </Card.Body>
            </Card>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                productName={productToDelete?.name}
                loading={loading}
            />
        </Container>
    );
};

export default ProductsList;
