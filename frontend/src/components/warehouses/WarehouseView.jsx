import React, {useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Col,
    Container,
    Form,
    Modal,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate, useParams} from 'react-router-dom';
import {useWarehouse} from '../../hooks/useWarehouses';
import {useStockItems} from '../../hooks/useStockItems';
import {useAuth} from '../../contexts/AuthContext';
import StockItemForm from './StockItemForm';
import DeleteStockItemModal from './DeleteStockItemModal';

/**
 * Component for viewing warehouse details
 * @returns {JSX.Element} The warehouse view component
 */
const WarehouseView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { currentUser } = useAuth();
    const { warehouse, loading, error } = useWarehouse(id);
    const { 
        stockItems, 
        loading: stockItemsLoading, 
        error: stockItemsError,
        pagination,
        fetchStockItems,
        createStockItem,
        updateStockItem,
        deleteStockItem
    } = useStockItems(id);

    // State for stock items filtering and pagination
    const [searchTerm, setSearchTerm] = useState('');
    const [ordering, setOrdering] = useState('product__name');

    // State for stock item form modal
    const [showStockItemModal, setShowStockItemModal] = useState(false);
    const [stockItemFormData, setStockItemFormData] = useState({});
    const [stockItemFormError, setStockItemFormError] = useState(null);
    const [stockItemFormLoading, setStockItemFormLoading] = useState(false);
    const [stockItemFormMode, setStockItemFormMode] = useState('add'); // 'add' or 'edit'

    // State for delete confirmation modal
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [stockItemToDelete, setStockItemToDelete] = useState(null);

    // Load stock items with pagination on component mount
    useEffect(() => {
        if (id) {
            fetchStockItems({
                page: 1,
                page_size: 10,
                ordering: 'product__name'
            });
        }
    }, [id, fetchStockItems]);

    // Check if user can perform admin actions
    const canPerformAdminActions = currentUser && 
        (currentUser.role === 'admin' || currentUser.role === 'manager');

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Handle opening the add stock item modal
    const handleAddStockItem = () => {
        setStockItemFormData({});
        setStockItemFormError(null);
        setStockItemFormMode('add');
        setShowStockItemModal(true);
    };

    // Handle opening the edit stock item modal
    const handleEditStockItem = (stockItem) => {
        setStockItemFormData(stockItem);
        setStockItemFormError(null);
        setStockItemFormMode('edit');
        setShowStockItemModal(true);
    };

    // Handle stock item form submission
    const handleStockItemSubmit = async (data) => {
        try {
            setStockItemFormLoading(true);
            setStockItemFormError(null);

            if (stockItemFormMode === 'add') {
                await createStockItem(data);
            } else {
                await updateStockItem(stockItemFormData.id, data);
            }

            setShowStockItemModal(false);
            fetchStockItems(); // Refresh the stock items list
        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;

                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field]) 
                            ? errorData[field] 
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setStockItemFormError(errorMessages.join(', '));
                } else {
                    setStockItemFormError(errorData.detail || errorData.message || 'Failed to save stock item');
                }
            } else {
                setStockItemFormError('Failed to save stock item. Please try again.');
            }
        } finally {
            setStockItemFormLoading(false);
        }
    };

    // Handle delete click
    const handleDeleteClick = (stockItem) => {
        setStockItemToDelete(stockItem);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteStockItem(stockItemToDelete.id);
            setShowDeleteModal(false);
            setStockItemToDelete(null);

            // Refresh the current page
            fetchStockItems({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            });
        } catch (err) {
            // Error is handled by the hook
        }
    };

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchStockItems({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchStockItems({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchStockItems({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // Previous button
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={!pagination.previous}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = 1; page <= totalPages; page++) {
            if (
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 2 && page <= currentPage + 2)
            ) {
                items.push(
                    <Pagination.Item
                        key={page}
                        active={page === currentPage}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </Pagination.Item>
                );
            } else if (
                page === currentPage - 3 ||
                page === currentPage + 3
            ) {
                items.push(<Pagination.Ellipsis key={`ellipsis-${page}`} />);
            }
        }

        // Next button
        items.push(
            <Pagination.Next
                key="next"
                disabled={!pagination.next}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return items;
    };

    const getSortIcon = (field) => {
        if (ordering === field) return ' ↑';
        if (ordering === `-${field}`) return ' ↓';
        return '';
    };

    // Show loading spinner
    if (loading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Warehouse Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading warehouse...</span>
                        </Spinner>
                        <div className="mt-2">Loading warehouse details...</div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if warehouse not found
    if (error || !warehouse) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Warehouse Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {error || 'Warehouse not found'}
                        </Alert>
                        <Button variant="secondary" onClick={() => navigate('/warehouses')}>
                            Back to Warehouses
                        </Button>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{warehouse.name}</h2>
                            <p className="text-muted mb-0">Warehouse Details</p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button 
                                variant="secondary" 
                                onClick={() => navigate('/warehouses')}
                            >
                                Back to Warehouses
                            </Button>
                            {canPerformAdminActions && (
                                <Button 
                                    variant="primary" 
                                    onClick={() => navigate(`/warehouses/${warehouse.id}/edit`)}
                                >
                                    Edit Warehouse
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col lg={8}>
                    <Card className="mb-4">
                        <Card.Header>
                            <h5 className="mb-0">Basic Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Warehouse Name:</strong>
                                        <div>{warehouse.name}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Location:</strong>
                                        <div>{warehouse.location}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Status:</strong>
                                        <div>
                                            <Badge 
                                                bg={warehouse.is_active ? 'success' : 'secondary'}
                                            >
                                                {warehouse.is_active ? 'Active' : 'Inactive'}
                                            </Badge>
                                        </div>
                                    </div>
                                </Col>

                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Created:</strong>
                                        <div className="text-muted">
                                            {formatDate(warehouse.created)}
                                        </div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Last Modified:</strong>
                                        <div className="text-muted">
                                            {formatDate(warehouse.modified)}
                                        </div>
                                    </div>
                                </Col>
                            </Row>

                            {warehouse.description && (
                                <div className="mt-3">
                                    <strong>Description:</strong>
                                    <div className="mt-2 p-3 bg-light rounded">
                                        {warehouse.description}
                                    </div>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>

                <Col lg={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Quick Info</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-flex justify-content-between mb-2">
                                <span>Status:</span>
                                <strong>
                                    <Badge 
                                        bg={warehouse.is_active ? 'success' : 'secondary'}
                                    >
                                        {warehouse.is_active ? 'Active' : 'Inactive'}
                                    </Badge>
                                </strong>
                            </div>
                            <div className="d-flex justify-content-between mb-2">
                                <span>Location:</span>
                                <strong>{warehouse.location}</strong>
                            </div>
                            <div className="d-flex justify-content-between">
                                <span>Created:</span>
                                <strong>{new Date(warehouse.created).toLocaleDateString()}</strong>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Stock Items Section */}
            <Row className="mt-4" id="stock">
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Stock Items</h5>
                            {canPerformAdminActions && (
                                <Button 
                                    variant="primary" 
                                    size="sm"
                                    onClick={handleAddStockItem}
                                >
                                    Add Stock Item
                                </Button>
                            )}
                        </Card.Header>
                        <Card.Body>
                            {/* Search and Filters */}
                            <Row className="mb-3">
                                <Col md={8}>
                                    <Form onSubmit={handleSearch}>
                                        <Form.Group className="d-flex">
                                            <Form.Control
                                                type="text"
                                                placeholder="Search stock items by product name..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                aria-label="Search stock items"
                                            />
                                            <Button variant="outline-secondary" type="submit" className="ms-2">
                                                Search
                                            </Button>
                                        </Form.Group>
                                    </Form>
                                </Col>
                                <Col md={4}>
                                    <Form.Select
                                        value={ordering}
                                        onChange={(e) => handleSort(e.target.value)}
                                        aria-label="Sort stock items"
                                    >
                                        <option value="product__name">Sort by Product Name (A-Z)</option>
                                        <option value="-product__name">Sort by Product Name (Z-A)</option>
                                        <option value="quantity">Sort by Quantity (Low-High)</option>
                                        <option value="-quantity">Sort by Quantity (High-Low)</option>
                                        <option value="modified">Sort by Last Updated (Oldest)</option>
                                        <option value="-modified">Sort by Last Updated (Newest)</option>
                                    </Form.Select>
                                </Col>
                            </Row>

                            {stockItemsError && (
                                <Alert variant="danger">
                                    {stockItemsError}
                                </Alert>
                            )}

                            {stockItemsLoading ? (
                                <div className="text-center py-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading stock items...</span>
                                    </Spinner>
                                    <div className="mt-2">Loading stock items...</div>
                                </div>
                            ) : stockItems.length === 0 ? (
                                <div className="text-center py-4">
                                    <p className="text-muted">No stock items found for this warehouse.</p>
                                    {canPerformAdminActions && (
                                        <Button
                                            variant="primary"
                                            onClick={handleAddStockItem}
                                        >
                                            Add First Stock Item
                                        </Button>
                                    )}
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped hover>
                                        <thead>
                                            <tr>
                                                <th onClick={() => handleSort('product__name')}>
                                                    Product{getSortIcon('product__name')}
                                                </th>
                                                <th onClick={() => handleSort('quantity')}>
                                                    Quantity{getSortIcon('quantity')}
                                                </th>
                                                <th onClick={() => handleSort('min_stock')}>
                                                    Min Stock{getSortIcon('min_stock')}
                                                </th>
                                                <th>Status</th>
                                                <th onClick={() => handleSort('modified')}>
                                                    Last Updated{getSortIcon('modified')}
                                                </th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {stockItems.map(item => (
                                                <tr key={item.id}>
                                                    <td>{item.product_name}</td>
                                                    <td>{item.quantity} {item.unit_type}</td>
                                                    <td>{item.min_stock} {item.unit_type}</td>
                                                    <td>
                                                        {item.quantity <= 0 ? (
                                                            <Badge bg="danger">Out of Stock</Badge>
                                                        ) : item.quantity <= item.min_stock ? (
                                                            <Badge bg="warning">Low Stock</Badge>
                                                        ) : (
                                                            <Badge bg="success">In Stock</Badge>
                                                        )}
                                                    </td>
                                                    <td>{new Date(item.modified).toLocaleDateString()}</td>
                                                    <td>
                                                        {canPerformAdminActions && (
                                                            <div className="d-flex gap-1">
                                                                <Button
                                                                    variant="outline-warning"
                                                                    size="sm"
                                                                    onClick={() => handleEditStockItem(item)}
                                                                >
                                                                    Edit
                                                                </Button>
                                                                <Button
                                                                    variant="outline-danger"
                                                                    size="sm"
                                                                    onClick={() => handleDeleteClick(item)}
                                                                >
                                                                    Delete
                                                                </Button>
                                                            </div>
                                                        )}
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </Table>

                                    {/* Pagination */}
                                    {pagination.count > pagination.page_size && (
                                        <div className="d-flex justify-content-between align-items-center mt-3">
                                            <div className="text-muted">
                                                Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                                {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                                {pagination.count} stock items
                                            </div>
                                            <Pagination className="mb-0">
                                                {generatePaginationItems()}
                                            </Pagination>
                                        </div>
                                    )}
                                </>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Stock Item Form Modal */}
            <Modal
                show={showStockItemModal}
                onHide={() => setShowStockItemModal(false)}
                size="lg"
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        {stockItemFormMode === 'add' ? 'Add Stock Item' : 'Edit Stock Item'}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <StockItemForm
                        warehouseId={id}
                        initialData={stockItemFormData}
                        onSubmit={handleStockItemSubmit}
                        loading={stockItemFormLoading}
                        error={stockItemFormError}
                        isEdit={stockItemFormMode === 'edit'}
                        onCancel={() => setShowStockItemModal(false)}
                    />
                </Modal.Body>
            </Modal>

            {/* Delete Confirmation Modal */}
            <DeleteStockItemModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                productName={stockItemToDelete?.product_name}
                loading={stockItemsLoading}
            />
        </Container>
    );
};

export default WarehouseView;
