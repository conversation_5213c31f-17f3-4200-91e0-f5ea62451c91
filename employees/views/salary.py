from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly

from ..models import Salary
from ..serializers.salary import SalarySerializer, SalarySummarySerializer


class SalaryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for listing and retrieving salary records.

    Provides:
    - List all salaries with filtering, searching, and ordering
    - Retrieve individual salary records
    - Admin-only access

    Filtering:
    - employee: Filter by employee ID
    - salary_min: Filter by minimum salary amount
    - salary_max: Filter by maximum salary amount
    - created_after: Filter by creation date (after)
    - created_before: Filter by creation date (before)
    - employee__type: Filter by employee type (full_time, part_time, daily)
    - employee__status: Filter by employee status (active, inactive, terminated)
    - employee__pos: Filter by POS ID

    Searching:
    - Employee first name, last name, email
    - Salary notes
    - Employee POS name

    Ordering:
    - created (default: -created)
    - salary
    - employee__user__first_name
    - employee__user__last_name
    - attendance__time_in
    - attendance__total_hours
    """

    queryset = Salary.objects.select_related(
        'employee__user',
        'employee__pos',
        'attendance'
    ).all()
    permission_classes = [IsAdminOnly]
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]

    # Filtering fields
    filterset_fields = {
        'employee': ['exact'],
        'salary': ['gte', 'lte', 'exact'],
        'created': ['gte', 'lte', 'date'],
        'employee__type': ['exact'],
        'employee__status': ['exact'],
        'employee__pos': ['exact'],
        'attendance__time_in': ['date', 'gte', 'lte'],
        'attendance__total_hours': ['gte', 'lte'],
    }

    # Search fields
    search_fields = [
        'employee__user__first_name',
        'employee__user__last_name',
        'employee__user__email',
        'notes',
        'employee__pos__name',
    ]

    # Ordering fields
    ordering_fields = [
        'created',
        'salary',
        'employee__user__first_name',
        'employee__user__last_name',
        'attendance__time_in',
        'attendance__total_hours',
    ]
    ordering = ['-created']  # Default ordering

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action.
        Use detailed serializer for retrieve, summary for list.
        """
        if self.action == 'retrieve':
            return SalarySerializer
        return SalarySummarySerializer

    def get_queryset(self):
        """
        Optionally filter the queryset based on additional parameters.
        """
        queryset = super().get_queryset()

        # Additional custom filtering can be added here
        # For example, filtering by date ranges with custom parameter names

        # Filter by salary range using custom parameters
        salary_min = self.request.query_params.get('salary_min')
        salary_max = self.request.query_params.get('salary_max')

        if salary_min:
            try:
                queryset = queryset.filter(salary__gte=float(salary_min))
            except (ValueError, TypeError):
                pass

        if salary_max:
            try:
                queryset = queryset.filter(salary__lte=float(salary_max))
            except (ValueError, TypeError):
                pass

        # Filter by date range using custom parameters
        created_after = self.request.query_params.get('created_after')
        created_before = self.request.query_params.get('created_before')

        if created_after:
            try:
                queryset = queryset.filter(created__date__gte=created_after)
            except (ValueError, TypeError):
                pass

        if created_before:
            try:
                queryset = queryset.filter(created__date__lte=created_before)
            except (ValueError, TypeError):
                pass

        # Filter by work date (attendance date)
        work_date = self.request.query_params.get('work_date')
        work_date_after = self.request.query_params.get('work_date_after')
        work_date_before = self.request.query_params.get('work_date_before')

        if work_date:
            try:
                queryset = queryset.filter(attendance__time_in__date=work_date)
            except (ValueError, TypeError):
                pass

        if work_date_after:
            try:
                queryset = queryset.filter(attendance__time_in__date__gte=work_date_after)
            except (ValueError, TypeError):
                pass

        if work_date_before:
            try:
                queryset = queryset.filter(attendance__time_in__date__lte=work_date_before)
            except (ValueError, TypeError):
                pass

        return queryset