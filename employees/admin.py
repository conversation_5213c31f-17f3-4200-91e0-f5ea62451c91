from django.contrib import admin

from .models.attendance import Attendance
from .models.employee import Employee
from .models.salary import Salary


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ("user", "type", "status", "hire_date", "pos")
    list_filter = ("type", "status", "pos")
    search_fields = ("user__first_name", "user__last_name", "user__email")
    readonly_fields = ("created", "modified")
    fieldsets = (
        ("Personal Info", {"fields": ("user", "address", "identification")}),
        ("Employment Details", {"fields": ("type", "hire_date", "pos", "status")}),
        ("Salary Information", {"fields": ("hour_rate", "day_rate")}),
        ("Timestamps", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ("employee", "time_in", "time_out", "total_hours")
    list_filter = ("employee",)
    search_fields = ("employee__user__first_name", "employee__user__last_name")
    readonly_fields = ("created", "modified", "total_hours")


@admin.register(Salary)
class SalaryAdmin(admin.ModelAdmin):
    list_display = ("employee", "salary", "created")
    list_filter = ("created",)
    search_fields = ("employee__user__first_name", "employee__user__last_name")
    date_hierarchy = "created"
    readonly_fields = ("created", "modified")
