from factory import Faker, Lazy<PERSON>ttribute, post_generation
from factory.django import DjangoModelFactory
from faker import Faker as FakerGenerator

from users.models import User


class UserFactory(DjangoModelFactory):
    """
    Factory for creating User instances for testing.
    """

    class Meta:
        model = User
        skip_postgeneration_save = True

    # Required fields
    email = Faker("email")
    phone_number = LazyAttribute(
        lambda _: f"01{FakerGenerator().random_number(digits=9, fix_len=True)}"
    )
    first_name = Faker("first_name")
    last_name = Faker("last_name")

    # Default values
    is_active = True
    is_staff = False
    is_superuser = False
    role = User.Role.CASHIER

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to handle password hashing."""
        password = kwargs.pop("password", None)
        user = super()._create(model_class, *args, **kwargs)
        if password:
            user.set_password(password)
            user.save()
        return user

    @post_generation
    def password(self, create, extracted, **kwargs):
        """
        Handle password setting after user creation.
        If no password is provided, use 'testpass123' as default.
        """
        password = extracted or "testpass123"
        self.set_password(password)
        if create:
            self.save()
