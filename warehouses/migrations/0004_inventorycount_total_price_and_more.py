# Generated by Django 5.2.1 on 2025-06-22 18:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("warehouses", "0003_inventorycount_total_cost"),
    ]

    operations = [
        migrations.AddField(
            model_name="inventorycount",
            name="total_price",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total cost of inventory based on difference and product price",
                max_digits=15,
                verbose_name="total price",
            ),
        ),
        migrations.AlterField(
            model_name="inventorycount",
            name="total_cost",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total cost of inventory based on difference and product costs",
                max_digits=15,
                verbose_name="total cost",
            ),
        ),
    ]
