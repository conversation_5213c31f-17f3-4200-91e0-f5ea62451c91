import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Row, Spinner} from 'react-bootstrap';
import {useWarehouses} from '../../hooks/useWarehouses';

/**
 * Reusable form component for creating and editing inventory counts
 * @param {Object} props - Component props
 * @param {Object} props.initialData - Initial form data for edit mode
 * @param {Function} props.onSubmit - Form submission handler
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {boolean} props.isEdit - Whether this is the edit mode
 */
const InventoryCountForm = ({
                                initialData = {},
                                onSubmit,
                                loading = false,
                                error = "",
                                isEdit = false
                            }) => {
    const {warehouses, loading: warehousesLoading, fetchWarehouses} = useWarehouses();
    const [formData, setFormData] = useState({
        warehouse: '',
        notes: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Load warehouses on the component mount
    useEffect(() => {
        fetchWarehouses();
    }, []);

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                warehouse: initialData.warehouse || '',
                notes: initialData.notes || ''
            });
        }
    }, []);

    // Handle input changes
    const handleChange = (e) => {
        const {name, value, type, checked} = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.warehouse) {
            errors.warehouse = 'Warehouse is required';
        }

        if (formData.notes && formData.notes.length > 1000) {
            errors.notes = 'Notes must not exceed 1000 characters';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            warehouse: parseInt(formData.warehouse),
            notes: formData.notes.trim() || null
        };

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Inventory Count' : 'Create New Inventory Count'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Warehouse <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Select
                                    name="warehouse"
                                    value={formData.warehouse}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.warehouse}
                                    disabled={isEdit || warehousesLoading}
                                >
                                    <option value="">Select a warehouse</option>
                                    {warehouses.map(warehouse => (
                                        <option key={warehouse.id} value={warehouse.id}>
                                            {warehouse.name} - {warehouse.location}
                                        </option>
                                    ))}
                                </Form.Select>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.warehouse}
                                </Form.Control.Feedback>
                                {warehousesLoading && (
                                    <Form.Text className="text-muted">
                                        Loading warehouses...
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </Col>

                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Status</Form.Label>
                                <Form.Control
                                    type="text"
                                    value={isEdit ? (initialData.status_display || 'Draft') : 'Draft'}
                                    disabled
                                    readOnly
                                />
                                <Form.Text className="text-muted">
                                    Status is automatically managed based on workflow
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Form.Group className="mb-3">
                        <Form.Label>Notes</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={4}
                            name="notes"
                            value={formData.notes}
                            onChange={handleChange}
                            isInvalid={!!validationErrors.notes}
                            placeholder="Enter any notes about this inventory count (optional)"
                            maxLength={1000}
                            aria-describedby="notes-help"
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.notes}
                        </Form.Control.Feedback>
                        <Form.Text id="notes-help" className="text-muted">
                            {formData.notes.length}/1000 characters
                        </Form.Text>
                    </Form.Group>

                    {!isEdit && (
                        <Alert variant="info" className="mb-3">
                            <strong>Note:</strong> Creating an inventory count will automatically generate
                            count items for all stock items in the selected warehouse. You can then record
                            the actual quantities found during the physical count.
                        </Alert>
                    )}

                    <div className="d-flex gap-2">
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading || warehousesLoading}
                            className="me-2"
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Inventory Count' : 'Create Inventory Count'
                            )}
                        </Button>
                        <Button
                            variant="secondary"
                            type="button"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default InventoryCountForm;
