import api from './api';

/**
 * Inventory Count Items API service
 * Handles all API calls related to inventory count items CRUD operations
 */
export const inventoryCountItemsService = {
    /**
     * Get all inventory count items for a specific inventory count
     * @param {number} inventoryCountId - Inventory count ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @returns {Promise} API response
     */
    getInventoryCountItems: (inventoryCountId, params = {}) => {
        const queryParams = new URLSearchParams();

        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);

        // Add search parameter
        if (params.search) queryParams.append('search', params.search);

        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);

        const queryString = queryParams.toString();
        const url = `/inventory-counts/${inventoryCountId}/items/${queryString ? `?${queryString}` : ''}`;

        return api.get(url);
    },

    /**
     * Get a single inventory count item by ID
     * @param {number} inventoryCountId - Inventory count ID
     * @param {number} itemId - Inventory count item ID
     * @returns {Promise} API response
     */
    getInventoryCountItem: (inventoryCountId, itemId) => {
        return api.get(`/inventory-counts/${inventoryCountId}/items/${itemId}/`);
    },

    /**
     * Create a new inventory count item
     * @param {number} inventoryCountId - Inventory count ID
     * @param {Object} itemData - Inventory count item data
     * @param {number} itemData.stock_item - Stock item ID
     * @param {number} itemData.recorded_quantity - Recorded quantity
     * @param {string} itemData.notes - Notes (optional)
     * @returns {Promise} API response
     */
    createInventoryCountItem: (inventoryCountId, itemData) => {
        return api.post(`/inventory-counts/${inventoryCountId}/items/`, itemData);
    },

    /**
     * Update an existing inventory count item
     * @param {number} inventoryCountId - Inventory count ID
     * @param {number} itemId - Inventory count item ID
     * @param {Object} itemData - Updated inventory count item data
     * @returns {Promise} API response
     */
    updateInventoryCountItem: (inventoryCountId, itemId, itemData) => {
        return api.put(`/inventory-counts/${inventoryCountId}/items/${itemId}/`, itemData);
    },

    /**
     * Bulk update multiple inventory count items
     * @param {number} inventoryCountId - Inventory count ID
     * @param {Array} itemsData - Array of item updates
     * @returns {Promise} API response
     */
    bulkUpdateInventoryCountItems: (inventoryCountId, itemsData) => {
        return api.patch(`/inventory-counts/${inventoryCountId}/items/bulk_update/`, itemsData);
    },

    /**
     * Delete an inventory count item
     * @param {number} inventoryCountId - Inventory count ID
     * @param {number} itemId - Inventory count item ID
     * @returns {Promise} API response
     */
    deleteInventoryCountItem: (inventoryCountId, itemId) => {
        return api.delete(`/inventory-counts/${inventoryCountId}/items/${itemId}/`);
    }
};
