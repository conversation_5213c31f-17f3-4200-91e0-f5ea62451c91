import random
from decimal import Decimal

import factory
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from expenses.models.expense import Expense
from utils.test.factories.expenses.expense_category import ExpenseCategoryFactory
from utils.test.factories.pos.pos_session_transaction import (
    POSSessionTransactionFactory,
)


class ExpenseFactory(DjangoModelFactory):
    """
    Factory for creating Expense instances for testing.
    """

    class Meta:
        model = Expense
        skip_postgeneration_save = True

    expense_category = SubFactory(ExpenseCategoryFactory)
    pos_session_transaction = SubFactory(POSSessionTransactionFactory)

    # Generate a random amount between 10 and 200
    amount = LazyAttribute(
        lambda _: Decimal(random.uniform(10, 200)).quantize(Decimal("0.01"))
    )

    description = factory.Faker("paragraph", nb_sentences=1)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure amount matches pos_session_transaction amount."""
        # If pos_session_transaction is provided and amount is not, use its amount
        if "pos_session_transaction" in kwargs and "amount" not in kwargs:
            kwargs["amount"] = kwargs["pos_session_transaction"].amount

        # If amount is provided and pos_session_transaction is not, create a transaction with that amount
        if "amount" in kwargs and "pos_session_transaction" not in kwargs:
            transaction = POSSessionTransactionFactory.create(amount=kwargs["amount"])
            kwargs["pos_session_transaction"] = transaction

        return super()._create(model_class, *args, **kwargs)
