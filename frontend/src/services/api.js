import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

const api = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });

    failedQueue = [];
};

// Request interceptor to add auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('access');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
            if (isRefreshing) {
                // If already refreshing, queue this request
                return new Promise((resolve, reject) => {
                    failedQueue.push({resolve, reject});
                }).then(token => {
                    originalRequest.headers.Authorization = `Bearer ${token}`;
                    return api(originalRequest);
                }).catch(err => {
                    return Promise.reject(err);
                });
            }

            originalRequest._retry = true;
            isRefreshing = true;

            const refreshToken = localStorage.getItem('refresh');

            if (!refreshToken) {
                // No refresh token, redirect to login
                localStorage.removeItem('access');
                localStorage.removeItem('refresh');
                window.location.href = '/login';
                return Promise.reject(error);
            }

            try {
                const response = await axios.post(`${API_URL}/auth/token/refresh/`, {
                    refresh: refreshToken
                });

                const {access, refresh} = response.data;
                localStorage.setItem('access', access);
                if (refresh) {
                    localStorage.setItem('refresh', refresh);
                }

                // Update the authorization header
                api.defaults.headers.common['Authorization'] = `Bearer ${access}`;
                originalRequest.headers.Authorization = `Bearer ${access}`;

                processQueue(null, access);

                return api(originalRequest);
            } catch (refreshError) {
                processQueue(refreshError, null);

                // Refresh failed, clear tokens and redirect to login
                localStorage.removeItem('access');
                localStorage.removeItem('refresh');
                window.location.href = '/login';

                return Promise.reject(refreshError);
            } finally {
                isRefreshing = false;
            }
        }

        return Promise.reject(error);
    }
);

export const authService = {
    login: (username, password) =>
        api.post('/auth/login/', {username, password}),

    refresh: (refreshToken) =>
        api.post('/auth/token/refresh/', {refresh: refreshToken}),

    // Note: There's no /auth/me/ endpoint in the backend
    // User data is returned with login and refresh responses
    getCurrentUser: () => {
        // Since there's no me endpoint, we'll try to refresh the token
        // which will return user data if the token is valid
        const refreshToken = localStorage.getItem('refresh');
        if (refreshToken) {
            return api.post('/auth/token/refresh/', {refresh: refreshToken});
        }
        return Promise.reject(new Error('No refresh token available'));
    },

    // Note: There's no logout endpoint in the backend
    logout: () => Promise.resolve(),
};

export default api;
