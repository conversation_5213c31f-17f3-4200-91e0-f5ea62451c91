import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

/**
 * Delete confirmation modal for inventory counts
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to hide the modal
 * @param {Function} props.onConfirm - Function to confirm deletion
 * @param {Object} props.inventoryCount - Inventory count object to delete
 * @param {boolean} props.loading - Loading state during deletion
 */
const DeleteConfirmModal = ({ 
    show, 
    onHide, 
    onConfirm, 
    inventoryCount, 
    loading = false 
}) => {
    if (!inventoryCount) return null;

    // Get status badge variant
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'draft':
                return 'secondary';
            case 'in_progress':
                return 'primary';
            case 'completed':
                return 'success';
            case 'cancelled':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    // Check if deletion is allowed
    const canDelete = inventoryCount.status === 'draft';

    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Delete</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="mb-3">
                    <h6>Inventory Count Details:</h6>
                    <ul className="list-unstyled">
                        <li><strong>ID:</strong> #{inventoryCount.id}</li>
                        <li><strong>Warehouse:</strong> {inventoryCount.warehouse_name}</li>
                        <li>
                            <strong>Status:</strong>{' '}
                            <Badge bg={getStatusBadgeVariant(inventoryCount.status)}>
                                {inventoryCount.status_display}
                            </Badge>
                        </li>
                        <li><strong>Items:</strong> {inventoryCount.item_count || 0}</li>
                        {inventoryCount.started_at && (
                            <li>
                                <strong>Started:</strong>{' '}
                                {new Date(inventoryCount.started_at).toLocaleString()}
                            </li>
                        )}
                    </ul>
                </div>

                {canDelete ? (
                    <>
                        <p>
                            Are you sure you want to delete this inventory count?
                        </p>
                        <p className="text-muted">
                            This action cannot be undone. The inventory count and all its items 
                            will be permanently removed from the system.
                        </p>
                    </>
                ) : (
                    <div className="alert alert-warning">
                        <strong>Cannot Delete:</strong> Only inventory counts in "Draft" status 
                        can be deleted. This inventory count is currently in "{inventoryCount.status_display}" 
                        status.
                    </div>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button 
                    variant="secondary" 
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                {canDelete && (
                    <Button 
                        variant="danger" 
                        onClick={onConfirm}
                        disabled={loading}
                    >
                        {loading ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Deleting...
                            </>
                        ) : (
                            'Delete Inventory Count'
                        )}
                    </Button>
                )}
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
