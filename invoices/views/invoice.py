from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from invoices.models.invoice import Invoice
from invoices.serializers.invoice import InvoiceReadSerializer, InvoiceSerializer
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly, IsAdminOrCashierOrManager


class InvoiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows invoices to be viewed, created, or edited.

    - Admin/Cashier/Manager: Can create and list invoices
    - Admin only: Can update invoices
    """

    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["warehouse", "pos_session", "payment_method"]
    search_fields = ["notes"]
    ordering_fields = ["created", "net_amount", "profit"]
    ordering = ["-created"]
    http_method_names = [
        "get",
        "post",
        "put",
        "patch",
        "head",
        "options",
        "trace",
    ]

    def get_queryset(self):
        """
        Optionally filter invoices by warehouse
        Example: /api/invoices/?warehouse=1
        """
        queryset = super().get_queryset()
        if self.request.user.is_authenticated and self.request.user.is_admin:
            return queryset
        return queryset.filter(pos_session__pos__employee__user=self.request.user)

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        - Admin/Cashier/Manager can create and list invoices
        - Only Admin can update invoices
        """

        if self.action in ["create"]:
            permission_classes = [IsAdminOrCashierOrManager]
        else:
            permission_classes = [IsAdminOnly]

        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on the action.
        """
        if self.action in ["list", "retrieve"]:
            return InvoiceReadSerializer
        return InvoiceSerializer
