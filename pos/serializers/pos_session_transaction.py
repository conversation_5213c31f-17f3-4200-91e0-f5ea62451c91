from rest_framework import serializers

from pos.models import POSSessionTransaction


class POSSessionTransactionSerializer(serializers.ModelSerializer):
    """
    Serializer for POS Session Transaction model
    """

    transaction_type_display = serializers.CharField(
        source="get_transaction_type_display", read_only=True
    )
    related_object_type = serializers.SerializerMethodField()
    related_object_id = serializers.SerializerMethodField()

    class Meta:
        model = POSSessionTransaction
        fields = [
            "id",
            "session",
            "transaction_type",
            "transaction_type_display",
            "amount",
            "content_type",
            "object_id",
            "related_object_type",
            "related_object_id",
            "description",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
            "related_object_type",
            "related_object_id",
            "content_type",
            "object_id",
        ]

    def get_related_object_type(self, obj):
        """
        Return the model name of the related object if it exists
        """
        if obj.content_type:
            return f"{obj.content_type.app_label}.{obj.content_type.model}"
        return None

    def get_related_object_id(self, obj):
        """
        Return the ID of the related object if it exists
        """
        return obj.object_id if obj.object_id else None
