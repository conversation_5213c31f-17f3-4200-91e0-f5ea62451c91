import React from 'react';
import { Badge } from 'react-bootstrap';
import PropTypes from 'prop-types';

/**
 * SessionStatusBadge component
 * Displays session status with appropriate styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.status - Session status (open, closed, suspended)
 * @param {string} props.className - Additional CSS classes
 */
const SessionStatusBadge = ({ status, className = '' }) => {
    const getStatusVariant = (status) => {
        switch (status?.toLowerCase()) {
            case 'open':
                return 'success';
            case 'closed':
                return 'secondary';
            case 'suspended':
                return 'warning';
            default:
                return 'light';
        }
    };

    const getStatusText = (status) => {
        switch (status?.toLowerCase()) {
            case 'open':
                return 'Open';
            case 'closed':
                return 'Closed';
            case 'suspended':
                return 'Suspended';
            default:
                return 'Unknown';
        }
    };

    return (
        <Badge 
            bg={getStatusVariant(status)} 
            className={className}
            title={`Session Status: ${getStatusText(status)}`}
        >
            {getStatusText(status)}
        </Badge>
    );
};

SessionStatusBadge.propTypes = {
    status: PropTypes.string.isRequired,
    className: PropTypes.string
};

export default SessionStatusBadge;
