import React, { useState, useEffect } from 'react';
import { 
    <PERSON>, 
    But<PERSON>, 
    Card, 
    Alert, 
    Spinner, 
    Row, 
    Col 
} from 'react-bootstrap';
import { useParentCategories } from '../../hooks/useCategories';

const CategoryForm = ({ 
    initialData = {}, 
    onSubmit, 
    loading = false, 
    error = null,
    isEdit = false 
}) => {
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        parent_id: ''
    });
    const [validationErrors, setValidationErrors] = useState({});
    const { parentCategories, loading: parentLoading } = useParentCategories();

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                description: initialData.description || '',
                parent_id: initialData.parent_id || ''
            });
        }
    }, []);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.name.trim()) {
            errors.name = 'Category name is required';
        } else if (formData.name.trim().length < 2) {
            errors.name = 'Category name must be at least 2 characters long';
        } else if (formData.name.trim().length > 100) {
            errors.name = 'Category name must not exceed 100 characters';
        }

        if (formData.description && formData.description.length > 500) {
            errors.description = 'Description must not exceed 500 characters';
        }

        // Prevent self-reference in edit mode
        if (isEdit && formData.parent_id && 
            parseInt(formData.parent_id) === initialData.id) {
            errors.parent_id = 'A category cannot be a parent of itself';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            parent_id: formData.parent_id ? parseInt(formData.parent_id) : null
        };

        onSubmit(submitData);
    };

    // Filter out current category from parent options in edit mode
    const getParentOptions = () => {
        if (!isEdit) return parentCategories;
        
        return parentCategories.filter(cat => cat.id !== initialData.id);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Category' : 'Create New Category'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Category Name <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.name}
                                    placeholder="Enter category name"
                                    maxLength={100}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.name}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    {formData.name.length}/100 characters
                                </Form.Text>
                            </Form.Group>
                        </Col>

                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Parent Category</Form.Label>
                                {parentLoading ? (
                                    <div className="d-flex align-items-center">
                                        <Spinner size="sm" className="me-2" />
                                        <span>Loading parent categories...</span>
                                    </div>
                                ) : (
                                    <Form.Select
                                        name="parent_id"
                                        value={formData.parent_id}
                                        onChange={handleChange}
                                        isInvalid={!!validationErrors.parent_id}
                                    >
                                        <option value="">No parent (Root category)</option>
                                        {getParentOptions().map(category => (
                                            <option key={category.id} value={category.id}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </Form.Select>
                                )}
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.parent_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Select a parent category to create a subcategory
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Form.Group className="mb-3">
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={4}
                            name="description"
                            value={formData.description}
                            onChange={handleChange}
                            isInvalid={!!validationErrors.description}
                            placeholder="Enter category description (optional)"
                            maxLength={500}
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.description}
                        </Form.Control.Feedback>
                        <Form.Text className="text-muted">
                            {formData.description.length}/500 characters
                        </Form.Text>
                    </Form.Group>

                    <div className="d-flex gap-2">
                        <Button 
                            type="submit" 
                            variant="primary"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Category' : 'Create Category'
                            )}
                        </Button>
                        
                        <Button 
                            type="button" 
                            variant="secondary"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default CategoryForm;
