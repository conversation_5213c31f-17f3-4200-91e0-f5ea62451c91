import random
from decimal import Decimal

import factory
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from pos.models.pos_session import POSSession
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.user import UserFactory


class POSSessionFactory(DjangoModelFactory):
    """
    Factory for creating POSSession instances for testing.
    """

    class Meta:
        model = POSSession
        skip_postgeneration_save = True

    pos = SubFactory(POSFactory)
    user = SubFactory(UserFactory)

    # Generate a random opening balance between 100 and 1000
    opening_balance = LazyAttribute(
        lambda _: Decimal(random.uniform(100, 1000)).quantize(Decimal("0.01"))
    )

    # Default values
    total_sales = Decimal("0.00")
    total_expenses = Decimal("0.00")
    difference = Decimal("0.00")
    status = POSSession.Status.OPEN
    notes = factory.Faker("paragraph", nb_sentences=1)

    # No closing_balance or closed_at for open sessions
    closing_balance = None
    closed_at = None
