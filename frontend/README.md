# BNPL Payment System Frontend

A React-based frontend for the Buy Now Pay Later (BNPL) payment system. This application allows merchants to create payment plans and clients to view and manage their installments.

## Features

### For Merchants
- Create payment plans with flexible installment options
- View all created payment plans and their status
- Track installment payments
- Access analytics dashboard with revenue, plan status, and success rate metrics

### For Clients
- View all payment plans and installments
- Track payment progress with visual indicators
- Make payments on pending installments
- See upcoming and overdue payments

## Technologies Used

- React 18
- React Router for navigation
- React Bootstrap for UI components
- Axios for API communication
- Context API for state management

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Backend API running on http://localhost:8000

### Installation

1. Clone the repository
2. Navigate to the frontend directory:
   ```
   cd frontend
   ```
3. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn
   ```
4. Start the development server:
   ```
   npm start
   ```
   or
   ```
   yarn start
   ```
5. Open your browser and navigate to http://localhost:5173

## Project Structure

- `/src/components/auth`: Authentication components (Login, Register)
- `/src/components/client`: Client-specific components
- `/src/components/merchant`: Merchant-specific components
- `/src/components/layout`: Shared layout components
- `/src/contexts`: React context providers
- `/src/services`: API service functions

## Security Considerations

- Token-based authentication
- Protected routes based on user roles
- Secure storage of authentication tokens
- Input validation on forms

## Trade-offs and Future Improvements

- Add more comprehensive error handling
- Implement form validation using a library like Formik or React Hook Form
- Add unit and integration tests
- Implement real-time updates using WebSockets
- Add more detailed analytics and reporting features
