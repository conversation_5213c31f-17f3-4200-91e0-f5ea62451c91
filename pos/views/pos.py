from django.utils import timezone
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import <PERSON>Filter
from rest_framework.response import Response

from pos.models import POS, POSSession
from pos.serializers import POSSerializer, POSSessionSerializer
from pos.serializers.pos_session import POSStartSessionSerializer
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly, IsAdminOrCashierOrManager, IsCashierOrManager


class POSViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing POS terminals.
    """

    queryset = POS.objects.all()
    serializer_class = POSSerializer
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["warehouse"]
    search_fields = ["name", "description"]

    def get_queryset(self):
        """
        Filter POS by user if needed
        """
        queryset = super().get_queryset()
        if not self.request.user.is_admin:
            return queryset.filter(employee__user=self.request.user)
        return queryset

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """

        if self.action == "start_session":
            permission_classes = [IsCashierOrManager]
        elif self.action == "list":
            permission_classes = [IsAdminOrCashierOrManager]
        else:
            permission_classes = [IsAdminOnly]
        return [permission() for permission in permission_classes]

    def destroy(self, request, *args, **kwargs):
        """
        Override destroy to check if POS has any sessions before deletion
        """
        instance = self.get_object()
        if instance.sessions.exists():
            return Response(
                {"detail": "Cannot delete POS with existing sessions"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return super().destroy(request, *args, **kwargs)

    def get_serializer_class(self):
        if self.action == "start_session":
            return POSStartSessionSerializer
        return super().get_serializer_class()

    @swagger_auto_schema(
        request_body=POSStartSessionSerializer,
        responses={
            200: openapi.Response(
                description="Session created successfully",
                schema=POSSessionSerializer(),
            ),
        },
    )
    @action(detail=True, methods=["post"], url_path="start_session")
    def start_session(self, request, *args, **kwargs):
        """
        Start a new POS session
        """
        pos = self.get_object()
        opening_balance = request.data.get("opening_balance")

        # Check if there's already an open session for this POS
        if POSSession.objects.filter(
            pos=pos, status__in=[POSSession.Status.OPEN, POSSession.Status.SUSPENDED]
        ).exists():
            return Response(
                {"detail": "There is already an open session for this POS"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create a new session
        session = POSSession.objects.create(
            pos=pos,
            user=request.user,
            opening_balance=opening_balance,
            status=POSSession.Status.OPEN,
            opened_at=timezone.now(),
        )

        return Response(
            POSSessionSerializer(session).data, status=status.HTTP_201_CREATED
        )
