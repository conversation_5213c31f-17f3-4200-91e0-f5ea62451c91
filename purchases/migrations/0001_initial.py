# Generated by Django 5.2.1 on 2025-07-21 07:46

import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0003_account_account_type_account_name"),
        ("products", "0002_alter_product_cost_alter_product_price"),
        ("warehouses", "0005_alter_inventorycountitem_difference_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Purchase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total amount of the purchase before discount",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                        verbose_name="total amount",
                    ),
                ),
                (
                    "discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Discount amount applied to the purchase",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.0)],
                        verbose_name="discount",
                    ),
                ),
                (
                    "bonus",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Bonus amount applied to the purchase",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.0)],
                        verbose_name="bonus",
                    ),
                ),
                (
                    "net_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Net amount after discount (total_amount - discount)",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                        verbose_name="net amount",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[("cash", "Cash")],
                        default="cash",
                        help_text="Method of payment for this purchase",
                        max_length=20,
                        verbose_name="payment method",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this purchase",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse where the purchased items will be stored",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchases",
                        to="warehouses.warehouse",
                        verbose_name="warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "purchase",
                "verbose_name_plural": "purchases",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        default=1,
                        help_text="Quantity of the product",
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                        verbose_name="quantity",
                    ),
                ),
                (
                    "unit_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Cost per unit of the product",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                        verbose_name="unit cost",
                    ),
                ),
                (
                    "total_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total cost (quantity × unit_cost)",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                        verbose_name="total cost",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this purchase item",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        help_text="The product being purchased",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchase_items",
                        to="products.product",
                        verbose_name="product",
                    ),
                ),
                (
                    "purchase",
                    models.ForeignKey(
                        help_text="The purchase this item belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="purchases.purchase",
                        verbose_name="purchase",
                    ),
                ),
            ],
            options={
                "verbose_name": "purchase item",
                "verbose_name_plural": "purchase items",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="Supplier",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Enter the supplier name",
                        max_length=200,
                        verbose_name="name",
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        help_text="Enter the supplier's phone number",
                        max_length=20,
                        verbose_name="phone number",
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        help_text="The financial account associated with this supplier",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suppliers",
                        to="accounts.account",
                        verbose_name="account",
                    ),
                ),
            ],
            options={
                "verbose_name": "supplier",
                "verbose_name_plural": "suppliers",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="purchase",
            name="supplier",
            field=models.ForeignKey(
                help_text="The supplier from whom the purchase is made",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="purchases",
                to="purchases.supplier",
                verbose_name="supplier",
            ),
        ),
    ]
