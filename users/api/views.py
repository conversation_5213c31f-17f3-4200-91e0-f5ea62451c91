from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .serializers import (
    UserLoginSerializer,
    CustomTokenObtainPairSerializer,
    TokenRefreshResponseSerializer,
)


class UserLoginView(APIView):
    """
    API endpoint that allows users to log in with email/phone and password.
    Returns JWT access and refresh tokens on successful authentication.
    """

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Login with email/phone and password to get JWT tokens",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["username", "password"],
            properties={
                "username": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Email or phone number"
                ),
                "password": openapi.Schema(
                    type=openapi.TYPE_STRING, description="User password"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Successful authentication",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "access": openapi.Schema(
                            type=openapi.TYPE_STRING, description="JWT access token"
                        ),
                        "refresh": openapi.Schema(
                            type=openapi.TYPE_STRING, description="JWT refresh token"
                        ),
                        "user": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_INTEGER, description="User ID"
                                ),
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, description="User email"
                                ),
                                "phone_number": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="User phone number",
                                ),
                                "role": openapi.Schema(
                                    type=openapi.TYPE_STRING, description="User role"
                                ),
                                "name": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="User name",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: "Invalid input",
            401: "Invalid credentials",
        },
    )
    def post(self, request, *args, **kwargs):
        serializer = UserLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data["user"]
        token_serializer = CustomTokenObtainPairSerializer()
        token = token_serializer.get_token(user)

        response_data = {
            "access": str(token.access_token),
            "refresh": str(token),
            "user": {
                "id": user.id,
                "email": user.email,
                "phone_number": user.phone_number,
                "role": user.role,
                "name": user.name,
            },
        }

        return Response(response_data, status=status.HTTP_200_OK)


class CustomTokenRefreshView(TokenRefreshView):
    """
    Custom token refresh view that includes the user data in the response.
    Provide a valid refresh token to get a new access token.
    """

    @swagger_auto_schema(
        operation_description="Refresh JWT token",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["refresh"],
            properties={
                "refresh": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Valid refresh token"
                )
            },
        ),
        responses={
            200: openapi.Response(
                description="Token refreshed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "access": openapi.Schema(
                            type=openapi.TYPE_STRING, description="New JWT access token"
                        ),
                        "refresh": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description="New JWT refresh token",
                        ),
                        "user": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_INTEGER, description="User ID"
                                ),
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, description="User email"
                                ),
                                "phone_number": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="User phone number",
                                ),
                                "role": openapi.Schema(
                                    type=openapi.TYPE_STRING, description="User role"
                                ),
                                "name": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description="User name",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: "Invalid token",
            401: "Token expired or invalid",
        },
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Get the user from the refresh token
        refresh = RefreshToken(serializer.validated_data["refresh"])
        user_id = refresh.payload.get("user_id")

        # Get the user
        from django.contrib.auth import get_user_model

        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response(
                {"detail": "User not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # Generate new tokens
        token_serializer = CustomTokenObtainPairSerializer()
        token = token_serializer.get_token(user)

        response_data = {
            "access": str(token.access_token),
            "refresh": str(token),
            "user": {
                "id": user.id,
                "email": user.email,
                "phone_number": user.phone_number,
                "role": user.role,
                "name": user.name,
            },
        }

        return Response(response_data, status=status.HTTP_200_OK)
