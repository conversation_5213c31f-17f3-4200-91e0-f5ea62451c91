# Categories CRUD Implementation

This directory contains the complete implementation of Categories CRUD functionality for the ERP frontend application.

## Overview

The categories system allows users to create, read, update, and delete product categories with hierarchical support (parent-child relationships). The implementation follows React best practices and uses React Bootstrap for styling.

## Features

- ✅ **List Categories**: Paginated table with search and sorting
- ✅ **Create Category**: Form to add new categories with validation
- ✅ **View Category**: Detailed view of category information
- ✅ **Edit Category**: Update existing categories
- ✅ **Delete Category**: Remove categories with confirmation
- ✅ **Hierarchical Support**: Parent-child category relationships
- ✅ **Search & Filter**: Search by name/description, sort by various fields
- ✅ **Pagination**: Handle large datasets efficiently
- ✅ **Error Handling**: Comprehensive error states and messages
- ✅ **Loading States**: User feedback during API operations
- ✅ **Responsive Design**: Mobile-friendly interface
- ✅ **Role-based Access**: Admin-only operations for create/edit/delete

## File Structure

```
categories/
├── README.md                    # This documentation
├── CategoriesList.jsx          # Main list view with table, search, pagination
├── CategoryForm.jsx            # Reusable form component for create/edit
├── CategoryCreate.jsx          # Create category page
├── CategoryEdit.jsx            # Edit category page
├── CategoryView.jsx            # View category details page
├── DeleteConfirmModal.jsx      # Delete confirmation modal
└── __tests__/                  # Test files
    ├── CategoriesList.test.jsx
    ├── CategoryForm.test.jsx
    └── ...
```

## Components

### CategoriesList
- **Purpose**: Main listing page for categories
- **Features**: 
  - Paginated table display
  - Search functionality
  - Column sorting
  - Action buttons (View, Edit, Delete)
  - Loading and error states
- **Props**: None (uses hooks for data management)

### CategoryForm
- **Purpose**: Reusable form for creating and editing categories
- **Features**:
  - Form validation
  - Character counting
  - Parent category selection
  - Error display
  - Loading states
- **Props**:
  - `initialData`: Pre-fill data for edit mode
  - `onSubmit`: Form submission handler
  - `loading`: Loading state
  - `error`: Error message
  - `isEdit`: Boolean to determine create vs edit mode

### CategoryCreate
- **Purpose**: Page wrapper for creating new categories
- **Features**:
  - Uses CategoryForm component
  - Success message and redirect
  - Error handling

### CategoryEdit
- **Purpose**: Page wrapper for editing existing categories
- **Features**:
  - Fetches category data by ID
  - Uses CategoryForm component
  - Success message and redirect
  - Error handling

### CategoryView
- **Purpose**: Display detailed category information
- **Features**:
  - Formatted category details
  - Action buttons
  - Error handling for not found categories

### DeleteConfirmModal
- **Purpose**: Confirmation dialog for category deletion
- **Features**:
  - Warning message
  - Loading state during deletion
  - Cancel/confirm actions

## API Integration

### Service Layer (`categoriesService.js`)
Handles all API communications:
- `getCategories(params)` - List categories with filtering
- `getCategory(id)` - Get single category
- `createCategory(data)` - Create new category
- `updateCategory(id, data)` - Update existing category
- `deleteCategory(id)` - Delete category
- `getParentCategories()` - Get root categories
- `getSubcategories(parentId)` - Get child categories

### Custom Hooks (`useCategories.js`)
State management and API integration:
- `useCategories()` - Main hook for categories CRUD operations
- `useCategory(id)` - Hook for single category management
- `useParentCategories()` - Hook for parent categories

## Routing

The following routes are configured in `App.jsx`:

- `/categories` - Categories list (authenticated users)
- `/categories/create` - Create category (admin only)
- `/categories/:id` - View category (authenticated users)
- `/categories/:id/edit` - Edit category (admin only)

## Permissions

- **List/View**: All authenticated users
- **Create/Edit/Delete**: Admin users only

## Validation

### Client-side Validation
- Category name: Required, 2-100 characters
- Description: Optional, max 500 characters
- Parent category: Cannot be self-referential

### Server-side Validation
- Handled by Django backend
- Prevents deletion of categories with products or subcategories
- Validates circular references

## Error Handling

### User-friendly Error Messages
- Network errors
- Validation errors
- Permission errors
- Not found errors

### Error States
- Loading spinners during API calls
- Error alerts with dismissible messages
- Fallback UI for failed operations

## Testing

### Test Coverage
- Component rendering tests
- User interaction tests
- Form validation tests
- API integration tests
- Error scenario tests

### Running Tests
```bash
npm test -- --testPathPattern=categories
```

## Usage Examples

### Basic Usage
```jsx
import CategoriesList from './components/categories/CategoriesList';

function App() {
  return <CategoriesList />;
}
```

### Custom Form Usage
```jsx
import CategoryForm from './components/categories/CategoryForm';

function CustomCategoryPage() {
  const handleSubmit = (data) => {
    console.log('Category data:', data);
  };

  return (
    <CategoryForm
      onSubmit={handleSubmit}
      loading={false}
      error={null}
      isEdit={false}
    />
  );
}
```

## Performance Considerations

- **Pagination**: Large datasets are paginated (10 items per page)
- **Debounced Search**: Search is triggered on form submission to avoid excessive API calls
- **Optimistic Updates**: Local state updates for better UX
- **Error Boundaries**: Prevent crashes from component errors

## Accessibility

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Progressive enhancement

## Future Enhancements

- Bulk operations (multi-select delete)
- Category import/export
- Advanced filtering options
- Category analytics
- Drag-and-drop reordering
- Category images/icons
