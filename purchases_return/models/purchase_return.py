from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from pos.models import POSSession
from purchases.models.supplier import Supplier
from warehouses.models import Warehouse


class PurchaseReturn(TimeStampedModel):
    """
    Purchase Return model representing returns made to suppliers.
    Each return is associated with a warehouse and supplier, and includes
    financial details such as total amount and payment method.
    """

    class PaymentMethod(models.TextChoices):
        CASH = "cash", _("Cash")
        CREDIT = "credit", _("Credit")
        CARD = "card", _("Card")
        BANK_TRANSFER = "bank_transfer", _("Bank Transfer")

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name="purchase_returns",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse from which items are being returned"),
    )

    pos_session = models.ForeignKey(
        POSSession,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="purchase_returns",
        verbose_name=_("POS session"),
        help_text=_("The POS session associated with this return (optional)"),
    )

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name="purchase_returns",
        verbose_name=_("supplier"),
        help_text=_("The supplier to whom the items are being returned"),
    )

    total_amount = models.DecimalField(
        _("total amount"),
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0.00)],
        help_text=_("Total amount of the return (calculated from items)"),
    )
    product_total_cost = models.DecimalField(
        _("product total cost"),
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0.00)],
        help_text=_("Total cost of products (calculated from items)"),
    )
    payment_method = models.CharField(
        _("payment method"),
        max_length=50,
        choices=PaymentMethod.choices,
        default=PaymentMethod.CASH,
        help_text=_("Method of payment for the return"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        help_text=_("Additional notes about this purchase return"),
    )

    class Meta:
        verbose_name = _("purchase return")
        verbose_name_plural = _("purchase returns")
        ordering = ["-created"]

    def __str__(self):
        return f"Return {self.id} - {self.supplier.name} - ${self.total_amount}"

    def calculate_total_amount(self):
        """Calculate and return the total amount from all items."""
        return sum(item.total_cost for item in self.items.all())
