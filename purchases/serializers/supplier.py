from django.contrib.auth import get_user_model
from rest_framework import serializers

from accounts.models.account import Account
from purchases.models.supplier import Supplier

User = get_user_model()


class SupplierSerializer(serializers.ModelSerializer):
    """
    Serializer for the Supplier model.
    Handles creation and updating of Supplier instances along with their associated Account.
    """

    class Meta:
        model = Supplier
        fields = ["id", "name", "phone_number", "account", "created", "modified"]
        read_only_fields = ["id", "created", "modified", "account"]

    def create(self, validated_data):
        """
        Create a new Supplier and its associated Account.
        The account name will be the same as the supplier name.
        """
        # Create the account first
        account = Account.objects.create(
            name=validated_data["name"], account_type="Supplier"
        )

        # Create the supplier with the new account
        supplier = Supplier.objects.create(
            name=validated_data["name"],
            phone_number=validated_data["phone_number"],
            account=account,
        )
        return supplier

    def update(self, instance, validated_data):
        """
        Update an existing Supplier and its associated Account.
        """
        # Update supplier fields
        instance.name = validated_data.get("name", instance.name)
        instance.phone_number = validated_data.get(
            "phone_number", instance.phone_number
        )

        # Update the associated account name if the supplier name changed
        if "name" in validated_data and instance.account:
            instance.account.name = validated_data["name"]
            instance.account.save()

        instance.save()
        return instance
