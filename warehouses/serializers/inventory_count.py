from rest_framework import serializers

from ..models import InventoryCount


class InventoryCountSerializer(serializers.ModelSerializer):
    """Serializer for inventory counts."""

    status_display = serializers.CharField(source="get_status_display", read_only=True)
    warehouse_name = serializers.CharField(source="warehouse.name", read_only=True)
    item_count = serializers.IntegerField(source="items.count", read_only=True)

    class Meta:
        model = InventoryCount
        fields = [
            "id",
            "warehouse",
            "warehouse_name",
            "status",
            "status_display",
            "started_at",
            "ended_at",
            "noted_by",
            "notes",
            "created",
            "modified",
            "item_count",
            "total_cost",
            "total_price",
        ]
        read_only_fields = [
            "id",
            "status",
            "status_display",
            "started_at",
            "ended_at",
            "noted_by",
            "notes",
            "created",
            "modified",
            "item_count",
            "warehouse_name",
            "total_cost",
            "total_price",
        ]

    def validate(self, attrs):
        inventory_count = InventoryCount.objects.filter(
            warehouse=attrs["warehouse"],
            status__in=[InventoryCount.Status.IN_PROGRESS, InventoryCount.Status.DRAFT],
        ).first()
        if inventory_count:
            raise serializers.ValidationError(
                "An inventory count is already in progress or draft in this warehouse"
            )
        return attrs
