from django.urls import reverse
from rest_framework import status

from utils.test.base_test import BaseTestCase
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models.warehouse import Warehouse


class WarehouseViewSetTestCase(BaseTestCase):
    """Test cases for WarehouseViewSet"""

    def setUp(self):
        super().setUp()
        self.list_url = reverse("warehouses:warehouse-list")
        self.detail_url_name = "warehouses:warehouse-detail"
        # Create test data using factories
        self.warehouse = WarehouseFactory()
        self.detail_url = reverse(
            self.detail_url_name, kwargs={"pk": self.warehouse.pk}
        )

    def test_list_warehouses_unauthenticated(self):
        """Test that unauthenticated users cannot list warehouses"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_warehouses_authenticated(self):
        """Test that admin users can list warehouses"""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_list_warehouses_unauthorized(self):
        """Test that admin users can list warehouses"""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_warehouse(self):
        """Test retrieving a single warehouse"""
        response = self.admin_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.warehouse.name)

    def test_create_warehouse_unauthenticated(self):
        """Test that unauthenticated users cannot create warehouses"""
        data = {
            "name": "New Warehouse",
            "location": "123 Test St, Test City",
            "description": "A test warehouse",
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_warehouse_unauthorized(self):
        """Test that non-admin users cannot create warehouses"""
        data = {
            "name": "New Warehouse",
            "location": "123 Test St, Test City",
            "description": "A test warehouse",
        }
        # Test manager cannot create
        response = self.manager_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot create
        response = self.cashier_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_warehouse_admin(self):
        """Test that admin users can create warehouses"""
        data = {
            "name": "New Warehouse",
            "location": "123 Test St, Test City",
            "description": "A test warehouse",
        }
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Warehouse.objects.count(), 3)
        self.assertEqual(response.data["name"], data["name"])

    def test_update_warehouse_admin(self):
        """Test that admin users can update warehouses"""
        data = {"name": "Updated Warehouse Name"}
        response = self.admin_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.warehouse.refresh_from_db()
        self.assertEqual(self.warehouse.name, data["name"])

    def test_update_warehouse_unauthorized(self):
        """Test that non-admin users cannot update warehouses"""
        data = {"name": "Unauthorized Update"}

        # Test manager cannot update
        response = self.manager_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot update
        response = self.cashier_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_warehouse_admin(self):
        """Test that admin users can delete warehouses"""
        response = self.admin_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Warehouse.objects.count(), 1)

    def test_delete_warehouse_unauthorized(self):
        """Test that non-admin users cannot delete warehouses"""
        # Test manager cannot delete
        response = self.manager_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot delete
        response = self.cashier_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_search_warehouses(self):
        """Test searching warehouses by name and location"""
        # Create a warehouse with a unique name and location
        unique_warehouse = WarehouseFactory(
            name="Unique Test Warehouse", location="789 Test Ave, Test City"
        )

        # Search by name
        response = self.admin_client.get(self.list_url, {"search": "Unique Test"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], unique_warehouse.id)

        # Search by location
        response = self.admin_client.get(self.list_url, {"search": "Test Ave"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], unique_warehouse.id)

    def test_ordering_warehouses(self):
        """Test ordering warehouses by name and created date"""
        # Create additional test warehouses
        WarehouseFactory(name="A Warehouse")
        WarehouseFactory(name="Z Warehouse")

        # Test ordering by name ascending
        response = self.admin_client.get(self.list_url, {"ordering": "name"})
        results = response.data["results"]
        self.assertEqual(results[0]["name"], "A Warehouse")

        # Test ordering by name descending
        response = self.admin_client.get(self.list_url, {"ordering": "-name"})
        results = response.data["results"]
        self.assertEqual(results[0]["name"], "Z Warehouse")
