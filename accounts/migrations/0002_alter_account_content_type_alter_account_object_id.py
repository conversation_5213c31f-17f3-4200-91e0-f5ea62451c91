# Generated by Django 5.2.1 on 2025-07-16 08:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0001_initial"),
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.AlterField(
            model_name="account",
            name="content_type",
            field=models.ForeignKey(
                blank=True,
                help_text="The model type of the related entity",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="account_owner",
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AlterField(
            model_name="account",
            name="object_id",
            field=models.PositiveIntegerField(
                blank=True, help_text="ID of the related entity", null=True
            ),
        ),
    ]
