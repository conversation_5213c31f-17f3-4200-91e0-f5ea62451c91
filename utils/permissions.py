from rest_framework import permissions


class IsAdminOrManager(permissions.BasePermission):
    """
    Custom permission to only allow admin or manager users to create/update/delete.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_admin or request.user.is_manager
        )


class IsAdminOnly(permissions.BasePermission):
    """
    Custom permission to only allow admin users to update/delete.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_admin


class IsCashierOrManager(permissions.BasePermission):
    """
    Custom permission to only allow admin or manager users to create/update/delete.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_cashier or request.user.is_manager
        )


class IsAdminOrCashierOrManager(permissions.BasePermission):
    """
    Custom permission to only allow admin or manager users to create/update/delete.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_cashier or request.user.is_manager or request.user.is_admin
        )
