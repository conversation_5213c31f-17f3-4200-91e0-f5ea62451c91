from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly
from ..models import Warehouse
from ..serializers.warehouse import WarehouseSerializer


class WarehouseViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows warehouses to be viewed or edited.
    """
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    pagination_class = PaginationClass
    permission_classes = [IsAuthenticated, IsAdminOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'location', 'description']
    ordering_fields = ['name', 'created', 'modified']
    ordering = ['name']

