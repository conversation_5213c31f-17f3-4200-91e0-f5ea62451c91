# Generated by Django 5.2.1 on 2025-07-16 08:42

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("employees", "0002_alter_attendance_time_in_image_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="employee",
            name="day_rate",
            field=models.DecimalField(
                decimal_places=2,
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
            ),
        ),
        migrations.AlterField(
            model_name="employee",
            name="hour_rate",
            field=models.DecimalField(
                decimal_places=2,
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
            ),
        ),
    ]
