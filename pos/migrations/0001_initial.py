# Generated by Django 5.2.1 on 2025-06-23 11:50

import django.db.models.deletion
import django_extensions.db.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("warehouses", "0004_inventorycount_total_price_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="POS",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the POS terminal",
                        max_length=100,
                        unique=True,
                        verbose_name="name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Additional information about the POS terminal",
                        null=True,
                        verbose_name="description",
                    ),
                ),
                (
                    "warehouse",
                    models.OneToOneField(
                        help_text="The warehouse this POS terminal is associated with",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pos",
                        to="warehouses.warehouse",
                        verbose_name="warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "point of sale",
                "verbose_name_plural": "points of sale",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="POSSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "opening_balance",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of money at the beginning of the session",
                        max_digits=12,
                        verbose_name="opening balance",
                    ),
                ),
                (
                    "closing_balance",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Amount of money at the end of the session",
                        max_digits=12,
                        null=True,
                        verbose_name="closing balance",
                    ),
                ),
                (
                    "total_sales",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total sales amount during the session",
                        max_digits=12,
                        verbose_name="total sales",
                    ),
                ),
                (
                    "total_expenses",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total expenses during the session",
                        max_digits=12,
                        verbose_name="total expenses",
                    ),
                ),
                (
                    "difference",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total difference between calculated balance and closing balance",
                        max_digits=12,
                        verbose_name="difference",
                    ),
                ),
                (
                    "opened_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When the session was opened",
                        verbose_name="opened at",
                    ),
                ),
                (
                    "closed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the session was closed",
                        null=True,
                        verbose_name="closed at",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about the session",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "Open"),
                            ("closed", "Closed"),
                            ("suspended", "Suspended"),
                        ],
                        default="open",
                        help_text="Current status of the session",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "pos",
                    models.ForeignKey(
                        help_text="The POS terminal this session belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="pos.pos",
                        verbose_name="POS device",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="The user who opened the session",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="pos_sessions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user",
                    ),
                ),
            ],
            options={
                "verbose_name": "POS session",
                "verbose_name_plural": "POS sessions",
                "ordering": ["-opened_at"],
            },
        ),
        migrations.CreateModel(
            name="POSSessionTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("sale", "Sale"),
                            ("refund", "Refund"),
                            ("purchase", "Purchase"),
                            ("refund_purchase", "Refund Purchase"),
                            ("cash_in", "Cash In"),
                            ("cash_out", "Cash Out"),
                            ("expense", "Expense"),
                            ("salary", "Salary"),
                            ("other", "Other"),
                        ],
                        help_text="Type of the transaction",
                        max_length=20,
                        verbose_name="transaction type",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Transaction amount",
                        max_digits=12,
                        verbose_name="amount",
                    ),
                ),
                (
                    "object_id",
                    models.PositiveIntegerField(
                        blank=True, help_text="ID of the related object", null=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Additional information about the transaction",
                        null=True,
                        verbose_name="description",
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        help_text="Content type of the related object",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        help_text="The POS session this transaction belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="pos.possession",
                        verbose_name="session",
                    ),
                ),
            ],
            options={
                "verbose_name": "POS session transaction",
                "verbose_name_plural": "POS session transactions",
                "ordering": ["-created"],
            },
        ),
    ]
