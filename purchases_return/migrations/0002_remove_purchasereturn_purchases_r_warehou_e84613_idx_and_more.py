# Generated by Django 5.2.1 on 2025-07-29 08:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("purchases_return", "0001_initial"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="purchasereturn",
            name="purchases_r_warehou_e84613_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchasereturn",
            name="purchases_r_supplie_ac3c59_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchasereturn",
            name="purchases_r_pos_ses_0fd017_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchasereturnitem",
            name="purchases_r_purchas_9d6c15_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchasereturnitem",
            name="purchases_r_product_3f3a08_idx",
        ),
        migrations.AddField(
            model_name="purchasereturn",
            name="product_total_cost",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total cost of products (calculated from items)",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="product total cost",
            ),
        ),
    ]
