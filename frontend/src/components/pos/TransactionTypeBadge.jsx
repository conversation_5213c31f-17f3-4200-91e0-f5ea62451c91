import React from 'react';
import { Badge } from 'react-bootstrap';
import PropTypes from 'prop-types';

/**
 * TransactionTypeBadge component
 * Displays transaction type with appropriate styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.type - Transaction type
 * @param {string} props.className - Additional CSS classes
 */
const TransactionTypeBadge = ({ type, className = '' }) => {
    const getTypeVariant = (type) => {
        switch (type?.toLowerCase()) {
            case 'sale':
                return 'success';
            case 'refund':
                return 'danger';
            case 'purchase':
                return 'primary';
            case 'refund_purchase':
                return 'info';
            case 'cash_in':
                return 'success';
            case 'cash_out':
                return 'warning';
            case 'expense':
                return 'danger';
            case 'salary':
                return 'secondary';
            case 'other':
                return 'light';
            default:
                return 'light';
        }
    };

    const getTypeText = (type) => {
        switch (type?.toLowerCase()) {
            case 'sale':
                return 'Sale';
            case 'refund':
                return 'Refund';
            case 'purchase':
                return 'Purchase';
            case 'refund_purchase':
                return 'Refund Purchase';
            case 'cash_in':
                return 'Cash In';
            case 'cash_out':
                return 'Cash Out';
            case 'expense':
                return 'Expense';
            case 'salary':
                return 'Salary';
            case 'other':
                return 'Other';
            default:
                return 'Unknown';
        }
    };

    const getTypeIcon = (type) => {
        switch (type?.toLowerCase()) {
            case 'sale':
                return '💰';
            case 'refund':
                return '↩️';
            case 'purchase':
                return '🛒';
            case 'refund_purchase':
                return '🔄';
            case 'cash_in':
                return '⬆️';
            case 'cash_out':
                return '⬇️';
            case 'expense':
                return '💸';
            case 'salary':
                return '👤';
            case 'other':
                return '📝';
            default:
                return '❓';
        }
    };

    return (
        <Badge 
            bg={getTypeVariant(type)} 
            className={className}
            title={`Transaction Type: ${getTypeText(type)}`}
        >
            <span className="me-1">{getTypeIcon(type)}</span>
            {getTypeText(type)}
        </Badge>
    );
};

TransactionTypeBadge.propTypes = {
    type: PropTypes.string.isRequired,
    className: PropTypes.string
};

export default TransactionTypeBadge;
