from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class Category(TimeStampedModel):
    """
    Category model for organizing products hierarchically.
    A category can have a parent category, allowing for nested categories.
    """

    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="children",
        verbose_name=_("parent category"),
        help_text=_("Select a parent category if this is a sub-category"),
    )

    name = models.CharField(
        _("name"), max_length=100, help_text=_("Enter the category name")
    )

    description = models.TextField(
        _("description"),
        blank=True,
        null=True,
        help_text=_("Enter a description for the category (optional)"),
    )

    class Meta:
        verbose_name = _("category")
        verbose_name_plural = _("categories")
        ordering = ["name"]

    def __str__(self):
        return self.name

    def get_full_path(self):
        """
        Returns the full path of the category including all parent categories
        Example: "Electronics > Computers > Laptops"
        """
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name
