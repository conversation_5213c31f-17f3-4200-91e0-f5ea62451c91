import React, {createContext, useContext, useEffect, useState} from 'react';
import {authService} from '../services/api';

// Utility function to check if token is expired
const isTokenExpired = (token) => {
    if (!token) return true;

    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Date.now() / 1000;

        // Check if token expires within the next 5 minutes
        return payload.exp < (currentTime + 300);
    } catch (error) {
        return true;
    }
};


const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({children}) => {
    const [currentUser, setCurrentUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const initializeAuth = async () => {
            const token = localStorage.getItem('access');
            const refreshToken = localStorage.getItem('refresh');

            if (token && refreshToken) {
                // Check if token is expired
                if (isTokenExpired(token)) {
                    // Token is expired, try to refresh
                    try {
                        const response = await authService.refresh(refreshToken);
                        localStorage.setItem('access', response.data.access);
                        if (response.data.refresh) {
                            localStorage.setItem('refresh', response.data.refresh);
                        }
                        // Set user data from refresh response
                        setCurrentUser(response.data.user);
                        setLoading(false);
                    } catch (err) {
                        // Refresh failed, clear tokens
                        localStorage.removeItem('access');
                        localStorage.removeItem('refresh');
                        setCurrentUser(null);
                        setLoading(false);
                    }
                } else {
                    // Token is still valid, get current user
                    await fetchCurrentUser();
                }
            } else {
                setLoading(false);
            }
        };

        initializeAuth();
    }, []);

    const fetchCurrentUser = async () => {
        try {
            setLoading(true);
            const response = await authService.getCurrentUser();

            // The response contains both new tokens and user data
            if (response.data.access) {
                localStorage.setItem('access', response.data.access);
            }
            if (response.data.refresh) {
                localStorage.setItem('refresh', response.data.refresh);
            }

            setCurrentUser(response.data.user);
            setError(null);
        } catch (err) {
            // If getCurrentUser fails (token refresh failed), clear everything
            localStorage.removeItem('access');
            localStorage.removeItem('refresh');
            setCurrentUser(null);
            setError('Session expired. Please login again.');
        } finally {
            setLoading(false);
        }
    };

    const login = async (username, password) => {
        try {
            setLoading(true);
            const response = await authService.login(username, password);
            localStorage.setItem('access', response.data.access);
            localStorage.setItem('refresh', response.data.refresh);
            setCurrentUser(response.data.user);
            setError(null);
            return response.data;
        } catch (err) {
            setError(err.response?.data?.detail || 'Invalid username or password');
            throw err;
        } finally {
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            setLoading(true);

            // Try to call logout endpoint to invalidate tokens on server
            const refreshToken = localStorage.getItem('refresh');
            if (refreshToken) {
                try {
                    await authService.logout();
                } catch (err) {
                    // Ignore logout API errors, still clear local tokens
                    console.warn('Logout API call failed:', err);
                }
            }
        } catch (err) {
            console.error('Logout error:', err);
        } finally {
            // Always clear local state and tokens
            setCurrentUser(null);
            setError(null);
            localStorage.removeItem('access');
            localStorage.removeItem('refresh');
            setLoading(false);
        }
    };

    const isAdmin = () => {
        return currentUser?.role === 'admin';
    };

    const isManager = () => {
        return currentUser?.role === 'manager';
    };

    const isCashier = () => {
        return currentUser?.role === 'cashier';
    };

    // Function to refresh user data (useful after profile updates)
    const refreshUser = async () => {
        if (localStorage.getItem('access')) {
            await fetchCurrentUser();
        }
    };

    const permittedAdmin = () => {
        return currentUser?.role === 'admin' || currentUser?.role === 'manager';
    };

    const value = {
        currentUser,
        loading,
        error,
        login,
        logout,
        refreshUser,
        isManager,
        isCashier,
        isAdmin,
        permittedAdmin,
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
