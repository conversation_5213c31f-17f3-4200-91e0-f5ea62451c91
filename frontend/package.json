{"name": "bnpl", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@vitejs/plugin-react": "^4.0.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "globals": "^13.20.0", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.11.2", "vite": "^6.3.5", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "test": "echo 'Tests would run here. Install vitest for actual testing.'"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}