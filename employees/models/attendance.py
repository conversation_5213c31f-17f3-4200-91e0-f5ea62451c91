from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from .employee import Employee


class Attendance(TimeStampedModel):
    """
    Model to track employee attendance with check-in and check-out times.
    """

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name="attendances",
        verbose_name=_("Employee"),
    )
    time_in = models.DateTimeField(_("Time In"))
    time_out = models.DateTimeField(_("Time Out"), null=True, blank=True)
    total_hours = models.FloatField(
        _("Total Hours"), null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    time_in_image = models.ImageField(
        null=True,
        blank=True,
        max_length=500,
        verbose_name=_("Time In Image"),
        upload_to="attendance/time_in/",
    )
    time_out_image = models.ImageField(
        null=True,
        blank=True,
        max_length=500,
        verbose_name=_("Time Out Image"),
        upload_to="attendance/time_out/",
    )
    notes = models.TextField(_("Notes"), blank=True, null=True)

    class Meta:
        verbose_name = _("Attendance")
        verbose_name_plural = _("Attendances")
        ordering = ["-time_in"]

    def __str__(self):
        return f"{self.employee}- {self.time_in} to {self.time_out or '...'}"

    def save(self, *args, **kwargs):
        # Calculate total_hours if both time_in and time_out are present
        if self.time_in and self.time_out:
            delta = self.time_out - self.time_in
            self.total_hours = round(delta.total_seconds() / 3600, 2)
        super().save(*args, **kwargs)
