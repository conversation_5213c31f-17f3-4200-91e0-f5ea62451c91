# Generated by Django 5.2.1 on 2025-07-28 12:00

import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("pos", "0002_alter_possession_closing_balance_and_more"),
        ("products", "0002_alter_product_cost_alter_product_price"),
        ("purchases", "0002_purchase_paid_amount_purchase_reminder_amount_and_more"),
        ("warehouses", "0005_alter_inventorycountitem_difference_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PurchaseReturn",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Total amount of the return (calculated from items)",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0.0)],
                        verbose_name="total amount",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("cash", "Cash"),
                            ("credit", "Credit"),
                            ("card", "Card"),
                            ("bank_transfer", "Bank Transfer"),
                        ],
                        default="cash",
                        help_text="Method of payment for the return",
                        max_length=50,
                        verbose_name="payment method",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this purchase return",
                        verbose_name="notes",
                    ),
                ),
                (
                    "pos_session",
                    models.ForeignKey(
                        blank=True,
                        help_text="The POS session associated with this return (optional)",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="purchase_returns",
                        to="pos.possession",
                        verbose_name="POS session",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        help_text="The supplier to whom the items are being returned",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchase_returns",
                        to="purchases.supplier",
                        verbose_name="supplier",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse from which items are being returned",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="purchase_returns",
                        to="warehouses.warehouse",
                        verbose_name="warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "purchase return",
                "verbose_name_plural": "purchase returns",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseReturnItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="Quantity of the product being returned",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.001"))
                        ],
                        verbose_name="quantity",
                    ),
                ),
                (
                    "unit_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Cost per unit of the returned product",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="unit cost",
                    ),
                ),
                (
                    "total_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total cost (quantity × unit_cost)",
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="total cost",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this return item",
                        verbose_name="notes",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        help_text="The product being returned",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchase_return_items",
                        to="products.product",
                        verbose_name="product",
                    ),
                ),
                (
                    "purchase_return",
                    models.ForeignKey(
                        help_text="The purchase return this item belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="purchases_return.purchasereturn",
                        verbose_name="purchase return",
                    ),
                ),
            ],
            options={
                "verbose_name": "purchase return item",
                "verbose_name_plural": "purchase return items",
                "ordering": ["-created"],
            },
        ),
        migrations.AddIndex(
            model_name="purchasereturn",
            index=models.Index(
                fields=["warehouse", "-created"], name="purchases_r_warehou_e84613_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchasereturn",
            index=models.Index(
                fields=["supplier", "-created"], name="purchases_r_supplie_ac3c59_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchasereturn",
            index=models.Index(
                fields=["pos_session", "-created"],
                name="purchases_r_pos_ses_0fd017_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="purchasereturnitem",
            index=models.Index(
                fields=["purchase_return", "-created"],
                name="purchases_r_purchas_9d6c15_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="purchasereturnitem",
            index=models.Index(
                fields=["product", "-created"], name="purchases_r_product_3f3a08_idx"
            ),
        ),
    ]
