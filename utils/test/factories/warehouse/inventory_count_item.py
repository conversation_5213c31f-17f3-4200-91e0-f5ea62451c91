import random
from decimal import Decimal

from django.utils import timezone
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory, post_generation
from factory.django import DjangoModelFactory

from warehouses.models.inventory_count_item import InventoryCountItem
from utils.test.factories.warehouse.stock_item import StockItemFactory


class InventoryCountItemFactory(DjangoModelFactory):
    """
    Factory for creating InventoryCountItem instances for testing.
    """

    class Meta:
        model = InventoryCountItem
        skip_postgeneration_save = True

    inventory_count = None  # Must be provided or use a post_generation hook
    stock_item = SubFactory(StockItemFactory)
    system_quantity = LazyAttribute(
        lambda o: o.stock_item.quantity if o.stock_item else Decimal('0')
    )
    recorded_quantity = LazyAttribute(
        lambda o: o.system_quantity + Decimal(str(round(random.uniform(-10, 10), 2)))
    )
    difference = LazyAttribute(
        lambda o: o.recorded_quantity - o.system_quantity
    )
    notes = "Test inventory count item"

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure proper difference calculation"""
        # Ensure difference is calculated if not provided
        if 'recorded_quantity' in kwargs and 'system_quantity' in kwargs:
            if 'difference' not in kwargs:
                kwargs['difference'] = (
                    kwargs['recorded_quantity'] - kwargs['system_quantity']
                )
        return super()._create(model_class, *args, **kwargs)

    @post_generation
    def set_inventory_count(self, create, extracted, **kwargs):
        """
        Set the inventory count if not provided.
        Can be passed an InventoryCount instance or use the default.
        """
        if not create:
            return

        if extracted:
            # Use the provided inventory count
            self.inventory_count = extracted
        elif not self.inventory_count:
            # Create a new inventory count if none provided
            from utils.test.factories.warehouse.inventory_count import InventoryCountFactory
            self.inventory_count = InventoryCountFactory()
            
        # Ensure the stock item is from the same warehouse as the inventory count
        if hasattr(self.inventory_count, 'warehouse') and hasattr(self.stock_item, 'warehouse'):
            if self.stock_item.warehouse != self.inventory_count.warehouse:
                self.stock_item.warehouse = self.inventory_count.warehouse
                self.stock_item.save()


class NoDiscrepancyInventoryCountItemFactory(InventoryCountItemFactory):
    """Factory for creating items with no discrepancy between system and recorded quantities"""
    recorded_quantity = LazyAttribute(lambda o: o.system_quantity)
    difference = Decimal('0')


class ShortageInventoryCountItemFactory(InventoryCountItemFactory):
    """Factory for creating items with a shortage (recorded < system)"""
    recorded_quantity = LazyAttribute(
        lambda o: o.system_quantity - Decimal(str(round(random.uniform(0.1, 10), 2)))
    )
    difference = LazyAttribute(
        lambda o: o.recorded_quantity - o.system_quantity
    )


class SurplusInventoryCountItemFactory(InventoryCountItemFactory):
    """Factory for creating items with a surplus (recorded > system)"""
    recorded_quantity = LazyAttribute(
        lambda o: o.system_quantity + Decimal(str(round(random.uniform(0.1, 10), 2)))
    )
    difference = LazyAttribute(
        lambda o: o.recorded_quantity - o.system_quantity
    )