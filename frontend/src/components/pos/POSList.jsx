import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {posService} from '../../services/posService';
import {warehousesService} from '../../services/warehousesService';
import {FaEdit, FaEye, FaPlus, FaSortAmountDown, FaSortAmountUp, FaTrash} from 'react-icons/fa';
import DeletePOSModal from './DeletePOSModal';

/**
 * POSList component
 * Lists all POS terminals with action buttons
 */
const POSList = () => {
    const navigate = useNavigate();
    const [posList, setPOSList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [warehouseFilter, setWarehouseFilter] = useState('');
    const [dateFromFilter, setDateFromFilter] = useState('');
    const [dateToFilter, setDateToFilter] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize] = useState(10);
    const [startingSession, setStartingSession] = useState(null);
    const [orderBy, setOrderBy] = useState('name');

    // Warehouse list for filter dropdown
    const [warehouseList, setWarehouseList] = useState([]);
    const [warehouseListLoading, setWarehouseListLoading] = useState(false);

    // Delete modal state
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [posToDelete, setPOSToDelete] = useState(null);
    const [deleteLoading, setDeleteLoading] = useState(false);

    // Fetch POS terminals
    const fetchPOSList = async (page = 1, search = '') => {
        try {
            setLoading(true);
            setError('');

            const params = {
                page,
                page_size: pageSize,
                ordering: orderBy
            };

            if (search.trim()) {
                params.search = search.trim();
            }

            if (warehouseFilter) {
                params.warehouse = warehouseFilter;
            }

            if (dateFromFilter) {
                params.created_after = dateFromFilter;
            }

            if (dateToFilter) {
                params.created_before = dateToFilter;
            }

            const response = await posService.getPOSList(params);

            setPOSList(response.data.results || []);
            setTotalCount(response.data.count || 0);
            setTotalPages(Math.ceil((response.data.count || 0) / pageSize));

        } catch (err) {
            console.error('Error fetching POS list:', err);
            setError('Failed to load POS terminals. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Fetch warehouse list for filter dropdown
    const fetchWarehouseList = async () => {
        try {
            setWarehouseListLoading(true);
            // Assuming there's a service method to get warehouses
            // If not, you might need to create one or use a different approach
            const response = await warehousesService.getWarehouses({page_size: 100}); // Get all warehouses
            setWarehouseList(response.data.results || []);
        } catch (err) {
            console.error('Error fetching warehouse list:', err);
            // Don't show error for warehouse list fetch failure, just log it
        } finally {
            setWarehouseListLoading(false);
        }
    };

    // Handle ordering change
    const handleOrderChange = (newOrder) => {
        setOrderBy(newOrder);
        setCurrentPage(1);
        fetchPOSList(1, searchTerm);
    };

    // Get sort icon for column headers
    const getSortIcon = (field) => {
        if (orderBy === field) {
            return <FaSortAmountUp className="ms-1"/>;
        } else if (orderBy === `-${field}`) {
            return <FaSortAmountDown className="ms-1"/>;
        }
        return null;
    };

    // Handle column header click for sorting
    const handleColumnSort = (field) => {
        let newOrder;
        if (orderBy === field) {
            // Currently ascending, switch to descending
            newOrder = `-${field}`;
        } else if (orderBy === `-${field}`) {
            // Currently descending, switch to ascending
            newOrder = field;
        } else {
            // Not currently sorted by this field, default to ascending for most fields
            newOrder = field;
        }
        handleOrderChange(newOrder);
    };

    // Handle search input change
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);

        // Apply search after a short delay
        const timer = setTimeout(() => {
            setCurrentPage(1);
            fetchPOSList(1, value);
        }, 300);

        return () => clearTimeout(timer);
    };

    // Handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchPOSList(page, searchTerm);
    };

    // Handle delete POS
    const handleDeletePOS = (pos) => {
        setPOSToDelete(pos);
        setShowDeleteModal(true);
    };

    const confirmDeletePOS = async (posId) => {
        try {
            setDeleteLoading(true);
            setError('');

            await posService.deletePOS(posId);

            setShowDeleteModal(false);
            setPOSToDelete(null);

            // Refresh the list
            fetchPOSList(currentPage, searchTerm);

        } catch (err) {
            console.error('Error deleting POS:', err);
            setError('Failed to delete POS terminal. Please try again.');
        } finally {
            setDeleteLoading(false);
        }
    };

    // Initial load
    useEffect(() => {
        fetchPOSList();
        fetchWarehouseList();
    }, []);

    // Render pagination
    const renderPagination = () => {
        if (totalPages <= 1) return null;

        const items = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Previous button
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Next button
        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return <Pagination className="justify-content-center">{items}</Pagination>;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Terminals</h2>
                        <div className="d-flex gap-2">
                            <Button
                                variant="success"
                                onClick={() => navigate('/pos/create')}
                                className="d-flex align-items-center"
                            >
                                <FaPlus className="me-2"/>
                                Add POS Terminal
                            </Button>
                            <Button
                                variant="primary"
                                onClick={() => navigate('/pos/sessions')}
                                className="d-flex align-items-center"
                            >
                                <FaEye className="me-2"/>
                                View Sessions
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Search Form */}
            <Row className="mb-4">
                <Col md={12}>
                    <Form>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>Search POS Terminals</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Search by name or description..."
                                        value={searchTerm}
                                        onChange={handleSearchChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Col>
            </Row>

            {/* Filters */}
            <Row className="mb-4">
                <Col>

                    <Row>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Warehouse</Form.Label>
                                <Form.Select
                                    value={warehouseFilter}
                                    onChange={(e) => {
                                        setWarehouseFilter(e.target.value);
                                        setCurrentPage(1);
                                        fetchPOSList(1, searchTerm);
                                    }}
                                    disabled={warehouseListLoading}
                                >
                                    <option value="">All Warehouses</option>
                                    {warehouseList.map(warehouse => (
                                        <option key={warehouse.id} value={warehouse.id}>
                                            {warehouse.name}
                                        </option>
                                    ))}
                                </Form.Select>
                                {warehouseListLoading && (
                                    <Form.Text className="text-muted">
                                        Loading warehouses...
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Order By</Form.Label>
                                <Form.Select
                                    value={orderBy}
                                    onChange={(e) => handleOrderChange(e.target.value)}
                                >
                                    <option value="name">Name (A-Z)</option>
                                    <option value="-name">Name (Z-A)</option>
                                    <option value="id">ID (Low to High)</option>
                                    <option value="-id">ID (High to Low)</option>
                                    <option value="warehouse__name">Warehouse (A-Z)</option>
                                    <option value="-warehouse__name">Warehouse (Z-A)</option>
                                    <option value="created">Created (Oldest First)</option>
                                    <option value="-created">Created (Newest First)</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={3} className="d-flex align-items-end">
                            <Button
                                type="button"
                                variant="outline-secondary"
                                onClick={() => {
                                    setSearchTerm('');
                                    setWarehouseFilter('');
                                    setDateFromFilter('');
                                    setDateToFilter('');
                                    setOrderBy('name');
                                    setCurrentPage(1);
                                    fetchPOSList(1, '');
                                }}
                            >
                                Clear All Filters
                            </Button>
                        </Col>
                    </Row>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* POS List */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <div className="d-flex justify-content-between align-items-center">
                                <h5 className="mb-0">
                                    POS Terminals ({totalCount})
                                </h5>
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : posList.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted mb-0">
                                        {searchTerm ? 'No POS terminals found matching your search.' : 'No POS terminals available.'}
                                    </p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                    <tr>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('id')}
                                            title="Click to sort by ID"
                                        >
                                            ID {getSortIcon('id')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('name')}
                                            title="Click to sort by Name"
                                        >
                                            Name {getSortIcon('name')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('warehouse__name')}
                                            title="Click to sort by Warehouse"
                                        >
                                            Warehouse {getSortIcon('warehouse__name')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('description')}
                                            title="Click to sort by Description"
                                        >
                                            Description {getSortIcon('description')}
                                        </th>
                                        <th
                                            style={{cursor: 'pointer'}}
                                            onClick={() => handleColumnSort('created')}
                                            title="Click to sort by Created Date"
                                        >
                                            Created {getSortIcon('created')}
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {posList.map((pos) => (
                                        <tr key={pos.id}>
                                            <td>#{pos.id}</td>
                                            <td>
                                                <strong>{pos.name}</strong>
                                            </td>
                                            <td>{pos.warehouse?.name || 'N/A'}</td>
                                            <td>{pos.description || 'No description'}</td>
                                            <td>
                                                {pos.created ? new Date(pos.created).toLocaleDateString() : 'N/A'}
                                            </td>
                                            <td>
                                                <div className="d-flex gap-2">
                                                    <Button
                                                        variant="outline-primary"
                                                        size="sm"
                                                        onClick={() => navigate(`/pos/${pos.id}`)}
                                                        title="View Details"
                                                    >
                                                        <FaEye/>
                                                    </Button>
                                                    <Button
                                                        variant="outline-secondary"
                                                        size="sm"
                                                        onClick={() => navigate(`/pos/${pos.id}/edit`)}
                                                        title="Edit POS"
                                                    >
                                                        <FaEdit/>
                                                    </Button>
                                                    <Button
                                                        variant="outline-danger"
                                                        size="sm"
                                                        onClick={() => handleDeletePOS(pos)}
                                                        title="Delete POS"
                                                    >
                                                        <FaTrash/>
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Pagination */}
            {renderPagination()}

            {/* Delete Modal */}
            <DeletePOSModal
                show={showDeleteModal}
                onHide={() => {
                    setShowDeleteModal(false);
                    setPOSToDelete(null);
                }}
                onConfirm={confirmDeletePOS}
                pos={posToDelete}
                loading={deleteLoading}
            />
        </Container>
    );
};

export default POSList;
