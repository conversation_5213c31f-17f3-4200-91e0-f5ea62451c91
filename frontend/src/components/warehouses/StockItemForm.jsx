import React, { useState, useEffect } from 'react';
import { 
    <PERSON>, 
    <PERSON><PERSON>, 
    Card, 
    <PERSON>, 
    Spin<PERSON>, 
    Row, 
    Col,
    InputGroup
} from 'react-bootstrap';
import { useProducts } from '../../hooks/useProducts';

/**
 * Reusable form component for creating and editing stock items
 * @param {Object} props - Component props
 * @param {number} props.warehouseId - ID of the warehouse
 * @param {Object} props.initialData - Initial form data for edit mode
 * @param {Function} props.onSubmit - Form submission handler
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {boolean} props.isEdit - Whether this is edit mode
 * @param {Function} props.onCancel - Function to cancel the form
 */
const StockItemForm = ({ 
    warehouseId,
    initialData = {}, 
    onSubmit, 
    loading = false, 
    error = null,
    isEdit = false,
    onCancel
}) => {
    const [formData, setFormData] = useState({
        product: '',
        quantity: '0',
        min_stock: '0'
    });
    const [validationErrors, setValidationErrors] = useState({});
    const { products, loading: productsLoading, fetchProducts } = useProducts();

    // Initialize form data
    useEffect(() => {
        if (initialData && Object.keys(initialData).length > 0) {
            setFormData({
                product: initialData.product || '',
                quantity: initialData.quantity?.toString() || '0',
                min_stock: initialData.min_stock?.toString() || '0'
            });
        }
    }, [initialData]);

    // Fetch products on component mount
    useEffect(() => {
        fetchProducts({
            page_size: 100, // Fetch more products to ensure we have a good selection
            ordering: 'name'
        });
    }, [fetchProducts]);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.product) {
            errors.product = 'Product is required';
        }

        if (!formData.quantity || isNaN(formData.quantity)) {
            errors.quantity = 'Quantity must be a valid number';
        } else if (parseFloat(formData.quantity) < 0) {
            errors.quantity = 'Quantity cannot be negative';
        }

        if (!formData.min_stock || isNaN(formData.min_stock)) {
            errors.min_stock = 'Minimum stock must be a valid number';
        } else if (parseFloat(formData.min_stock) < 0) {
            errors.min_stock = 'Minimum stock cannot be negative';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            product: parseInt(formData.product),
            quantity: parseFloat(formData.quantity),
            min_stock: parseFloat(formData.min_stock)
        };

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Stock Item' : 'Add Stock Item'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={12}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Product <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Select
                                    name="product"
                                    value={formData.product}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.product}
                                    disabled={isEdit || productsLoading}
                                >
                                    <option value="">Select a product</option>
                                    {products.map(product => (
                                        <option key={product.id} value={product.id}>
                                            {product.name}
                                        </option>
                                    ))}
                                </Form.Select>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.product}
                                </Form.Control.Feedback>
                                {productsLoading && (
                                    <Form.Text className="text-muted">
                                        Loading products...
                                    </Form.Text>
                                )}
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Quantity <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    name="quantity"
                                    value={formData.quantity}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.quantity}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.quantity}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Minimum Stock <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    name="min_stock"
                                    value={formData.min_stock}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.min_stock}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.min_stock}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Minimum quantity before restocking is needed
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <div className="d-flex gap-2">
                        <Button 
                            variant="primary" 
                            type="submit" 
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Adding...'}
                                </>
                            ) : (
                                isEdit ? 'Update Stock Item' : 'Add Stock Item'
                            )}
                        </Button>
                        <Button 
                            variant="secondary" 
                            type="button"
                            onClick={onCancel}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default StockItemForm;