from django.contrib.auth import get_user_model
from rest_framework import serializers

from ..models.employee import Employee

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "email",
            "phone_number",
            "password",
            "first_name",
            "last_name",
            "role",
        )
        extra_kwargs = {"password": {"write_only": True}}


class EmployeeCreateSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = Employee
        fields = [
            "id",
            "address",
            "type",
            "hour_rate",
            "day_rate",
            "hire_date",
            "status",
            "pos",
            "identification",
            "user",
            "created",
            "modified",
        ]
        read_only_fields = ("id", "created", "modified")

    def create(self, validated_data):
        user_data = validated_data.pop("user")
        password = user_data.pop("password")
        # Create user
        user = User.objects.create_user(**user_data, password=password, is_active=True)

        # Create employee with the file
        employee = Employee.objects.create(user=user, **validated_data)
        return employee

    def update(self, instance, validated_data):
        user_data = validated_data.pop("user", {})
        password = user_data.pop("password", None)
        # Update user
        for attr, value in user_data.items():
            setattr(instance.user, attr, value)
        if password:
            instance.user.set_password(password)
        instance.user.save()

        # Update employee
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance
