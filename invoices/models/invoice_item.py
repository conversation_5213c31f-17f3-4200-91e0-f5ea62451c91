from django.core.validators import MinValueValidator
from django.db import models
from django_extensions.db.models import TimeStampedModel

from invoices.models.invoice import Invoice


class InvoiceItem(TimeStampedModel):
    """
    Represents an item within an invoice.
    """

    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name="items",
        help_text="The invoice this item belongs to",
    )
    product = models.ForeignKey(
        "products.Product",
        on_delete=models.PROTECT,
        related_name="invoice_items",
        help_text="The product being invoiced",
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Quantity of the product",
        default=1,
        validators=[MinValueValidator(0.01)],
    )
    unit_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Price per unit of the product",
        validators=[MinValueValidator(0.01)],
    )
    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Total price (quantity × unit_price)",
        validators=[MinValueValidator(0.01)],
    )
    profit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Profit amount for this item",
        validators=[MinValueValidator(0.01)],
    )
    notes = models.TextField(
        blank=True, null=True, help_text="Additional notes about this invoice item"
    )

    class Meta:
        verbose_name = "Invoice Item"
        verbose_name_plural = "Invoice Items"
        ordering = ["-created"]

    def __str__(self):
        return f"{self.quantity} x {self.product.name} - {self.total_price}"

    def save(self, *args, **kwargs):
        # Calculate total_price and profit before saving
        if self.pk is None:
            self.total_price = self.unit_price * self.quantity
            self.profit = (self.unit_price - self.product.cost) * self.quantity
        super().save(*args, **kwargs)
