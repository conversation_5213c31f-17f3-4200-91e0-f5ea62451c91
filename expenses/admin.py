from django.contrib import admin
from django.db import models
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import Expense, ExpenseCategory


@admin.register(ExpenseCategory)
class ExpenseCategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "expense_count", "created", "modified")
    search_fields = ("name", "description")
    list_filter = ("created", "modified")
    readonly_fields = ("created", "modified", "expense_count")
    fieldsets = (
        (None, {"fields": ("name", "description")}),
        (
            "Metadata",
            {
                "fields": ("expense_count", "created", "modified"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .annotate(_expense_count=models.Count("expenses"))
        )

    def expense_count(self, obj):
        return obj._expense_count

    expense_count.short_description = _("Number of Expenses")
    expense_count.admin_order_field = "_expense_count"


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    list_display = (
        "amount_display",
        "expense_category_link",
    )
    list_filter = ("expense_category",)
    search_fields = ("description", "expense_category__name")
    readonly_fields = (
        "created",
        "modified",
        "expense_category_link",
        "pos_session_transaction_link",
    )
    fieldsets = (
        (
            "Expense Information",
            {
                "fields": (
                    "expense_category_link",
                    "amount",
                    "description",
                )
            },
        ),
        (
            "Relations",
            {"fields": ("pos_session_transaction_link",), "classes": ("collapse",)},
        ),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )
    actions = ["approve_selected_expenses", "reject_selected_expenses", "mark_as_paid"]

    def amount_display(self, obj):
        return f"{obj.amount:.2f}"

    amount_display.short_description = _("Amount")
    amount_display.admin_order_field = "amount"

    def expense_category_link(self, obj):
        if obj.expense_category:
            url = reverse(
                "admin:expenses_expensecategory_change", args=[obj.expense_category.id]
            )
            return format_html('<a href="{}">{}</a>', url, obj.expense_category)
        return "—"

    expense_category_link.short_description = _("Category")
    expense_category_link.admin_order_field = "expense_category__name"

    def pos_session_transaction_link(self, obj):
        if obj.pos_session_transaction:
            url = reverse(
                "admin:pos_possessiontransaction_change",
                args=[obj.pos_session_transaction.id],
            )
            return format_html('<a href="{}">View Transaction</a>', url)
        return "—"

    pos_session_transaction_link.short_description = _("POS Transaction")

    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only for new objects
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related(
                "expense_category",
                "pos_session_transaction",
            )
        )
