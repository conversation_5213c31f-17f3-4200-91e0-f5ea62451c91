from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from products.models.category import Category


class CategoryFactory(DjangoModelFactory):
    """
    Factory for creating Category instances for testing.
    """

    class Meta:
        model = Category
        skip_postgeneration_save = True

    name = Faker("word")
    description = Faker("paragraph", nb_sentences=1)
    parent = None  # Default to no parent

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unique names."""
        # Generate a unique name by appending a random string
        if "name" not in kwargs:
            base_name = Faker("word").generate({})
            counter = 1
            name = base_name
            while Category.objects.filter(name=name).exists():
                name = f"{base_name} {counter}"
                counter += 1
            kwargs["name"] = name
        return super()._create(model_class, *args, **kwargs)


class SubCategoryFactory(CategoryFactory):
    """
    Factory for creating subcategories with a parent category.
    """

    parent = SubFactory(CategoryFactory)
