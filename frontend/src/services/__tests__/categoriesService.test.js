import { categoriesService } from '../categoriesService';
import api from '../api';

// Mock the api module
jest.mock('../api');

describe('categoriesService', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getCategories', () => {
        test('calls API with correct URL for basic request', () => {
            categoriesService.getCategories();
            expect(api.get).toHaveBeenCalledWith('/v1/categories/');
        });

        test('calls API with query parameters', () => {
            const params = {
                page: 2,
                page_size: 20,
                search: 'electronics',
                ordering: '-created',
                parent_id: 1
            };

            categoriesService.getCategories(params);
            
            expect(api.get).toHaveBeenCalledWith(
                '/v1/categories/?page=2&page_size=20&search=electronics&ordering=-created&parent_id=1'
            );
        });

        test('handles null parent_id parameter', () => {
            const params = { parent_id: null };
            categoriesService.getCategories(params);
            
            expect(api.get).toHaveBeenCalledWith('/v1/categories/?parent_id=null');
        });

        test('ignores undefined parameters', () => {
            const params = {
                page: 1,
                search: undefined,
                ordering: 'name'
            };

            categoriesService.getCategories(params);
            
            expect(api.get).toHaveBeenCalledWith('/v1/categories/?page=1&ordering=name');
        });
    });

    describe('getCategory', () => {
        test('calls API with correct URL', () => {
            categoriesService.getCategory(123);
            expect(api.get).toHaveBeenCalledWith('/v1/categories/123/');
        });
    });

    describe('createCategory', () => {
        test('calls API with correct URL and data', () => {
            const categoryData = {
                name: 'New Category',
                description: 'Category description',
                parent_id: 1
            };

            categoriesService.createCategory(categoryData);
            
            expect(api.post).toHaveBeenCalledWith('/v1/categories/', categoryData);
        });
    });

    describe('updateCategory', () => {
        test('calls API with correct URL and data', () => {
            const categoryData = {
                name: 'Updated Category',
                description: 'Updated description'
            };

            categoriesService.updateCategory(123, categoryData);
            
            expect(api.put).toHaveBeenCalledWith('/v1/categories/123/', categoryData);
        });
    });

    describe('deleteCategory', () => {
        test('calls API with correct URL', () => {
            categoriesService.deleteCategory(123);
            expect(api.delete).toHaveBeenCalledWith('/v1/categories/123/');
        });
    });

    describe('getParentCategories', () => {
        test('calls API with correct URL for parent categories', () => {
            categoriesService.getParentCategories();
            expect(api.get).toHaveBeenCalledWith('/v1/categories/?parent_id=null');
        });
    });

    describe('getSubcategories', () => {
        test('calls API with correct URL for subcategories', () => {
            categoriesService.getSubcategories(123);
            expect(api.get).toHaveBeenCalledWith('/v1/categories/?parent_id=123');
        });
    });
});
