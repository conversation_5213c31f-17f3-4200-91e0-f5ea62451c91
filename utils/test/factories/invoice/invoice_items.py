import factory
import random
from decimal import Decimal
from factory import SubFactory, LazyAttribute
from factory.django import DjangoModelFactory

from invoices.models.invoice_item import InvoiceItem
from utils.test.factories.product.product import ProductFactory


class InvoiceItemFactory(DjangoModelFactory):
    """
    Factory for creating InvoiceItem instances for testing.
    """
    class Meta:
        model = InvoiceItem
        skip_postgeneration_save = True

    # Invoice will be set when creating items through InvoiceFactory
    # or can be specified directly when creating standalone items
    invoice = None
    
    product = SubFactory(ProductFactory)
    
    # Generate a random quantity between 1 and 10
    quantity = LazyAttribute(lambda _: Decimal(random.uniform(1, 10)).quantize(Decimal('0.01')))
    
    # Use the product's price as the unit price
    unit_price = LazyAttribute(lambda o: o.product.price)
    
    # Calculate total_price based on quantity and unit_price
    total_price = LazyAttribute(lambda o: o.quantity * o.unit_price)
    
    notes = factory.Faker('sentence')
    
    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override _create to ensure unit_price is not less than product cost."""
        if 'unit_price' not in kwargs and 'product' in kwargs:
            # Set unit_price to product price if not specified
            kwargs['unit_price'] = kwargs['product'].price
            
        # Calculate total_price if not specified
        if 'total_price' not in kwargs and 'quantity' in kwargs and 'unit_price' in kwargs:
            kwargs['total_price'] = kwargs['quantity'] * kwargs['unit_price']
            
        return super()._create(model_class, *args, **kwargs)