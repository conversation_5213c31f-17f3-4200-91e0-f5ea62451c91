from django.urls import reverse
from rest_framework import status

from products.models.category import Category
from utils.test.base_test import BaseTestCase
from utils.test.factories.product.category import CategoryFactory, SubCategoryFactory
from utils.test.factories.product.product import ProductFactory


class CategoryViewSetTestCase(BaseTestCase):
    """Test cases for CategoryViewSet"""

    def setUp(self):
        super().setUp()
        self.list_url = reverse("products:category-list")
        self.detail_url_name = "products:category-detail"

        # Create test categories using factories
        self.parent_category = CategoryFactory()
        self.child_category = SubCategoryFactory(parent=self.parent_category)
        self.detail_url = reverse(
            self.detail_url_name, kwargs={"pk": self.parent_category.pk}
        )

    def test_list_categories_unauthenticated(self):
        """Test that unauthenticated users can list categories"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_categories_authenticated(self):
        """Test that authenticated users can list categories"""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)

    def test_filter_categories_by_parent(self):
        """Test filtering categories by parent_id"""
        response = self.cashier_client.get(
            self.list_url, {"parent_id": self.parent_category.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], self.child_category.name)

    def test_retrieve_category(self):
        """Test retrieving a single category"""
        response = self.cashier_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.parent_category.name)

    def test_create_category_unauthenticated(self):
        """Test that unauthenticated users cannot create categories"""
        data = {"name": "New Category"}
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_category_authenticated_unauthorized(self):
        """Test that non-admin users cannot create categories"""
        data = {"name": "New Category"}
        response = self.cashier_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.manager_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_category_admin(self):
        """Test that admin users can create categories"""
        data = {
            "name": "New Admin Category",
            "description": "Test admin created category",
        }
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Category.objects.count(), 3)
        self.assertEqual(response.data["name"], data["name"])

    def test_update_category_admin(self):
        """Test that admin users can update categories"""
        data = {"name": "Updated Category Name"}
        response = self.admin_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.parent_category.refresh_from_db()
        self.assertEqual(self.parent_category.name, data["name"])

    def test_update_category_manager_cashier(self):
        """Test that manager and cashier users can not update categories"""
        data = {"name": "Updated Category Name"}
        response = self.cashier_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.manager_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_category_with_products(self):
        """Test that categories with products cannot be deleted"""
        # Create a product in the category using factory
        self.product = ProductFactory(
            name="Test Product", category=self.parent_category, price=100, cost=50
        )

        response = self.admin_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            "Cannot delete category with associated products. Please remove or reassign products first.",
            response.data["detail"],
        )

    def test_delete_category_with_subcategories(self):
        """Test that categories with subcategories cannot be deleted"""
        response = self.admin_client.delete(
            reverse(self.detail_url_name, kwargs={"pk": self.parent_category.pk})
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            "Cannot delete category with subcategories. Please delete or reassign subcategories first.",
            response.data["detail"],
        )

    def test_delete_category_admin(self):
        """Test that admin users can delete categories"""
        # First, delete the child category
        child_url = reverse(self.detail_url_name, kwargs={"pk": self.child_category.pk})
        response = self.admin_client.delete(child_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Then delete the parent category
        response = self.admin_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Category.objects.count(), 0)

    def test_delete_category_manager_cashier(self):
        """Test that manager or cashier users can not delete categories"""
        child_url = reverse(self.detail_url_name, kwargs={"pk": self.child_category.pk})
        response = self.cashier_client.delete(child_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.manager_client.delete(child_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
