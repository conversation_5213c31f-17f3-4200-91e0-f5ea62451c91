from decimal import Decimal

from django.urls import reverse
from rest_framework import status

from pos.models import POSSession, POSSessionTransaction, TransactionType
from utils.test.base_test import BaseTestCase
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.pos.pos_session_transaction import (
    POSSessionTransactionFactory,
)


class POSSessionViewSetTestCase(BaseTestCase):
    """
    Test cases for POSSessionViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()
        # Create test sessions
        # Open session for manager
        self.manager_session = POSSessionFactory.create(
            pos=self.pos,
            user=self.manager_user,
            status=POSSession.Status.OPEN,
            opening_balance=Decimal("1000.00"),
        )

        # Closed session for manager
        self.manager_closed_session = POSSessionFactory.create(
            pos=self.pos,
            user=self.manager_user,
            status=POSSession.Status.CLOSED,
            opening_balance=Decimal("1000.00"),
            closing_balance=Decimal("1200.00"),
        )

        # Suspended session for cashier
        self.cashier_session = POSSessionFactory.create(
            pos=self.pos,
            user=self.cashier_user,
            status=POSSession.Status.CLOSED,
            opening_balance=Decimal("500.00"),
        )

        # URL for the POS session list
        self.list_url = reverse("pos:pos-session-list")

    # Helper methods
    def get_detail_url(self, session_id):
        return reverse("pos:pos-session-detail", args=[session_id])

    def get_close_session_url(self, session_id):
        return reverse("pos:pos-session-close-session", args=[session_id])

    def get_suspend_session_url(self, session_id):
        return reverse("pos:pos-session-suspend-session", args=[session_id])

    def get_resume_session_url(self, session_id):
        return reverse("pos:pos-session-resume-session", args=[session_id])

    def get_transactions_url(self, session_id):
        return reverse("pos:pos-session-transactions", args=[session_id])

    def get_create_transactions_url(self, session_id):
        return reverse("pos:pos-session-create-transactions", args=[session_id])

    # Test cases for list action
    def test_list_sessions_as_admin(self):
        """Admin should see all sessions."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)  # All 3 sessions

    def test_list_sessions_as_manager(self):
        """Manager should only see their sessions and open/suspended sessions."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should see both manager's sessions (open and closed) and cashier's suspended session
        self.assertEqual(len(response.data["results"]), 1)
        session_ids = [s["id"] for s in response.data["results"]]
        self.assertIn(self.manager_session.id, session_ids)

    def test_list_sessions_as_cashier(self):
        """Cashier should only see their own sessions."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.manager_session.id)

    def test_list_sessions_unauthenticated(self):
        """Unauthenticated users should not be able to list sessions."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Test cases for retrieve action
    def test_retrieve_session_as_admin(self):
        """Admin should be able to retrieve any session."""
        url = self.get_detail_url(self.manager_closed_session.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.manager_closed_session.id)

    def test_retrieve_session_as_manager(self):
        """Manager should be able to retrieve their own session."""
        url = self.get_detail_url(self.manager_session.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.manager_session.id)

    def test_retrieve_closed_session_as_manager(self):
        """Manager should be not able to retrieve their own closed session."""
        url = self.get_detail_url(self.manager_closed_session.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Test cases for close_session action
    def test_close_session_as_manager(self):
        """Manager should be able to close their open session."""
        url = self.get_close_session_url(self.manager_session.id)
        data = {"closing_balance": "1200.00"}
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.manager_session.refresh_from_db()
        self.assertEqual(self.manager_session.status, POSSession.Status.CLOSED)
        self.assertEqual(
            self.manager_session.difference,
            self.manager_session.calculated_balance
            - self.manager_session.closing_balance,
        )

    def test_close_session_as_unauthorized_user(self):
        """Admin should not be able to close manager's session."""
        url = self.get_close_session_url(self.manager_session.id)
        data = {"closing_balance": "1200.00"}
        response = self.admin_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_close_session_as_unauthenticated(self):
        url = self.get_close_session_url(self.manager_session.id)
        data = {"closing_balance": "1200.00"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_close_already_closed_session(self):
        """Should not be able to close an already closed session."""
        url = self.get_close_session_url(self.manager_closed_session.id)
        data = {"closing_balance": "1200.00"}
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Test cases for suspend_session action
    def test_suspend_session_as_manager(self):
        """Manager should be able to suspend their open session."""
        url = self.get_suspend_session_url(self.manager_session.id)
        response = self.manager_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.manager_session.refresh_from_db()
        self.assertEqual(self.manager_session.status, POSSession.Status.SUSPENDED)

    def test_suspend_as_unauthorized_user(self):
        url = self.get_suspend_session_url(self.cashier_session.id)
        response = self.admin_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_suspend_as_unauthenticated(self):
        url = self.get_suspend_session_url(self.cashier_session.id)
        response = self.client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_suspend_suspended_session(self):
        """Should not be able to suspend an already suspended session."""
        self.manager_session.status = POSSession.Status.SUSPENDED
        self.manager_session.save()
        url = self.get_suspend_session_url(self.manager_session.id)
        response = self.cashier_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Test cases for resume_session action
    def test_resume_session_as_manager(self):
        """Cashier should be able to resume their suspended session."""
        self.manager_session.status = POSSession.Status.SUSPENDED
        self.manager_session.save()
        url = self.get_resume_session_url(self.manager_session.id)
        response = self.cashier_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.manager_session.refresh_from_db()
        self.assertEqual(self.manager_session.status, POSSession.Status.OPEN)

    def test_resume_session_as_unauthorized_user(self):
        self.manager_session.status = POSSession.Status.SUSPENDED
        self.manager_session.save()
        url = self.get_resume_session_url(self.manager_session.id)
        response = self.admin_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_resume_session_as_unauthenticated(self):
        self.manager_session.status = POSSession.Status.SUSPENDED
        self.manager_session.save()
        url = self.get_resume_session_url(self.manager_session.id)
        response = self.client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_resume_session_already_opened(self):
        """Cashier should be able to resume their suspended session."""
        url = self.get_resume_session_url(self.manager_session.id)
        response = self.cashier_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_resume_open_session(self):
        """Should not be able to resume an open session."""
        url = self.get_resume_session_url(self.manager_session.id)
        response = self.manager_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Test cases for transactions action
    def test_list_transactions_as_admin(self):
        """Admin should be able to view transactions for any session."""
        # Create some test transactions
        POSSessionTransactionFactory.create_batch(
            3,
            session=self.manager_session,
            amount=Decimal("100.00"),
            transaction_type=TransactionType.SALE,
        )

        url = self.get_transactions_url(self.manager_session.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_list_transactions_unauthorized(self):
        """Cashier should not be able to view transactions for manager's session."""
        url = self.get_transactions_url(self.manager_session.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        url = self.get_transactions_url(self.manager_session.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # Test cases for create_transaction action
    def test_create_transaction_as_manager(self):
        """Manager should be able to create a transaction in their open session."""
        url = self.get_create_transactions_url(self.manager_session.id)
        data = {
            "transaction_type": "cash_out",
            "amount": "50.00",
            "description": "Test expense",
        }
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(
            POSSessionTransaction.objects.filter(
                session=self.manager_session, amount=Decimal("50.00")
            ).exists()
        )

    def test_create_transaction_closed_session(self):
        """Should not be able to create a transaction in a closed session."""
        url = self.get_create_transactions_url(self.manager_closed_session.id)
        data = {
            "transaction_type": "sale",
            "amount": "100.00",
            "description": "Test sale",
        }
        response = self.manager_client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_transaction_unauthenticated(self):
        url = self.get_create_transactions_url(self.manager_session.id)
        data = {
            "transaction_type": "sale",
            "amount": "100.00",
            "description": "Unauthorized sale",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
