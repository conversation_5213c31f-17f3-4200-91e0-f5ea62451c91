from django.contrib import admin
from django.db.models.aggregates import Count
from django.utils.translation import gettext_lazy as _

from .models.category import Category
from .models.product import Product


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "parent",
        "product_count",
        "created",
        "modified",
    )
    list_display_links = ("name",)
    search_fields = ("name", "description")
    list_filter = ("created", "modified", "parent")
    prepopulated_fields = {"slug": ("name",)} if hasattr(Category, "slug") else {}
    readonly_fields = ("created", "modified")
    fieldsets = (
        (None, {"fields": ("name", "parent", "description")}),
        ("Metadata", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(_product_count=Count("products"))

    def product_count(self, obj):
        return obj._product_count

    product_count.short_description = _("Products")
    product_count.admin_order_field = "_product_count"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "category",
        "price",
        "cost",
        "profit_margin_display",
        "unit_type",
        "barcode",
    )
    list_filter = ("category", "unit_type", "created", "modified")
    search_fields = ("name", "description", "barcode")
    list_editable = ("price", "cost")
    readonly_fields = ("created", "modified", "profit_margin_display")
    filter_horizontal = ("categories",) if hasattr(Product, "categories") else ()
    fieldsets = (
        (
            _("Basic Information"),
            {
                "fields": (
                    "name",
                    "description",
                    "category",
                    "barcode",
                    "unit_type",
                )
            },
        ),
        (_("Pricing"), {"fields": ("price", "cost", "profit_margin_display")}),
        (_("Images"), {"fields": ("image",)}),
        (_("Metadata"), {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def profit_margin_display(self, obj):
        return f"{obj.profit_margin:.2f}%"

    profit_margin_display.short_description = _("Profit Margin")
    profit_margin_display.admin_order_field = "profit_margin"

    def save_model(self, request, obj, form, change):
        # Ensure price is not less than cost
        if obj.price < obj.cost:
            obj.price = obj.cost
        super().save_model(request, obj, form, change)
