import random
from decimal import Decimal

import factory
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory
from django.contrib.contenttypes.models import ContentType

from accounts.models import AccountTransaction, TransactionType


class AccountTransactionFactory(DjangoModelFactory):
    """
    Factory for creating AccountTransaction instances for testing.
    """

    class Meta:
        model = AccountTransaction
        skip_postgeneration_save = True

    # Balance will be set when creating transactions through BalanceFactory
    # or can be specified directly when creating standalone transactions
    balance = None
    
    # Random transaction type
    type = factory.LazyAttribute(
        lambda _: random.choice([TransactionType.DEBIT, TransactionType.CREDIT])
    )

    # Generate random amount between 10 and 500
    amount = LazyAttribute(
        lambda _: Decimal(random.uniform(10, 500)).quantize(Decimal("0.01"))
    )

    description = factory.Faker("paragraph", nb_sentences=1)

    # Generic relation fields are left empty by default
    related_content_type = None
    related_object_id = None

    @classmethod
    def create_credit(cls, **kwargs):
        """Create a credit transaction."""
        return cls.create(type=TransactionType.CREDIT, **kwargs)

    @classmethod
    def create_debit(cls, **kwargs):
        """Create a debit transaction."""
        return cls.create(type=TransactionType.DEBIT, **kwargs)

    @classmethod
    def create_for_related_object(cls, related_object, **kwargs):
        """
        Create a transaction related to a specific object.
        
        Usage:
            warehouse = WarehouseFactory.create()
            transaction = AccountTransactionFactory.create_for_related_object(warehouse)
        """
        content_type = ContentType.objects.get_for_model(related_object)
        return cls.create(
            related_content_type=content_type,
            related_object_id=related_object.id,
            **kwargs
        )
