from django.db import models
from django_extensions.db.models import TimeStampedModel


class ExpenseCategory(TimeStampedModel):
    """
    Model to represent different categories for expenses.
    """

    name = models.CharField(
        max_length=100, unique=True, help_text="Name of the expense category"
    )
    description = models.TextField(
        blank=True, null=True, help_text="Description of the expense category"
    )

    class Meta:
        verbose_name = "Expense Category"
        verbose_name_plural = "Expense Categories"
        ordering = ["name"]

    def __str__(self):
        return self.name
