# Session Expiration Fixes

## Problem
The user session was expiring on every page refresh, causing users to be logged out constantly.

## Root Causes Identified

### 1. **Incorrect API Endpoints**
- **Issue**: Frontend was calling `/auth/refresh/` but backend expects `/auth/token/refresh/`
- **Issue**: Front<PERSON> was calling `/auth/me/` which doesn't exist in the backend
- **Fix**: Updated API service to use correct endpoints

### 2. **Missing Token Refresh Logic**
- **Issue**: No automatic token refresh when access token expires
- **Issue**: No response interceptor to handle 401 errors
- **Fix**: Added comprehensive token refresh interceptor

### 3. **Incomplete Token Management**
- **Issue**: Not handling token rotation (backend rotates refresh tokens)
- **Issue**: Not validating token expiration before use
- **Fix**: Added token expiration checking and proper rotation handling

### 4. **Improper User Data Fetching**
- **Issue**: Trying to fetch user data from non-existent `/auth/me/` endpoint
- **Fix**: Use refresh token response which includes user data

## Solutions Implemented

### 1. **Fixed API Service (`frontend/src/services/api.js`)**

#### **Correct Endpoints**
```javascript
// Before (incorrect)
refresh: () => api.post('/auth/refresh/')
getCurrentUser: () => api.get('/auth/me/')

// After (correct)
refresh: (refreshToken) => api.post('/auth/token/refresh/', { refresh: refreshToken })
getCurrentUser: () => {
    // Use refresh endpoint since /auth/me/ doesn't exist
    const refreshToken = localStorage.getItem('refresh');
    return api.post('/auth/token/refresh/', { refresh: refreshToken });
}
```

#### **Added Response Interceptor**
- Automatically handles 401 errors
- Prevents multiple simultaneous refresh attempts
- Queues failed requests during token refresh
- Automatically retries failed requests with new token
- Redirects to login if refresh fails

#### **Token Rotation Support**
- Handles new refresh tokens from backend
- Updates both access and refresh tokens in localStorage
- Supports JWT token rotation as configured in Django

### 2. **Enhanced AuthContext (`frontend/src/contexts/AuthContext.jsx`)**

#### **Token Expiration Checking**
```javascript
const isTokenExpired = (token) => {
    if (!token) return true;
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Date.now() / 1000;
        // Check if token expires within the next 5 minutes
        return payload.exp < (currentTime + 300);
    } catch (error) {
        return true;
    }
};
```

#### **Improved Initialization**
- Checks token expiration on app startup
- Automatically refreshes expired tokens
- Handles user data from refresh response
- Graceful fallback if refresh fails

#### **Better Error Handling**
- Distinguishes between network errors and auth errors
- Provides user-friendly error messages
- Automatic cleanup on auth failures

### 3. **Added Vite Proxy Configuration**
```javascript
// frontend/vite.config.js
server: {
    proxy: {
        '/api': {
            target: 'http://localhost:8000',
            changeOrigin: true,
            secure: false,
        }
    }
}
```

### 4. **Created Debug Component**
- **Path**: `frontend/src/components/auth/AuthDebug.jsx`
- **Route**: `/auth-debug`
- **Features**:
  - Shows current user state
  - Displays token information and expiration
  - Provides manual refresh and logout buttons
  - Includes test login form
  - Debug actions for troubleshooting

## Backend JWT Configuration

The backend is configured with these JWT settings:
```python
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),  # 1 hour
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),     # 1 day
    "ROTATE_REFRESH_TOKENS": True,                   # New refresh token on each refresh
    "BLACKLIST_AFTER_ROTATION": True,               # Blacklist old refresh tokens
    "UPDATE_LAST_LOGIN": True,
}
```

## How It Works Now

### 1. **Initial Login**
1. User logs in with username/password
2. Backend returns access token, refresh token, and user data
3. Frontend stores tokens and user data
4. User is authenticated

### 2. **Token Refresh Flow**
1. When access token expires (or is about to expire)
2. Frontend automatically calls `/auth/token/refresh/` with refresh token
3. Backend returns new access token, new refresh token, and user data
4. Frontend updates stored tokens and user data
5. Original request is retried with new access token

### 3. **Page Refresh**
1. App checks if stored access token exists
2. If token is expired, automatically refreshes it
3. If refresh succeeds, user stays logged in
4. If refresh fails, user is redirected to login

### 4. **API Request Flow**
1. Request interceptor adds access token to headers
2. If API returns 401, response interceptor triggers refresh
3. After successful refresh, original request is retried
4. If refresh fails, user is logged out

## Testing the Fix

### 1. **Access Debug Page**
Visit `/auth-debug` to see:
- Current authentication state
- Token information and expiration times
- Manual testing controls

### 2. **Test Scenarios**
1. **Login** → Should work and show user data
2. **Refresh page** → Should maintain login state
3. **Wait for token expiration** → Should auto-refresh
4. **API calls with expired token** → Should auto-refresh and retry
5. **Invalid refresh token** → Should logout and redirect

### 3. **Console Debugging**
Check browser console for:
- Token refresh attempts
- API call retries
- Error messages

## Key Benefits

1. **Seamless User Experience**: No unexpected logouts
2. **Automatic Token Management**: No manual intervention needed
3. **Robust Error Handling**: Graceful degradation on failures
4. **Security**: Proper token rotation and validation
5. **Performance**: Efficient request queuing during refresh

## Troubleshooting

If session issues persist:

1. **Check Network Tab**: Look for 401 responses and refresh attempts
2. **Check Console**: Look for authentication errors
3. **Use Debug Page**: Visit `/auth-debug` for detailed state
4. **Clear Storage**: Use "Clear All Storage" button to reset
5. **Check Backend**: Ensure Django server is running and accessible

The authentication system should now maintain sessions properly across page refreshes and handle token expiration gracefully.
