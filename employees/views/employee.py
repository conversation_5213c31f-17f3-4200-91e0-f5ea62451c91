from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON>end
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly

from ..models import Employee
from ..serializers import EmployeeCreateSerializer


class EmployeeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing employees with CRUD operations.
    """

    queryset = Employee.objects.all()
    serializer_class = EmployeeCreateSerializer
    permission_classes = [IsAdminOnly]
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["day_rate", "hour_rate", "pos"]
    search_fields = ["address", "user__first_name", "user__last_name"]
    ordering_fields = ["user__first_name", "day_rate", "hire_date"]
    ordering = ["user__first_name"]
