from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from .employee import Employee


class Salary(TimeStampedModel):
    """
    Model to track employee salaries and working hours.
    """

    employee = models.ForeignKey(
        Employee,
        on_delete=models.PROTECT,
        related_name="salaries",
        verbose_name=_("Employee"),
    )
    attendance = models.OneToOneField(
        "employees.Attendance",
        on_delete=models.PROTECT,
        related_name="salary",
        verbose_name=_("Attendance"),
    )
    salary = models.DecimalField(
        _("Salary"),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    notes = models.TextField(_("Notes"), blank=True, null=True)

    class Meta:
        verbose_name = _("Salary")
        verbose_name_plural = _("Salaries")
        ordering = ["-created"]

    def __str__(self):
        return f"{self.employee} - {self.salary} - {self.created.date()}"
