# Generated by Django 5.2.1 on 2025-07-23 09:33

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0003_account_account_type_account_name"),
        ("pos", "0002_alter_possession_closing_balance_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="accounttransaction",
            name="account",
        ),
        migrations.CreateModel(
            name="Balance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="The amount of the balance",
                        max_digits=15,
                        verbose_name="amount",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this balance record",
                        null=True,
                        verbose_name="notes",
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        help_text="The account this balance is associated with",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="balances",
                        to="accounts.account",
                        verbose_name="account",
                    ),
                ),
                (
                    "pos",
                    models.ForeignKey(
                        blank=True,
                        help_text="The POS this balance is associated with",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="balances",
                        to="pos.pos",
                        verbose_name="POS",
                    ),
                ),
            ],
            options={
                "verbose_name": "balance",
                "verbose_name_plural": "balances",
                "ordering": ["-created"],
                "unique_together": {("account", "pos")},
            },
        ),
        migrations.AddField(
            model_name="accounttransaction",
            name="balance",
            field=models.ForeignKey(
                default=1,
                help_text="The balance record of the account this transaction belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transactions",
                to="accounts.balance",
            ),
            preserve_default=False,
        ),
    ]
