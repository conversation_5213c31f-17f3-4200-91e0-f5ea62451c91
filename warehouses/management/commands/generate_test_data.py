import random

from django.core.management.base import BaseCommand
from faker import Faker

# Import your models
from products.models import Category, Product
from warehouses.models import StockItem, Warehouse


class Command(BaseCommand):
    help = "Generate random test data for products, categories, warehouses, and stock items"

    def add_arguments(self, parser):
        parser.add_argument(
            "--categories",
            type=int,
            default=10,
            help="Number of categories to create (default: 5)",
        )
        parser.add_argument(
            "--products",
            type=int,
            default=200,
            help="Number of products to create (default: 20)",
        )
        parser.add_argument(
            "--warehouses",
            type=int,
            default=3,
            help="Number of warehouses to create (default: 3)",
        )
        parser.add_argument(
            "--min-stock",
            type=int,
            default=0,
            help="Minimum stock quantity (default: 0)",
        )
        parser.add_argument(
            "--max-stock",
            type=int,
            default=100,
            help="Maximum stock quantity (default: 100)",
        )

    def handle(self, *args, **options):
        fake = Faker()

        # Get or create categories
        categories = []
        self.stdout.write(f"Creating {options['categories']} categories...")
        for _ in range(options["categories"]):
            name = fake.unique.word().capitalize()
            category, created = Category.objects.get_or_create(
                name=name,
                defaults={
                    "description": fake.sentence(),
                },
            )
            categories.append(category)
            self.stdout.write(f"  - Created category: {name}")

        # Get or create warehouses
        warehouses = []
        self.stdout.write(f"\nCreating {options['warehouses']} warehouses...")
        for i in range(options["warehouses"]):
            name = f"Warehouse {i+1}"
            warehouse, created = Warehouse.objects.get_or_create(
                name=name,
                defaults={
                    "name": f"WH{i+1:03d}",
                    "location": fake.address(),
                },
            )
            warehouses.append(warehouse)
            self.stdout.write(f"  - Created warehouse: {name}")

        # Create products
        self.stdout.write(f"\nCreating {options['products']} products...")
        products = []
        for i in range(options["products"]):
            name = fake.unique.catch_phrase()
            cost = round(random.uniform(1, 1000), 2)
            price = round(cost * random.uniform(1.1, 2.5), 2)

            product = Product.objects.create(
                name=name,
                description=fake.paragraph(),
                barcode=f"{random.randint(100000000000, 999999999999)}",
                category=random.choice(categories),
                unit_type=random.choice(["piece", "kg", "g", "l", "ml"]),
                cost=cost,
                price=price,
            )
            products.append(product)
            self.stdout.write(f"  - Created product: {name}")

        # Create stock items
        self.stdout.write("\nCreating stock items...")
        for product in products:
            for warehouse in warehouses:
                quantity = random.randint(options["min_stock"], options["max_stock"])
                stock_item, created = StockItem.objects.get_or_create(
                    product=product,
                    warehouse=warehouse,
                    defaults={
                        "quantity": quantity,
                        "min_stock": max(0, quantity - random.randint(5, 20)),
                    },
                )

                if created:
                    self.stdout.write(
                        f"  - Created stock for {product.name} in {warehouse.name}: {quantity} units"
                    )

        self.stdout.write("\nTest data generation completed successfully!")
